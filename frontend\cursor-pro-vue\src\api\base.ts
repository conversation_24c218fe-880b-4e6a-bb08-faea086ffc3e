/**
 * 基础API类 - 现代化API架构
 * 提供统一的请求处理、拦截器、错误处理
 */

// API响应基础类型
export interface ApiResponse<T = any> {
  success: boolean
  data: T
  error?: string
  message?: string
  timestamp?: number
}

// 请求配置接口
export interface RequestConfig {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  url: string
  data?: any
  params?: Record<string, any>
  headers?: Record<string, string>
  timeout?: number
}

// 错误类型枚举
export enum ApiErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  CLIENT_ERROR = 'CLIENT_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR'
}

// API错误类
export class ApiError extends Error {
  public readonly type: ApiErrorType
  public readonly status?: number
  public readonly response?: any

  constructor(
    message: string,
    type: ApiErrorType,
    status?: number,
    response?: any
  ) {
    super(message)
    this.name = 'ApiError'
    this.type = type
    this.status = status
    this.response = response
  }
}

// 请求拦截器类型
export type RequestInterceptor = (config: RequestConfig) => RequestConfig | Promise<RequestConfig>

// 响应拦截器类型
export type ResponseInterceptor<T = any> = (response: ApiResponse<T>) => ApiResponse<T> | Promise<ApiResponse<T>>

// 错误拦截器类型
export type ErrorInterceptor = (error: ApiError) => ApiError | Promise<ApiError>

/**
 * 基础API类
 * 提供统一的HTTP请求处理能力
 */
export class BaseAPI {
  private baseURL: string
  private defaultTimeout: number
  private requestInterceptors: RequestInterceptor[] = []
  private responseInterceptors: ResponseInterceptor[] = []
  private errorInterceptors: ErrorInterceptor[] = []

  constructor(baseURL: string = 'http://localhost:8080', timeout: number = 30000) {
    this.baseURL = baseURL
    this.defaultTimeout = timeout
  }

  /**
   * 添加请求拦截器
   */
  addRequestInterceptor(interceptor: RequestInterceptor): void {
    this.requestInterceptors.push(interceptor)
  }

  /**
   * 添加响应拦截器
   */
  addResponseInterceptor(interceptor: ResponseInterceptor): void {
    this.responseInterceptors.push(interceptor)
  }

  /**
   * 添加错误拦截器
   */
  addErrorInterceptor(interceptor: ErrorInterceptor): void {
    this.errorInterceptors.push(interceptor)
  }

  /**
   * 执行请求拦截器
   */
  private async executeRequestInterceptors(config: RequestConfig): Promise<RequestConfig> {
    let processedConfig = config
    for (const interceptor of this.requestInterceptors) {
      processedConfig = await interceptor(processedConfig)
    }
    return processedConfig
  }

  /**
   * 执行响应拦截器
   */
  private async executeResponseInterceptors<T>(response: ApiResponse<T>): Promise<ApiResponse<T>> {
    let processedResponse = response
    for (const interceptor of this.responseInterceptors) {
      processedResponse = await interceptor(processedResponse)
    }
    return processedResponse
  }

  /**
   * 执行错误拦截器
   */
  private async executeErrorInterceptors(error: ApiError): Promise<ApiError> {
    let processedError = error
    for (const interceptor of this.errorInterceptors) {
      processedError = await interceptor(processedError)
    }
    return processedError
  }

  /**
   * 构建完整URL
   */
  private buildURL(url: string, params?: Record<string, any>): string {
    const fullURL = url.startsWith('http') ? url : `${this.baseURL}/api/${url}`
    
    if (!params || Object.keys(params).length === 0) {
      return fullURL
    }

    const urlParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        urlParams.append(key, String(value))
      }
    })

    return `${fullURL}?${urlParams.toString()}`
  }

  /**
   * 处理HTTP错误
   */
  private handleHttpError(response: Response): ApiError {
    const status = response.status

    if (status >= 400 && status < 500) {
      if (status === 401) {
        return new ApiError('认证失败', ApiErrorType.AUTHENTICATION_ERROR, status)
      } else if (status === 403) {
        return new ApiError('权限不足', ApiErrorType.AUTHORIZATION_ERROR, status)
      } else if (status === 422) {
        return new ApiError('数据验证失败', ApiErrorType.VALIDATION_ERROR, status)
      } else {
        return new ApiError('客户端请求错误', ApiErrorType.CLIENT_ERROR, status)
      }
    } else if (status >= 500) {
      return new ApiError('服务器内部错误', ApiErrorType.SERVER_ERROR, status)
    } else {
      return new ApiError(`HTTP错误: ${status}`, ApiErrorType.NETWORK_ERROR, status)
    }
  }

  /**
   * 核心请求方法
   */
  protected async request<T = any>(config: RequestConfig): Promise<ApiResponse<T>> {
    try {
      // 执行请求拦截器
      const processedConfig = await this.executeRequestInterceptors(config)

      // 构建请求选项
      const requestOptions: RequestInit = {
        method: processedConfig.method,
        headers: {
          'Content-Type': 'application/json',
          ...processedConfig.headers
        },
        signal: AbortSignal.timeout(processedConfig.timeout || this.defaultTimeout)
      }

      // 添加请求体
      if (processedConfig.data && ['POST', 'PUT', 'PATCH'].includes(processedConfig.method)) {
        requestOptions.body = JSON.stringify(processedConfig.data)
      }

      // 构建URL
      const url = this.buildURL(processedConfig.url, processedConfig.params)

      // 发送请求
      const response = await fetch(url, requestOptions)

      // 检查HTTP状态
      if (!response.ok) {
        throw this.handleHttpError(response)
      }

      // 解析响应
      const responseData = await response.json()

      // 构建API响应
      const apiResponse: ApiResponse<T> = {
        success: responseData.success !== false,
        data: responseData.data !== undefined ? responseData.data : responseData,
        error: responseData.error,
        message: responseData.message,
        timestamp: Date.now()
      }

      // 检查业务逻辑错误
      if (apiResponse.success === false) {
        throw new ApiError(
          apiResponse.error || '业务逻辑错误',
          ApiErrorType.SERVER_ERROR,
          response.status,
          apiResponse
        )
      }

      // 执行响应拦截器
      return await this.executeResponseInterceptors(apiResponse)

    } catch (error) {
      // 处理不同类型的错误
      let apiError: ApiError

      if (error instanceof ApiError) {
        apiError = error
      } else if (error instanceof TypeError && error.message.includes('fetch')) {
        apiError = new ApiError('网络连接失败', ApiErrorType.NETWORK_ERROR)
      } else if (error.name === 'TimeoutError') {
        apiError = new ApiError('请求超时', ApiErrorType.TIMEOUT_ERROR)
      } else {
        apiError = new ApiError(
          error instanceof Error ? error.message : '未知错误',
          ApiErrorType.NETWORK_ERROR
        )
      }

      // 执行错误拦截器
      const processedError = await this.executeErrorInterceptors(apiError)
      throw processedError
    }
  }

  /**
   * GET请求
   */
  protected async get<T = any>(url: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    return this.request<T>({ method: 'GET', url, params })
  }

  /**
   * POST请求
   */
  protected async post<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>({ method: 'POST', url, data })
  }

  /**
   * PUT请求
   */
  protected async put<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>({ method: 'PUT', url, data })
  }

  /**
   * DELETE请求
   */
  protected async delete<T = any>(url: string): Promise<ApiResponse<T>> {
    return this.request<T>({ method: 'DELETE', url })
  }

  /**
   * PATCH请求
   */
  protected async patch<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>({ method: 'PATCH', url, data })
  }
}
