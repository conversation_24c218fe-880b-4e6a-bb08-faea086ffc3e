-- Cursor Free VIP SQLite数据库初始化脚本
-- 启用外键支持
PRAGMA foreign_keys = ON;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT,
    device_id TEXT UNIQUE,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    last_login TEXT,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'banned'))
);

-- 创建用户表索引
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_device_id ON users(device_id);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);

-- Cursor认证信息表（对应原SQLite的ItemTable）
CREATE TABLE IF NOT EXISTS cursor_auth (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    auth_key TEXT NOT NULL,
    auth_value TEXT,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, auth_key)
);

-- 创建认证表索引
CREATE INDEX IF NOT EXISTS idx_cursor_auth_key ON cursor_auth(auth_key);
CREATE INDEX IF NOT EXISTS idx_cursor_auth_user_id ON cursor_auth(user_id);

-- Cursor账户表
CREATE TABLE IF NOT EXISTS cursor_accounts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    email TEXT NOT NULL,
    access_token TEXT,
    refresh_token TEXT,
    auth_type TEXT DEFAULT 'Auth_0',
    is_active INTEGER DEFAULT 1,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建账户表索引
CREATE INDEX IF NOT EXISTS idx_cursor_accounts_user_id ON cursor_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_cursor_accounts_email ON cursor_accounts(email);
CREATE INDEX IF NOT EXISTS idx_cursor_accounts_active ON cursor_accounts(is_active);

-- 用户配置表
CREATE TABLE IF NOT EXISTS user_configs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    config_key TEXT NOT NULL,
    config_value TEXT, -- JSON存储为TEXT
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now')),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, config_key)
);

-- 创建配置表索引
CREATE INDEX IF NOT EXISTS idx_user_configs_key ON user_configs(config_key);
CREATE INDEX IF NOT EXISTS idx_user_configs_user_id ON user_configs(user_id);

-- 备份记录表
CREATE TABLE IF NOT EXISTS backup_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    backup_type TEXT NOT NULL CHECK (backup_type IN ('settings', 'accounts', 'full')),
    backup_path TEXT,
    backup_size INTEGER,
    status TEXT DEFAULT 'in_progress' CHECK (status IN ('success', 'failed', 'in_progress')),
    created_at TEXT DEFAULT (datetime('now')),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建备份表索引
CREATE INDEX IF NOT EXISTS idx_backup_records_user_backup ON backup_records(user_id, backup_type);
CREATE INDEX IF NOT EXISTS idx_backup_records_status ON backup_records(status);

-- 操作日志表
CREATE TABLE IF NOT EXISTS operation_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    operation TEXT NOT NULL,
    details TEXT,
    status TEXT NOT NULL CHECK (status IN ('success', 'failed', 'warning')),
    ip_address TEXT,
    user_agent TEXT,
    created_at TEXT DEFAULT (datetime('now')),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 创建日志表索引
CREATE INDEX IF NOT EXISTS idx_operation_logs_user_operation ON operation_logs(user_id, operation);
CREATE INDEX IF NOT EXISTS idx_operation_logs_status ON operation_logs(status);
CREATE INDEX IF NOT EXISTS idx_operation_logs_created_at ON operation_logs(created_at);

-- 创建更新时间触发器
CREATE TRIGGER IF NOT EXISTS update_users_timestamp 
    AFTER UPDATE ON users
BEGIN
    UPDATE users SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_cursor_auth_timestamp 
    AFTER UPDATE ON cursor_auth
BEGIN
    UPDATE cursor_auth SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_cursor_accounts_timestamp 
    AFTER UPDATE ON cursor_accounts
BEGIN
    UPDATE cursor_accounts SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_user_configs_timestamp 
    AFTER UPDATE ON user_configs
BEGIN
    UPDATE user_configs SET updated_at = datetime('now') WHERE id = NEW.id;
END;

-- 插入默认用户（用于本地模式兼容）
INSERT OR IGNORE INTO users (id, email, device_id, status) 
VALUES (1, '<EMAIL>', 'local-device', 'active');
