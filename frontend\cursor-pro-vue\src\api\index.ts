/**
 * API模块入口文件 - 现代化版本
 * 统一导出所有API相关的功能
 */

// 导出基础类型和错误处理
export * from './base'
export * from './types'
export * from './error-handler'

// 导出服务实例
export { settingsService } from './services/settings'
export { accountService } from './services/account'
export { logService } from './services/logs'
export { machineIdService } from './services/machine-id'
export { backupService } from './services/backup'
export { oauthService } from './services/oauth'
export { systemService } from './services/system'

// 导出服务类（用于扩展）
export { SettingsService } from './services/settings'
export { AccountService } from './services/account'
export { LogService } from './services/logs'
export { MachineIdService } from './services/machine-id'
export { BackupService } from './services/backup'
export { OAuthService } from './services/oauth'
export { SystemService } from './services/system'

// 兼容性：保持原有的api导出
import { realAPI } from './real-api'
import { settingsService } from './services/settings'
import { accountService } from './services/account'
import { logService } from './services/logs'
import { machineIdService } from './services/machine-id'
import { backupService } from './services/backup'
import { oauthService } from './services/oauth'
import { systemService } from './services/system'

// 现代化API聚合对象
export const api = {
  // 新的服务实例
  settings: settingsService,
  account: accountService,
  logs: logService,
  machineId: machineIdService,
  backup: backupService,
  oauth: oauthService,
  system: systemService,

  // 兼容性：保留原有方法
  ...realAPI
}

// 默认导出现代化API
export default api
