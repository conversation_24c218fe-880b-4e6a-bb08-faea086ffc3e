name: ❌ Bug Report [English]
description: Create a report to help us improve
title: '[Bug]: '
labels: ['bug']
body:
  - type: markdown
    attributes:
      value: |
        Thank you for taking the time to fill out this bug report!
        Before submitting this issue, please ensure that you have read the [github issues](https://github.com/yeongpin/cursor-pro/issues)

  - type: checkboxes
    id: checklist
    attributes:
      label: Commit before submitting
      description: |
        Please ensure that you have completed all of the following steps before submitting an issue
      options:
        - label: I understand that Issues are used to provide feedback and solve problems, not to complain in the comments section, and will provide more information to help solve the problem.
          required: true
        - label: I have checked the top Issue and searched for existing [open issues](https://github.com/yeongpin/cursor-pro/issues) and [closed issues](https://github.com/yeongpin/cursor-pro/issues?q=is%3Aissue%20state%3Aclosed%20), and found no similar issues.
          required: true
        - label: I have filled out a short and clear title, so that developers can quickly determine the general problem when browsing the Issue list. Not "a suggestion", "stuck", etc.
          required: true

  - type: dropdown
    id: platform
    attributes:
      label: Platform
      description: Which platform are you using?
      options:
        - Windows x32
        - Windows x64
        - macOS Intel
        - macOS ARM64
        - Linux x64
        - Linux ARM64
    validations:
      required: true

  - type: input
    id: version
    attributes:
      label: Version
      description: What version of Cursor Free Vip are you running?
      placeholder: For example v1.0.0 ( Not Cursor AI Version )
    validations:
      required: true

  - type: textarea
    id: description
    attributes:
      label: Description
      description: Please describe the problem as detailed as possible
      placeholder: Tell us what happened...
    validations:
      required: true

  - type: textarea
    id: logs
    attributes:
      label: Related log output
      description: Please copy and paste any related log output
      render: shell

  - type: textarea
    id: additional
    attributes:
      label: Additional information
      description: Anything that might help us understand the problem better
