@echo off
chcp 65001 >nul 2>&1
title Cursor Pro - Web Launcher
color 0A

echo.
echo ========================================
echo   Cursor Pro Web Launcher
echo ========================================
echo.

echo Starting backend API server...
set "PYTHONPATH=%cd%\src"
echo Backend will be available at: http://localhost:8080
echo.
echo Tips:
echo - Web interface will be accessible in your browser
echo - Press Ctrl+C to stop the server
echo - Server logs will appear below
echo.

echo Opening web interface in browser in 5 seconds...
start /min cmd /c "timeout /t 5 /nobreak >nul && start http://localhost:8080"

python -m cursor_pro.core.api_server

echo.
echo Server stopped
pause
