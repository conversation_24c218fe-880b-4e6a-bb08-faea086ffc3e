# 🚀 Cursor Pro 快速启动指南

## 📋 两种使用方式

### 🖥️ 桌面版（推荐）
```bash
双击运行: start-cursor-pro-desktop.bat
```
- ✅ 原生桌面体验
- ✅ 自动启动后端API
- ✅ Electron界面，支持Windows 11圆角

### 🌐 网页版
```bash
双击运行: start-cursor-pro-web.bat
```
- ✅ 浏览器访问 http://localhost:8080
- ✅ 跨平台兼容
- ✅ 无需额外安装

## 🔧 系统要求

### 必需环境
- **Python 3.7+** - 后端API服务器
- **Node.js 16+** - 前端构建（仅桌面版需要）

### 可选环境
- **现代浏览器** - Chrome/Edge/Firefox（网页版）

## 💡 使用提示

### 首次使用
1. 确保Python和Node.js已安装
2. 选择桌面版或网页版启动
3. 等待后端API启动完成
4. 开始使用Cursor Pro功能

### 常见问题
- **启动失败**: 检查Python和Node.js是否正确安装
- **端口占用**: 确保8080端口未被占用
- **权限问题**: 尝试以管理员身份运行

## 📚 更多文档
- **[完整文档](README.md)** - 详细的项目说明
- **[文档索引](docs/INDEX.md)** - 所有文档的索引
- **[配置指南](docs/CONFIGURATION_GUIDE.md)** - 高级配置选项

---

*🎯 快速开始，立即体验Cursor Pro的强大功能！*
