<template>
  <div class="content-section">
    <div class="log-container">
      <div class="log-header">
        <h3 class="log-title">终端日志输出</h3>
        <div class="log-controls">
          <!-- 日志级别过滤 -->
          <div class="log-filters">
            <button
              class="filter-btn"
              :class="{ active: activeFilter === 'all' }"
              @click="toggleFilter('all')"
            >
              全部
            </button>
            <button
              class="filter-btn info"
              :class="{ active: activeFilter === 'info' }"
              @click="toggleFilter('info')"
            >
              信息
            </button>
            <button
              class="filter-btn success"
              :class="{ active: activeFilter === 'success' }"
              @click="toggleFilter('success')"
            >
              成功
            </button>
            <button
              class="filter-btn warning"
              :class="{ active: activeFilter === 'warning' }"
              @click="toggleFilter('warning')"
            >
              警告
            </button>
            <button
              class="filter-btn error"
              :class="{ active: activeFilter === 'error' }"
              @click="toggleFilter('error')"
            >
              错误
            </button>
          </div>
          <button class="refresh-log-btn" @click="refreshLogs">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
              <path d="M21 3v5h-5"/>
              <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
              <path d="M3 21v-5h5"/>
            </svg>
            刷新日志
          </button>
        </div>
      </div>

      <div class="log-card" ref="logArea">
        <!-- 月亮装饰 -->
        <div class="moon-decoration">
          <div class="moon">
            <div class="crater cr1"></div>
            <div class="crater cr2"></div>
            <div class="crater cr3"></div>
          </div>
        </div>

        <div class="log-content-inner">
          <div v-if="loading" class="log-loading">
            <div class="loading-spinner"></div>
            正在加载日志...
          </div>
          <div v-else-if="filteredLogs.length === 0" class="empty-log">
            <div class="empty-icon">📝</div>
            <div class="empty-text">暂无日志</div>
            <div class="empty-hint">执行操作后，日志会在这里显示</div>
          </div>
          <div v-else class="log-content">
            <div
              v-for="(log, index) in filteredLogs"
              :key="index"
              class="log-entry"
              :class="log.level"
            >
              <span class="log-time">{{ log.time }}</span>
              <span class="log-text" v-html="log.content"></span>
            </div>
          </div>
        </div>

        <div class="points_wrapper">
          <i class="point"></i>
          <i class="point"></i>
          <i class="point"></i>
          <i class="point"></i>
          <i class="point"></i>
          <i class="point"></i>
          <i class="point"></i>
          <i class="point"></i>
          <i class="point"></i>
          <i class="point"></i>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useLogStore } from '@/stores/logs'

// 定义组件名称（用于keep-alive缓存）
defineOptions({
  name: 'Logs'
})

interface LogEntry {
  time: string
  level: 'info' | 'success' | 'warning' | 'error'
  content: string
  raw: string
}

// 日志存储
const logStore = useLogStore()

const logArea = ref<HTMLElement>()
const logContent = ref<string>('')
const loading = ref(false)
let refreshInterval: number | null = null

// 当前选中的过滤器类型
const activeFilter = ref<string>('all')

// 解析的日志条目
const parsedLogs = ref<LogEntry[]>([])

// 过滤后的日志 - 使用日志存储
const filteredLogs = computed(() => {
  // 优先使用日志存储中的日志
  if (logStore.logs.length > 0) {
    const logs = logStore.logs

    if (activeFilter.value === 'all') {
      return logs.map(log => ({
        time: new Date(log.timestamp).toLocaleTimeString(),
        level: log.level,
        content: log.content,
        raw: log.content
      }))
    }

    return logs
      .filter(log => log.level === activeFilter.value)
      .map(log => ({
        time: new Date(log.timestamp).toLocaleTimeString(),
        level: log.level,
        content: log.content,
        raw: log.content
      }))
  }

  // 回退到解析的日志
  if (activeFilter.value === 'all') {
    return parsedLogs.value
  }
  return parsedLogs.value.filter(log => log.level === activeFilter.value)
})

// 过滤器切换
const toggleFilter = (type: string) => {
  activeFilter.value = type
}

// 获取日志图标
const getLogIcon = (level: string) => {
  const icons = {
    info: '💡',
    success: '✅',
    warning: '⚠️',
    error: '❌'
  }
  return icons[level as keyof typeof icons] || '📝'
}

// 解析日志内容
const parseLogContent = (content: string): LogEntry[] => {
  if (!content) return []

  const lines = content.split('\n').filter(line => line.trim())
  const logs: LogEntry[] = []

  lines.forEach(line => {
    // 提取时间戳
    const timeMatch = line.match(/\[(\d{2}:\d{2}:\d{2})\]/)
    const time = timeMatch ? timeMatch[1] : new Date().toLocaleTimeString()

    // 判断日志级别
    let level: LogEntry['level'] = 'info'
    let content = line

    if (line.includes('系统初始化开始') || line.includes('核心模块导入成功') || line.includes('Vue前端界面加载完成')) {
      level = 'info'
      content = line.replace(/\[.*?\]\s*/, '')
    } else if (line.includes('成功') || line.includes('✅') || line.includes('完成')) {
      level = 'success'
      content = line.replace(/\[.*?\]\s*/, '')
    } else if (line.includes('警告') || line.includes('⚠️') || line.includes('等待用户操作')) {
      level = 'warning'
      content = line.replace(/\[.*?\]\s*/, '')
    } else if (line.includes('错误') || line.includes('❌') || line.includes('失败') || line.includes('正在获取账户信息')) {
      level = 'error'
      content = line.replace(/\[.*?\]\s*/, '')
    }

    // 美化内容 - 移除图标，使用文字标识
    content = content
      .replace(/🔧/g, '<span class="emoji">[TOOL]</span>')
      .replace(/✅/g, '<span class="emoji">[OK]</span>')
      .replace(/🔗/g, '<span class="emoji">[LINK]</span>')
      .replace(/💡/g, '<span class="emoji">[TIP]</span>')
      .replace(/💎/g, '<span class="emoji">[GEM]</span>')
      .replace(/PyWebView API/g, '<span class="highlight">PyWebView API</span>')
      .replace(/Vue前端界面/g, '<span class="highlight">Vue前端界面</span>')
      .replace(/系统准备就绪/g, '<span class="highlight">系统准备就绪</span>')

    logs.push({
      time,
      level,
      content,
      raw: line
    })
  })

  return logs
}

// 获取日志内容
const getLogs = async () => {
  try {
    // 优先使用HTTP API
    try {
      const response = await fetch('/api/get_log', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          logContent.value = result.data || ''
          parsedLogs.value = parseLogContent(logContent.value)
          return
        }
      }
    } catch (httpError) {
      console.log('HTTP API调用失败，尝试PyWebView API:', httpError)
    }

    // 如果HTTP API也失败，显示默认内容
    logContent.value = '[终端日志输出区]\n\n暂无日志输出。\n\n提示：\n- 执行任何操作后，相关日志会在这里显示\n- 日志会自动刷新\n- 支持彩色显示'
    parsedLogs.value = parseLogContent(logContent.value)
  } catch (error) {
    console.error('获取日志失败:', error)
    logContent.value = '获取日志失败: ' + error
    parsedLogs.value = []
  }
}

// 刷新日志
const refreshLogs = async () => {
  console.log('🔄 手动刷新日志，当前日志数量:', logStore.logs.length)

  loading.value = true

  // 确保日志存储已初始化
  logStore.init()

  // 添加刷新日志
  logStore.addInfo('🔄 用户手动刷新日志页面', 'LogsPage')

  // 模拟加载时间，让用户看到反馈
  await new Promise(resolve => setTimeout(resolve, 500))

  loading.value = false

  // 滚动到底部
  if (logArea.value) {
    logArea.value.scrollTop = logArea.value.scrollHeight
  }
}



// 组件挂载时获取日志
onMounted(() => {
  // 确保日志存储已初始化（只会执行一次）
  logStore.init()

  console.log('📝 日志页面已挂载，当前日志数量:', logStore.logs.length)

  // 不再调用后端API，直接使用日志存储
  // 如果需要后端日志，可以手动刷新
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
    refreshInterval = null
  }
  console.log('📝 日志页面已卸载')
})
</script>

<style scoped>
.log-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 4px;
  gap: 16px;
}

.log-title {
  color: #60a5fa;
  font-size: 1.2rem;
  font-weight: bold;
  margin: 0;
}

.log-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.log-filters {
  display: flex;
  gap: 8px;
  align-items: center;
}

.filter-btn {
  position: relative;
  transition: all 0.3s ease-in-out;
  box-shadow: 0px 6px 15px rgba(0, 0, 0, 0.3);
  padding: 8px 16px;
  background-color: #475569;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #e2e8f0;
  gap: 6px;
  font-weight: 600;
  border: 2px solid rgba(255, 255, 255, 0.2);
  outline: none;
  overflow: hidden;
  font-size: 0.8rem;
  cursor: pointer;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.filter-btn::before {
  content: "";
  position: absolute;
  width: 80px;
  height: 100%;
  background-image: linear-gradient(
    120deg,
    rgba(255, 255, 255, 0) 30%,
    rgba(255, 255, 255, 0.6),
    rgba(255, 255, 255, 0) 70%
  );
  top: 0;
  left: -80px;
  opacity: 0.6;
}

.filter-btn:hover {
  transform: scale(1.05);
  border-color: rgba(255, 255, 255, 0.4);
  animation: shake 0.6s ease-in-out;
}

.filter-btn:hover::before {
  animation: shine 1.5s ease-out infinite;
}

.filter-btn.active {
  background-color: rgb(0 107 179);
  border-color: rgba(255, 255, 255, 0.6);
  color: #ffffff;
  box-shadow: 0px 8px 20px rgba(0, 107, 179, 0.4);
  transform: scale(1.02);
}

.filter-btn.info.active {
  background-color: #3b82f6;
  box-shadow: 0px 8px 20px rgba(59, 130, 246, 0.4);
}

.filter-btn.success.active {
  background-color: #10b981;
  box-shadow: 0px 8px 20px rgba(16, 185, 129, 0.4);
}

.filter-btn.warning.active {
  background-color: #f59e0b;
  box-shadow: 0px 8px 20px rgba(245, 158, 11, 0.4);
}

.filter-btn.error.active {
  background-color: #ef4444;
  box-shadow: 0px 8px 20px rgba(239, 68, 68, 0.4);
}

.refresh-log-btn {
  position: relative;
  transition: all 0.3s ease-in-out;
  box-shadow: 0px 8px 20px rgba(0, 0, 0, 0.3);
  padding: 10px 20px;
  background-color: rgb(16 185 129);
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  gap: 8px;
  font-weight: 600;
  border: 2px solid rgba(255, 255, 255, 0.3);
  outline: none;
  overflow: hidden;
  font-size: 0.9rem;
  cursor: pointer;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.refresh-log-btn::before {
  content: "";
  position: absolute;
  width: 100px;
  height: 100%;
  background-image: linear-gradient(
    120deg,
    rgba(255, 255, 255, 0) 30%,
    rgba(255, 255, 255, 0.8),
    rgba(255, 255, 255, 0) 70%
  );
  top: 0;
  left: -100px;
  opacity: 0.6;
}

.refresh-log-btn:hover {
  transform: scale(1.05);
  border-color: rgba(255, 255, 255, 0.6);
  animation: shake 0.6s ease-in-out;
}

.refresh-log-btn:hover::before {
  animation: shine 1.5s ease-out infinite;
}

.refresh-log-btn:hover svg {
  transform: rotate(180deg);
}

/* 动态边框日志卡片 */
.log-card {
  --border: 4px;
  --rounded: 16px;
  --w-card: 100%;
  --h-card: 500px;
  margin: 0;
  width: var(--w-card);
  height: var(--h-card);
  min-height: 400px;
  max-height: 500px;
  border-radius: var(--rounded);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: var(--border);
}

.log-card::before,
.log-card::after {
  content: "";
  z-index: 1;
  position: absolute;
  top: 0;
  left: 0;
  will-change: auto;
  width: 100%;
  height: 100%;
  border-radius: var(--rounded);
  background-size: 400% 400%;
  animation: bg-spin 3s linear 0s infinite normal none running;
  background-image: radial-gradient(
      circle farthest-side at 0 100%,
      #00ccb1,
      transparent
    ),
    radial-gradient(circle farthest-side at 100% 0, #5ddcff, transparent),
    radial-gradient(circle farthest-side at 100% 100%, #3c67e3, transparent),
    radial-gradient(circle farthest-side at 0 0, #4e00c2, #0000);
}

.log-card::after {
  z-index: 0;
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter,
    backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.5s;
  animation-delay: 0.5s;
  filter: blur(24px);
  opacity: 0.7;
}

@keyframes bg-spin {
  25% {
    background-position: right 20% bottom 40%;
  }
  75% {
    background-position: left 45% top 20%;
  }
}



/* 浮动粒子效果 */
.points_wrapper {
  position: absolute;
  top: var(--border);
  left: var(--border);
  overflow: hidden;
  width: calc(100% - (var(--border) * 2));
  height: calc(100% - (var(--border) * 2));
  border-radius: calc(var(--rounded) - 4px);
  pointer-events: none;
  z-index: 15;
}

.points_wrapper .point {
  --sz-point: 4px;
  top: -8px;
  position: absolute;
  animation: floating-points infinite ease-in-out;
  pointer-events: none;
  width: var(--sz-point);
  height: var(--sz-point);
  background-color: #5ddcff;
  border-radius: 9999px;
}

@keyframes floating-points {
  0% {
    transform: translate(0, 0);
  }
  95% {
    opacity: 0;
  }
  100% {
    opacity: 0;
    transform: translate(calc(var(--h-card) / 1.75), calc(var(--h-card) / 1.5));
  }
}

.points_wrapper .point:nth-child(1) {
  left: 10%;
  opacity: 1;
  animation-duration: 2.35s;
  animation-delay: 0.2s;
}
.points_wrapper .point:nth-child(2) {
  left: 30%;
  opacity: 0.7;
  animation-duration: 2.5s;
  animation-delay: 0.5s;
}
.points_wrapper .point:nth-child(3) {
  left: 25%;
  opacity: 0.8;
  animation-duration: 2.2s;
  animation-delay: 0.1s;
}
.points_wrapper .point:nth-child(4) {
  left: 44%;
  opacity: 0.6;
  animation-duration: 2.05s;
}
.points_wrapper .point:nth-child(5) {
  left: 50%;
  opacity: 1;
  animation-duration: 1.9s;
}
.points_wrapper .point:nth-child(6) {
  left: 75%;
  opacity: 0.5;
  animation-duration: 1.5s;
  animation-delay: 1.5s;
}
.points_wrapper .point:nth-child(7) {
  left: 88%;
  opacity: 0.9;
  animation-duration: 2.2s;
  animation-delay: 0.2s;
}
.points_wrapper .point:nth-child(8) {
  left: 58%;
  opacity: 0.8;
  animation-duration: 2.25s;
  animation-delay: 0.2s;
}
.points_wrapper .point:nth-child(9) {
  left: 98%;
  opacity: 0.6;
  animation-duration: 2.6s;
  animation-delay: 0.1s;
}
.points_wrapper .point:nth-child(10) {
  left: 65%;
  opacity: 1;
  animation-duration: 2.5s;
  animation-delay: 0.2s;
}

/* 日志内容区域 */
.log-content-inner {
  position: absolute;
  top: var(--border);
  left: var(--border);
  width: calc(100% - (var(--border) * 2));
  height: calc(100% - (var(--border) * 2));
  border-radius: calc(var(--rounded) - 4px);
  overflow-y: auto;
  z-index: 10;
  background-color: #191c29;
  padding: 16px 20px;
  /* 现代字体样式 */
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  white-space: pre-wrap;
  line-height: 1.6;
}

.log-content-inner::-webkit-scrollbar {
  width: 6px;
}

.log-content-inner::-webkit-scrollbar-track {
  background: transparent;
}

.log-content-inner::-webkit-scrollbar-thumb {
  background: rgba(100, 116, 139, 0.3);
  border-radius: 3px;
}

.log-content-inner::-webkit-scrollbar-thumb:hover {
  background: rgba(100, 116, 139, 0.5);
}

.log-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
  text-align: center;
  padding: 60px 20px;
  font-style: italic;
  gap: 12px;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #374151;
  border-top: 2px solid #60a5fa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-log {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 60px 20px;
  gap: 12px;
}

.empty-icon {
  font-size: 3rem;
  opacity: 0.6;
}

.empty-text {
  color: #94a3b8;
  font-size: 1.1rem;
  font-weight: 600;
}

.empty-hint {
  color: #6b7280;
  font-size: 0.9rem;
  line-height: 1.5;
}

.log-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

/* 日志条目样式 - 现代简洁风格 */
.log-entry {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px 0;
  margin-bottom: 4px;
  transition: all 0.3s ease;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.log-entry:hover {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 6px;
  padding: 8px 12px;
}

.log-time {
  color: #64748b;
  font-size: 0.9rem;
  font-weight: 400;
  min-width: 70px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.log-icon {
  font-size: 1rem;
  min-width: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.log-text {
  flex: 1;
  word-wrap: break-word;
  line-height: 1.5;
}

/* 不同级别的日志文本颜色 */
.log-entry.info .log-text {
  color: #cbd5e1;
}

.log-entry.success .log-text {
  color: #86efac;
}

.log-entry.warning .log-text {
  color: #fbbf24;
}

.log-entry.error .log-text {
  color: #fca5a5;
}

/* 特殊元素样式 */
.emoji {
  font-size: 1.1em;
  margin: 0 2px;
}

.highlight {
  background: rgba(96, 165, 250, 0.2);
  color: #93c5fd;
  padding: 1px 4px;
  border-radius: 3px;
  font-weight: 600;
}

/* 动画关键帧 */
@keyframes shine {
  0% {
    left: -100px;
  }
  60% {
    left: 100%;
  }
  to {
    left: 100%;
  }
}

@keyframes shake {
  0%, 100% {
    transform: scale(1.05) translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: scale(1.05) translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: scale(1.05) translateX(2px);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .log-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .log-controls {
    flex-direction: column;
    gap: 12px;
  }

  .log-filters {
    justify-content: center;
    flex-wrap: wrap;
  }

  .log-entry {
    padding: 8px;
    gap: 6px;
  }

  .log-time {
    min-width: 60px;
    font-size: 0.85rem;
    font-weight: 400;
  }
}

/* 月亮装饰样式 */
.moon-decoration {
  position: absolute;
  top: 20px;
  right: 30px;
  width: 180px;
  height: 180px;
  pointer-events: none;
  z-index: 100;
  overflow: visible;
  background: transparent;
  border: none;
  outline: none;
  box-shadow: none;
  margin: 0;
  padding: 0;
  /* 调试用边框，确认位置 */
  /* border: 1px solid red; */
}

/* Enhanced Moon with Craters */
.moon {
  height: 80px;
  width: 80px;
  background: linear-gradient(145deg, #eeeeee, #f8f8f8);
  border-radius: 50%;
  position: absolute;
  right: 50px;
  top: 50px;
  box-shadow:
    0 0 25px rgba(235, 235, 235, 0.6),
    0 0 40px rgba(255, 255, 255, 0.3),
    inset -5px -5px 15px rgba(0, 0, 0, 0.12);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 101;
  opacity: 0.75;
}

/* Moon Craters */
.crater {
  position: absolute;
  background: rgba(200, 200, 200, 0.3);
  border-radius: 50%;
  box-shadow: inset 1px 1px 3px rgba(0, 0, 0, 0.1);
}

.cr1 {
  width: 12px;
  height: 12px;
  top: 18px;
  left: 12px;
}
.cr2 {
  width: 16px;
  height: 16px;
  top: 40px;
  left: 35px;
}
.cr3 {
  width: 10px;
  height: 10px;
  top: 50px;
  left: 18px;
}



/* Hover Effects */
.log-card:hover .moon {
  box-shadow:
    0 0 40px rgba(173, 216, 230, 0.6),
    0 0 50px rgba(255, 255, 255, 0.4),
    inset -6px -6px 20px rgba(0, 0, 0, 0.15);
  background: linear-gradient(145deg, #f0f8ff, #ffffff);
  transform: scale(1.05);
  opacity: 0.9;
}
</style>
