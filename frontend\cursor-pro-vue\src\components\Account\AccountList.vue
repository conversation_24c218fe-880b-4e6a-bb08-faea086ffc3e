<template>
  <div class="registered-accounts-section">
    <!-- 标题栏 -->
    <div class="section-title">

      <span class="title-text">注册账户列表</span>
      <button 
        class="refresh-accounts-btn"
        @click="handleRefresh"
        :disabled="loading"
      >
        {{ loading ? '刷新中...' : '刷新' }}
      </button>
    </div>

    <!-- 账户列表容器 -->
    <div class="registered-accounts-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <span>加载账户列表中...</span>
      </div>

      <!-- 空状态 -->
      <div v-else-if="accounts.length === 0" class="empty-state">
        <div class="empty-icon">📭</div>
        <div class="empty-text">暂无注册账户</div>
        <div class="empty-hint">使用一键注册功能创建新账户</div>
      </div>

      <!-- 账户列表 -->
      <div v-else class="accounts-list">
        <AccountCard
          v-for="(account, index) in accounts"
          :key="account.email || index"
          :account="account"
          :index="index"
          @select="handleSelectAccount"
          @copy="handleCopyAccount"
          @delete="handleDeleteAccount"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import AccountCard from './AccountCard.vue'

interface RegisteredAccount {
  email: string
  password: string
  firstName?: string
  lastName?: string
  registeredAt?: string
  isActive?: boolean
  trialDays?: number
  masterEmail?: string
  token?: string
  originalIndex: number
}

interface AccountListProps {
  accounts: RegisteredAccount[]
  loading?: boolean
}

interface AccountListEvents {
  'refresh': []
  'select-account': [account: RegisteredAccount]
  'copy-account': [account: RegisteredAccount]
  'delete-account': [account: RegisteredAccount]
}

const props = withDefaults(defineProps<AccountListProps>(), {
  loading: false
})

const emit = defineEmits<AccountListEvents>()

// 方法
const handleRefresh = () => {
  emit('refresh')
}

const handleSelectAccount = (account: RegisteredAccount) => {
  emit('select-account', account)
}

const handleCopyAccount = (account: RegisteredAccount) => {
  emit('copy-account', account)
}

const handleDeleteAccount = (account: RegisteredAccount) => {
  emit('delete-account', account)
}
</script>

<style scoped>
.registered-accounts-section {
  margin-top: 24px;
}

/* 标题栏 */
.section-title {
  color: #60a5fa;
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: 'Fira Mono', 'Consolas', monospace;
}

.title-icon {
  font-size: 1.1rem;
}

.title-text {
  flex: 1;
}

.refresh-accounts-btn {
  margin-left: auto;
  padding: 6px 12px;
  background: #374151;
  color: #e5e5e5;
  border: 1px solid #4b5563;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Fira Mono', 'Consolas', monospace;
}

.refresh-accounts-btn:hover:not(:disabled) {
  background: #4b5563;
  border-color: #6b7280;
}

.refresh-accounts-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 容器 */
.registered-accounts-container {
  min-height: 200px;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #94a3b8;
  font-family: 'Fira Mono', 'Consolas', monospace;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #374151;
  border-top: 3px solid #60a5fa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text {
  color: #e5e5e5;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
  font-family: 'Fira Mono', 'Consolas', monospace;
}

.empty-hint {
  color: #94a3b8;
  font-size: 0.9rem;
  font-family: 'Fira Mono', 'Consolas', monospace;
}

/* 账户列表 */
.accounts-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
</style>
