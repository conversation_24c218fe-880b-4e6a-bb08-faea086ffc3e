#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
构建Cursor Pro启动器exe
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否安装"""
    try:
        subprocess.check_output(['pyinstaller', '--version'])
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ PyInstaller未安装")
        print("请运行: pip install pyinstaller")
        return False

def create_launcher_spec():
    """创建启动器的spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['cursor_pro_launcher.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='Cursor Pro 启动器',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 无控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='cursor-pro.ico' if os.path.exists('cursor-pro.ico') else None,
)
'''
    
    with open('cursor-pro-launcher.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 创建了启动器spec文件")

def build_launcher():
    """构建启动器exe"""
    print("🚀 开始构建Cursor Pro启动器...")
    
    # 检查PyInstaller
    if not check_pyinstaller():
        return False
    
    # 创建spec文件
    create_launcher_spec()
    
    # 清理之前的构建
    if os.path.exists('dist'):
        shutil.rmtree('dist')
        print("🧹 清理旧的构建文件")
    
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # 执行构建
    try:
        print("📦 正在打包启动器...")
        subprocess.check_call([
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            "cursor-pro-launcher.spec"
        ])
        
        launcher_exe = "dist/Cursor Pro 启动器.exe"
        if os.path.exists(launcher_exe):
            print("✅ 启动器打包成功!")
            print(f"📁 可执行文件位置: {os.path.abspath(launcher_exe)}")
            return True
        else:
            print("❌ 打包失败，未找到可执行文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包过程中出错: {e}")
        return False

def copy_to_release():
    """复制启动器到发布目录"""
    launcher_exe = "dist/Cursor Pro 启动器.exe"
    if not os.path.exists(launcher_exe):
        print("❌ 启动器exe不存在")
        return False
    
    release_dirs = [
        "release_cursor_pro_v3",
        "release_cursor_pro_v2", 
        "release_cursor_pro_final"
    ]
    
    for release_dir in release_dirs:
        if os.path.exists(release_dir):
            target_path = os.path.join(release_dir, "Cursor Pro 启动器.exe")
            shutil.copy2(launcher_exe, target_path)
            print(f"✅ 复制启动器到: {target_path}")
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("🏗️  Cursor Pro - 启动器打包工具")
    print("=" * 50)
    
    if build_launcher():
        print("\n🎉 启动器打包完成!")
        
        # 复制到发布目录
        if copy_to_release():
            print("📦 启动器已复制到所有发布目录")
        
        print("\n🚀 使用方法:")
        print("1. 双击 'Cursor Pro 启动器.exe'")
        print("2. 点击'启动 Cursor Pro'按钮")
        print("3. 等待前后端自动启动")
        print("4. 享受完整功能!")
    else:
        print("\n❌ 启动器打包失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
