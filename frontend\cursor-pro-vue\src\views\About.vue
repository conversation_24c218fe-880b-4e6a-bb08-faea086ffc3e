<template>
  <div class="data-management-container">
    <!-- 顶部导航 -->
    <div class="top-nav">
      <div class="nav-title">📊 数据管理中心</div>
      <div class="nav-actions">
        <button class="btn-refresh" @click="refreshAllData" :disabled="loading">
          <i class="icon-refresh" :class="{ 'spinning': loading }"></i>
          {{ loading ? '刷新中...' : '刷新全部' }}
        </button>
        <button class="btn-backup" @click="showBackupManager = true">
          <svg class="icon-backup" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14,2 14,8 20,8"></polyline>
            <line x1="16" y1="13" x2="8" y2="13"></line>
            <line x1="16" y1="17" x2="8" y2="17"></line>
            <polyline points="10,9 9,9 8,9"></polyline>
          </svg>
          备份管理
        </button>
        <button class="btn-export" @click="exportAllData">
          <i class="icon-export"></i> 导出全部
        </button>
        <button class="btn-import" @click="importData">
          <i class="icon-import"></i> 导入数据
        </button>
      </div>
    </div>

    <!-- 主要布局 -->
    <div class="main-layout">
      <!-- 数据统计背景卡片容器 -->
      <div class="data-stats-container">
        <!-- 顶部统计栏 -->
        <div class="stats-bar">
          <h3>数据统计</h3>
          <div class="stats-row">
          <div class="stat-card">
            <div class="card-icon">👥</div>
            <div class="stat-number">{{ accountData.length }}</div>
            <div class="stat-label">账户总数</div>
            <div class="card-corner-accent"></div>
          </div>
          <div class="stat-card">
            <div class="card-icon">📁</div>
            <div class="stat-number">{{ dataFiles.length }}</div>
            <div class="stat-label">配置文件</div>
            <div class="card-corner-accent"></div>
          </div>
          <div class="stat-card">
            <div class="card-icon">💾</div>
            <div class="stat-number">{{ getTotalFileSize }}</div>
            <div class="stat-label">总大小</div>
            <div class="card-corner-accent"></div>
          </div>
          <div class="stat-card">
            <div class="card-icon">🕒</div>
            <div class="stat-number">{{ getLastUpdateTime }}</div>
            <div class="stat-label">最后更新</div>
            <div class="card-corner-accent"></div>
          </div>
        </div>
      </div>
      </div>

      <!-- 主内容区域 -->
      <div class="content">
        <h3>📄 数据文件列表</h3>
        <div class="table-container">
          <table class="data-table">
            <thead>
              <tr>
                <th>文件名</th>
                <th>类型</th>
                <th>大小</th>
                <th>修改时间</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="file in dataFiles" :key="file.name">
                <td>
                  <div class="file-name-cell">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                      <polyline points="14,2 14,8 20,8"/>
                      <line x1="16" y1="13" x2="8" y2="13"/>
                      <line x1="16" y1="17" x2="8" y2="17"/>
                    </svg>
                    {{ file.name }}
                  </div>
                </td>
                <td>{{ getFileType(file.name) }}</td>
                <td>{{ file.size }}</td>
                <td>{{ formatDate(file.modified) }}</td>
                <td><span class="status-badge">{{ getStatusText(file.status) }}</span></td>
                <td>
                  <button class="table-btn" @click="viewFile(file)">查看</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="content-accent-shape"></div>
        <div class="content-corner-slice"></div>
        <div class="content-dots-pattern">
          <svg viewBox="0 0 80 40">
            <circle fill="var(--text)" r="2" cy="10" cx="10" opacity="0.3"></circle>
            <circle fill="var(--text)" r="2" cy="10" cx="30" opacity="0.3"></circle>
            <circle fill="var(--text)" r="2" cy="10" cx="50" opacity="0.3"></circle>
            <circle fill="var(--text)" r="2" cy="10" cx="70" opacity="0.3"></circle>
            <circle fill="var(--text)" r="2" cy="20" cx="20" opacity="0.3"></circle>
            <circle fill="var(--text)" r="2" cy="20" cx="40" opacity="0.3"></circle>
            <circle fill="var(--text)" r="2" cy="20" cx="60" opacity="0.3"></circle>
            <circle fill="var(--text)" r="2" cy="30" cx="10" opacity="0.3"></circle>
            <circle fill="var(--text)" r="2" cy="30" cx="30" opacity="0.3"></circle>
            <circle fill="var(--text)" r="2" cy="30" cx="50" opacity="0.3"></circle>
            <circle fill="var(--text)" r="2" cy="30" cx="70" opacity="0.3"></circle>
          </svg>
        </div>
      </div>

      <!-- 右侧信息面板 -->
      <div class="actions">
        <!-- 信息面板背景卡片容器 - 包含账户信息和存储信息 -->
        <div class="info-panel-container">
          <!-- 账户信息 -->
          <div class="action-panel">
          <div class="panel-title">👥 账户信息</div>
          <div class="account-info">
            <div class="info-item">
              <span class="info-label">总账户数:</span>
              <span class="info-value">{{ accountData.length }}个</span>
            </div>
            <div class="info-item">
              <span class="info-label">异常账户:</span>
              <span class="info-value">{{ accountData.filter(acc => acc.status === 'inactive').length }}个</span>
            </div>
          </div>
          <div class="panel-accent-shape"></div>
          <div class="panel-corner-slice"></div>
        </div>

          <!-- 存储信息 -->
          <div class="action-panel">
          <div class="panel-title">💾 存储信息</div>
          <div class="storage-info">
            <div class="storage-item">
              <div class="storage-label">已使用空间</div>
              <div class="storage-bar">
                <div class="storage-progress" style="width: 65%"></div>
              </div>
              <div class="storage-text">{{ getTotalFileSize }}</div>
            </div>
            <div class="info-item">
              <span class="info-label">配置文件:</span>
              <span class="info-value">{{ dataFiles.filter(f => f.name.includes('.json')).length }}个</span>
            </div>
            <div class="info-item">
              <span class="info-label">日志文件:</span>
              <span class="info-value">{{ dataFiles.filter(f => f.name.includes('.log')).length }}个</span>
            </div>
            <div class="info-item">
              <span class="info-label">备份文件:</span>
              <span class="info-value">{{ dataFiles.filter(f => f.name.includes('backup')).length }}个</span>
            </div>
          </div>
          <div class="panel-accent-shape"></div>
          <div class="panel-corner-slice"></div>
        </div>
        </div>
      </div>
    </div>


  </div>

  <!-- 备份管理模态框 -->
  <BaseModal
    :visible="showBackupManager"
    title="数据备份管理"
    width="800px"
    @close="showBackupManager = false"
  >
    <BackupManager />
  </BaseModal>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useAppStore } from '@/stores/app'
import { useLogStore } from '@/stores/logs'
import BackupManager from '@/components/Backup/BackupManager.vue'
import BaseModal from '@/components/Modal/BaseModal.vue'
import { realAPI } from '@/api/real-api'
import { systemDetector } from '@/services/system-detector'

const appStore = useAppStore()
const logStore = useLogStore()

// 响应式数据
const accountData = ref<any[]>([])
const dataFiles = ref<any[]>([])
const storageUsage = ref<any>({})
const systemInfo = ref<any>({})
const showBackupManager = ref(false)
const loading = ref(false)

// 数据刷新方法
const refreshAllData = async () => {
  if (loading.value) return

  loading.value = true
  try {
    logStore.addInfo('正在刷新数据...', 'DataManagement')

    // 并行获取所有数据
    const [accountsResult, filesResult, storageResult, systemResult] = await Promise.allSettled([
      refreshAccountData(),
      refreshDataFiles(),
      refreshStorageUsage(),
      refreshSystemInfo()
    ])

    // 检查结果
    const errors = []
    if (accountsResult.status === 'rejected') errors.push('账户数据')
    if (filesResult.status === 'rejected') errors.push('文件数据')
    if (storageResult.status === 'rejected') errors.push('存储信息')
    if (systemResult.status === 'rejected') errors.push('系统信息')

    if (errors.length > 0) {
      logStore.addWarning(`部分数据刷新失败: ${errors.join(', ')}`, 'DataManagement')
    } else {
      logStore.addSuccess('所有数据刷新完成', 'DataManagement')
    }

  } catch (error) {
    logStore.addError(`数据刷新失败: ${(error as Error).message}`, 'DataManagement')
    appStore.showError(`刷新失败: ${(error as Error).message}`)
  } finally {
    loading.value = false
  }
}

// 账户数据管理
const refreshAccountData = async () => {
  try {
    const response = await realAPI.getAccountList()
    const accounts = Array.isArray(response) ? response : (response.data || [])
    accountData.value = accounts.map((account: any) => ({
      ...account,
      status: account.token ? 'active' : 'inactive',
      last_updated: account.last_updated || new Date().toLocaleString()
    }))
  } catch (error) {
    console.error('刷新账户数据失败:', error)
    throw error
  }
}

// 数据文件管理
const refreshDataFiles = async () => {
  try {
    const response = await systemDetector.scanConfigFiles()
    dataFiles.value = response || []
  } catch (error) {
    console.error('刷新数据文件失败:', error)
    throw error
  }
}

// 存储使用情况
const refreshStorageUsage = async () => {
  try {
    const response = await systemDetector.getStorageUsage()
    storageUsage.value = response || {}
  } catch (error) {
    console.error('刷新存储信息失败:', error)
    throw error
  }
}

// 系统信息
const refreshSystemInfo = async () => {
  try {
    const response = await systemDetector.detectSystemInfo()
    systemInfo.value = response || {}
  } catch (error) {
    console.error('刷新系统信息失败:', error)
    throw error
  }
}

const exportAccountData = async () => {
  try {
    const dataStr = JSON.stringify(accountData.value, null, 2)
    const blob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `cursor_accounts_${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)
    appStore.showSuccess('账户数据导出成功！')
  } catch (error) {
    appStore.showError(`导出失败: ${(error as Error).message}`)
  }
}

// 计算属性
const getTotalFileSize = computed(() => {
  if (!storageUsage.value.usedSize) return '0 B'
  return formatFileSize(storageUsage.value.usedSize)
})

const getLastUpdateTime = computed(() => {
  if (dataFiles.value.length === 0) return '未知'

  const latestFile = dataFiles.value.reduce((latest, file) => {
    const fileTime = new Date(file.modified).getTime()
    const latestTime = new Date(latest.modified).getTime()
    return fileTime > latestTime ? file : latest
  })

  const now = new Date()
  const fileTime = new Date(latestFile.modified)
  const diffMs = now.getTime() - fileTime.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffMinutes = Math.floor(diffMs / (1000 * 60))

  if (diffDays > 0) return `${diffDays}天前`
  if (diffHours > 0) return `${diffHours}小时前`
  if (diffMinutes > 0) return `${diffMinutes}分钟前`
  return '刚刚'
})

// 工具函数
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getFileType = (filename: string): string => {
  const ext = filename.split('.').pop()?.toLowerCase()
  const typeMap: { [key: string]: string } = {
    'json': '配置文件',
    'txt': '文本文件',
    'log': '日志文件',
    'vscdb': '数据库文件',
    'db': '数据库文件'
  }
  return typeMap[ext || ''] || '数据文件'
}

const getStatusText = (status: string): string => {
  const statusMap: { [key: string]: string } = {
    'normal': '正常',
    'error': '错误',
    'missing': '缺失'
  }
  return statusMap[status] || status
}

const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch {
    return dateString
  }
}

// 查看文件功能
const viewFile = (file: any) => {
  logStore.addInfo(`查看文件: ${file.name}`, 'DataManagement')
  appStore.showInfo(`文件路径: ${file.path}`)
}

const exportDataFiles = async () => {
  try {
    const dataStr = JSON.stringify(dataFiles.value, null, 2)
    const blob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `data_files_${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)
    appStore.showSuccess('数据文件列表导出成功！')
  } catch (error) {
    appStore.showError(`导出失败: ${(error as Error).message}`)
  }
}

// 删除重复的方法，使用上面定义的refreshAllData

const exportAllData = async () => {
  try {
    const allData = {
      accounts: accountData.value,
      dataFiles: dataFiles.value,
      exportTime: new Date().toISOString(),
      version: '1.0.0'
    }

    const dataStr = JSON.stringify(allData, null, 2)
    const blob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `cursor_pro_data_${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)
    appStore.showSuccess('所有数据导出成功！')
  } catch (error) {
    appStore.showError(`导出失败: ${(error as Error).message}`)
  }
}

const importData = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'
  input.onchange = async (e: any) => {
    const file = e.target.files[0]
    if (!file) return

    try {
      const text = await file.text()
      const data = JSON.parse(text)

      if (data.accounts) {
        accountData.value = data.accounts
      }
      if (data.dataFiles) {
        dataFiles.value = data.dataFiles
      }

      appStore.showSuccess('数据导入成功！')
      logStore.addSuccess('数据导入完成', 'DataManagement')
    } catch (error) {
      appStore.showError(`导入失败: ${(error as Error).message}`)
    }
  }
  input.click()
}

// 删除重复的函数定义，使用上面的computed版本

// 初始化 - 页面加载时刷新数据
onMounted(async () => {
  logStore.addInfo('数据管理页面初始化中...', 'DataManagement')

  // 页面加载时刷新所有数据
  await refreshAllData()

  logStore.addInfo('数据管理页面初始化完成', 'DataManagement')
})

// 删除重复的函数定义，使用上面定义的版本
</script>

<style scoped>
.data-management-container {
  max-width: 95%;
  width: 100%;
  margin: 0 auto;
  padding: 20px 20px 40px 20px;
  background: transparent;
  min-height: 100vh;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 数据统计背景卡片容器 - 和注册账户列表一样的样式 */
.data-stats-container {
  background: #23272e;
  border-radius: 12px;
  padding: 16px;
  border: 1.5px solid #2d323a;
  margin-bottom: 24px;
}

/* 信息面板背景卡片容器 - 和注册账户列表一样的样式 */
.info-panel-container {
  background: #23272e;
  border-radius: 12px;
  padding: 16px;
  border: 1.5px solid #2d323a;
  margin-bottom: 24px;
  display: flex;
  flex-direction: row;
  gap: 20px;
  width: 100%;
}

/* 顶部导航 */
.top-nav {
  --primary: #ff3e00;
  --primary-hover: #ff6d43;
  --secondary: #4d61ff;
  --secondary-hover: #5e70ff;
  --accent: #00e0b0;
  --text: #050505;
  --bg: #ffffff;
  --shadow-color: #000000;
  --pattern-color: #cfcfcf;

  position: relative;
  background: var(--bg);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  overflow: hidden;
  font-family: ui-sans-serif, system-ui, sans-serif;
  padding: 16px 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  background-image: linear-gradient(to right, rgba(240, 240, 240, 0.5) 1px, transparent 1px),
                    linear-gradient(to bottom, rgba(240, 240, 240, 0.5) 1px, transparent 1px);
  background-size: 20px 20px;
}

.top-nav::before {
  content: "";
  position: absolute;
  inset: 0;
  background-image: linear-gradient(
      to right,
      rgba(0, 0, 0, 0.05) 1px,
      transparent 1px
    ),
    linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
  background-size: 0.5em 0.5em;
  pointer-events: none;
  opacity: 0.5;
  transition: opacity 0.4s ease;
  z-index: 1;
}

.top-nav:hover::before {
  opacity: 1;
}

.nav-title {
  position: relative;
  font-size: 1.3em;
  font-weight: 700;
  color: var(--text);
  letter-spacing: 0.05em;
  z-index: 2;
  flex-shrink: 0;
  margin-right: 20px;
  white-space: nowrap;
}

.nav-actions {
  position: relative;
  display: flex;
  align-items: center;
  gap: 10px;
  z-index: 2;
  flex-wrap: wrap;
  justify-content: flex-end;
}

/* 按钮基础样式 - 日志风格 */
.btn-refresh, .btn-backup, .btn-export, .btn-import {
  position: relative;
  transition: all 0.3s ease-in-out;
  box-shadow: 0px 6px 15px rgba(0, 0, 0, 0.3);
  padding: 8px 16px;
  background-color: #475569;
  color: #ffffff;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.8rem;
  cursor: pointer;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  display: flex;
  align-items: center;
  gap: 6px;
  white-space: nowrap;
  overflow: hidden;
}

.btn-refresh::before, .btn-backup::before, .btn-export::before, .btn-import::before {
  content: "";
  position: absolute;
  width: 100px;
  height: 100%;
  background-image: linear-gradient(
    120deg,
    transparent 30%,
    rgba(255, 255, 255, 0.8),
    transparent 70%
  );
  top: 0;
  left: -100px;
  opacity: 0.6;
}

.btn-refresh:hover, .btn-backup:hover, .btn-export:hover, .btn-import:hover {
  transform: scale(1.05);
  border-color: rgba(255, 255, 255, 0.4);
  animation: shake 0.6s ease-in-out;
}

.btn-refresh:hover::before, .btn-backup:hover::before, .btn-export:hover::before, .btn-import:hover::before {
  animation: shine 1.5s ease-out infinite;
}

/* 不同按钮的颜色 */
.btn-refresh {
  background-color: #3b82f6;
  box-shadow: 0px 8px 20px rgba(59, 130, 246, 0.4);
}

.btn-backup {
  background-color: #10b981;
  box-shadow: 0px 8px 20px rgba(16, 185, 129, 0.4);
}

.btn-export {
  background-color: #f59e0b;
  box-shadow: 0px 8px 20px rgba(245, 158, 11, 0.4);
}

.btn-import {
  background-color: #06b6d4;
  box-shadow: 0px 8px 20px rgba(6, 182, 212, 0.4);
}

.btn-refresh:disabled {
  background-color: #6b7280;
  box-shadow: 0px 6px 15px rgba(0, 0, 0, 0.2);
  cursor: not-allowed;
  transform: none;
  opacity: 0.6;
}

.btn-refresh:disabled:hover {
  transform: none;
  animation: none;
}







/* 图标样式 */
.icon-refresh, .icon-backup, .icon-export, .icon-import {
  width: 14px;
  height: 14px;
  transition: transform 0.3s ease;
}

.icon-refresh.spinning {
  animation: spin 1s linear infinite;
}

/* 动画效果 */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes shake {
  0%, 100% { transform: scale(1.05) translateX(0); }
  25% { transform: scale(1.05) translateX(-2px); }
  75% { transform: scale(1.05) translateX(2px); }
}

@keyframes shine {
  0% { left: -100px; }
  100% { left: 100%; }
}

/* 主要布局 */
.main-layout {
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: auto auto 1fr;
  gap: 20px;
  min-height: calc(100vh - 120px);
  padding-bottom: 40px;
  grid-template-areas:
    "stats-bar"
    "actions"
    "content";
}

/* 顶部统计栏 */
.stats-bar {
  --primary: #ff3e00;
  --primary-hover: #ff6d43;
  --secondary: #4d61ff;
  --secondary-hover: #5e70ff;
  --accent: #00e0b0;
  --text: #050505;
  --bg: #ffffff;
  --shadow-color: #000000;
  --pattern-color: #cfcfcf;

  position: relative;
  grid-area: stats-bar;
  background: var(--bg);
  border: 0.35em solid var(--text);
  border-radius: 0.6em;
  box-shadow: 0.7em 0.7em 0 var(--shadow-color), inset 0 0 0 0.15em rgba(0, 0, 0, 0.05);
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  overflow: hidden;
  font-family: ui-sans-serif, system-ui, sans-serif;
  transform-origin: center;
  padding: 1.4em;
}

.stats-bar::before {
  content: "";
  position: absolute;
  inset: 0;
  background-image: linear-gradient(
      to right,
      rgba(0, 0, 0, 0.05) 1px,
      transparent 1px
    ),
    linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
  background-size: 0.5em 0.5em;
  pointer-events: none;
  opacity: 0.5;
  transition: opacity 0.4s ease;
  z-index: 1;
}

.stats-bar:hover::before {
  opacity: 1;
}

.stats-bar h3 {
  position: relative;
  margin-bottom: 1.5em;
  color: var(--text);
  font-size: 1.2em;
  font-weight: 800;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  z-index: 2;
}

.stats-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.stat-card {
  --primary: #ff3e00;
  --primary-hover: #ff6d43;
  --secondary: #4d61ff;
  --secondary-hover: #5e70ff;
  --accent: #00e0b0;
  --text: #050505;
  --bg: #ffffff;
  --shadow-color: #000000;
  --pattern-color: #cfcfcf;

  position: relative;
  background: var(--bg);
  border: 0.35em solid var(--text);
  border-radius: 0.6em;
  box-shadow: 0.7em 0.7em 0 var(--shadow-color), inset 0 0 0 0.15em rgba(0, 0, 0, 0.05);
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  overflow: hidden;
  font-family: ui-sans-serif, system-ui, sans-serif;
  transform-origin: center;
  text-align: center;
  padding: 20px;
  cursor: pointer;
}

.stat-card:hover {
  transform: translate(-0.4em, -0.4em) scale(1.02);
  box-shadow: 1em 1em 0 var(--shadow-color);
}

.stat-card:active {
  transform: translate(0.1em, 0.1em) scale(0.98);
  box-shadow: 0.5em 0.5em 0 var(--shadow-color);
}

.stat-card::before {
  content: "";
  position: absolute;
  inset: 0;
  background-image: linear-gradient(
      to right,
      rgba(0, 0, 0, 0.05) 1px,
      transparent 1px
    ),
    linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
  background-size: 0.5em 0.5em;
  pointer-events: none;
  opacity: 0.5;
  transition: opacity 0.4s ease;
  z-index: 1;
}

.stat-card:hover::before {
  opacity: 1;
}

.card-icon {
  position: relative;
  font-size: 2em;
  margin-bottom: 0.5em;
  z-index: 2;
}

.stat-number {
  position: relative;
  font-size: 1.8em;
  font-weight: 800;
  color: var(--text);
  margin-bottom: 8px;
  z-index: 2;
}

.stat-number::before {
  content: "";
  position: absolute;
  bottom: 0.15em;
  left: 0;
  width: 100%;
  height: 0.2em;
  background: var(--accent);
  z-index: -1;
  opacity: 0.5;
}

.stat-label {
  position: relative;
  font-size: 0.85em;
  font-weight: 600;
  color: var(--text);
  z-index: 2;
}

.card-corner-accent {
  position: absolute;
  width: 1.5em;
  height: 1.5em;
  background: var(--secondary);
  border: 0.1em solid var(--text);
  border-radius: 0.2em;
  transform: rotate(45deg);
  bottom: -0.7em;
  right: 1em;
  z-index: 0;
  transition: transform 0.3s ease;
}

.stat-card:hover .card-corner-accent {
  transform: rotate(55deg) scale(1.1);
}

/* 主内容区域 */
.content {
  --primary: #ff3e00;
  --primary-hover: #ff6d43;
  --secondary: #4d61ff;
  --secondary-hover: #5e70ff;
  --accent: #00e0b0;
  --text: #050505;
  --bg: #ffffff;
  --shadow-color: #000000;
  --pattern-color: #cfcfcf;

  position: relative;
  grid-area: content;
  background: var(--bg);
  border: 0.35em solid var(--text);
  border-radius: 0.6em;
  box-shadow: 0.7em 0.7em 0 var(--shadow-color), inset 0 0 0 0.15em rgba(0, 0, 0, 0.05);
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  overflow: hidden;
  font-family: ui-sans-serif, system-ui, sans-serif;
  transform-origin: center;
  padding: 1.5em;
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 400px;
  max-width: 100%; /* 限制内容区域最大宽度 */
}

.content::before {
  content: "";
  position: absolute;
  inset: 0;
  background-image: linear-gradient(
      to right,
      rgba(0, 0, 0, 0.05) 1px,
      transparent 1px
    ),
    linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
  background-size: 0.5em 0.5em;
  pointer-events: none;
  opacity: 0.5;
  transition: opacity 0.4s ease;
  z-index: 1;
}

.content:hover::before {
  opacity: 1;
}

.content h3 {
  position: relative;
  margin-bottom: 1.5em;
  color: var(--text);
  font-size: 1.2em;
  font-weight: 800;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  z-index: 2;
}

.content h3 span {
  color: rgba(5, 5, 5, 0.6);
  font-weight: 600;
  font-size: 0.85em;
}

.content-accent-shape {
  position: absolute;
  width: 2.5em;
  height: 2.5em;
  background: var(--secondary);
  border: 0.15em solid var(--text);
  border-radius: 0.3em;
  transform: rotate(45deg);
  bottom: -1.2em;
  right: 2em;
  z-index: 0;
  transition: transform 0.3s ease;
}

.content:hover .content-accent-shape {
  transform: rotate(55deg) scale(1.1);
}

.content-corner-slice {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 1.5em;
  height: 1.5em;
  background: var(--bg);
  border-right: 0.25em solid var(--text);
  border-top: 0.25em solid var(--text);
  border-radius: 0 0.5em 0 0;
  z-index: 1;
}

.content-dots-pattern {
  position: absolute;
  bottom: 2em;
  left: -2em;
  width: 8em;
  height: 4em;
  opacity: 0.3;
  transform: rotate(-10deg);
  pointer-events: none;
  z-index: 1;
}

/* 表格容器 */
.table-container {
  position: relative;
  flex: 1;
  overflow: auto; /* 允许水平和垂直滚动 */
  margin-top: 1.5em;
  margin-bottom: 1.5em;
  min-height: 0;
  padding-bottom: 1.5em;
  z-index: 2;
  max-width: 100%; /* 限制最大宽度 */
}

/* 表格样式 */
.data-table {
  width: 100%;
  min-width: 800px; /* 设置最小宽度，确保表格内容不会过度压缩 */
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 2em;
  border: 0.15em solid var(--text);
  border-radius: 0.6em;
  overflow: hidden;
  box-shadow: 0.3em 0.3em 0 rgba(0, 0, 0, 0.1);
}

.data-table th,
.data-table td {
  padding: 0.8em;
  text-align: left;
  border-bottom: 0.1em solid rgba(0, 0, 0, 0.1);
  position: relative;
}

.data-table td {
  color: var(--text);
  font-weight: 500;
  font-size: 0.9em;
}

.file-name-cell {
  display: flex;
  align-items: center;
  gap: 0.5em;
  color: var(--text);
  font-weight: 600;
}

.file-name-cell svg {
  color: var(--secondary);
  flex-shrink: 0;
}

.data-table th {
  background: var(--primary);
  color: var(--bg);
  font-weight: 800;
  font-size: 0.85em;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.data-table th::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: repeating-linear-gradient(
    45deg,
    rgba(0, 0, 0, 0.1),
    rgba(0, 0, 0, 0.1) 0.5em,
    transparent 0.5em,
    transparent 1em
  );
  pointer-events: none;
  opacity: 0.3;
}

.data-table tr:hover td {
  background: rgba(77, 97, 255, 0.05);
  transform: scale(1.01);
  color: var(--text);
}

.data-table tr:hover .file-name-cell {
  color: var(--text);
}

.data-table tr {
  transition: all 0.2s ease;
}

.status-badge {
  padding: 0.3em 0.6em;
  border: 0.1em solid var(--text);
  border-radius: 0.3em;
  font-size: 0.7em;
  font-weight: 700;
  background: var(--accent);
  color: var(--text);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  box-shadow: 0.15em 0.15em 0 rgba(0, 0, 0, 0.2);
}

.table-btn {
  position: relative;
  background: var(--secondary);
  color: var(--bg);
  font-size: 0.75em;
  font-weight: 700;
  padding: 0.5em 0.8em;
  border: 0.15em solid var(--text);
  border-radius: 0.3em;
  box-shadow: 0.2em 0.2em 0 var(--shadow-color);
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%
  );
  transition: left 0.6s ease;
}

.table-btn:hover {
  background: var(--secondary-hover);
  transform: translate(-0.1em, -0.1em);
  box-shadow: 0.3em 0.3em 0 var(--shadow-color);
}

.table-btn:hover::before {
  left: 100%;
}

.table-btn:active {
  transform: translate(0.1em, 0.1em);
  box-shadow: 0.1em 0.1em 0 var(--shadow-color);
}

/* 右侧操作面板 */
.actions {
  grid-area: actions;
  display: flex;
  flex-direction: row;
  gap: 20px;
  align-self: start;
}

.action-panel {
  --primary: #ff3e00;
  --primary-hover: #ff6d43;
  --secondary: #4d61ff;
  --secondary-hover: #5e70ff;
  --accent: #00e0b0;
  --text: #050505;
  --bg: #ffffff;
  --shadow-color: #000000;
  --pattern-color: #cfcfcf;

  position: relative;
  background: var(--bg);
  border: 0.35em solid var(--text);
  border-radius: 0.6em;
  box-shadow: 0.7em 0.7em 0 var(--shadow-color), inset 0 0 0 0.15em rgba(0, 0, 0, 0.05);
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  overflow: hidden;
  font-family: ui-sans-serif, system-ui, sans-serif;
  transform-origin: center;
  padding: 1.4em;
  flex: 1;
}

.action-panel::before {
  content: "";
  position: absolute;
  inset: 0;
  background-image: linear-gradient(
      to right,
      rgba(0, 0, 0, 0.05) 1px,
      transparent 1px
    ),
    linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
  background-size: 0.5em 0.5em;
  pointer-events: none;
  opacity: 0.5;
  transition: opacity 0.4s ease;
  z-index: 1;
}

.action-panel:hover::before {
  opacity: 1;
}

.action-panel:hover {
  transform: translate(-0.4em, -0.4em) scale(1.02);
  box-shadow: 1em 1em 0 var(--shadow-color);
}

.panel-title {
  position: relative;
  font-size: 1.2em;
  font-weight: 800;
  margin-bottom: 1.5em;
  color: var(--text);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  z-index: 2;
}

.panel-accent-shape {
  position: absolute;
  width: 2em;
  height: 2em;
  background: var(--secondary);
  border: 0.1em solid var(--text);
  border-radius: 0.2em;
  transform: rotate(45deg);
  bottom: -1em;
  right: 1.5em;
  z-index: 0;
  transition: transform 0.3s ease;
}

.action-panel:hover .panel-accent-shape {
  transform: rotate(55deg) scale(1.1);
}

.panel-corner-slice {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 1em;
  height: 1em;
  background: var(--bg);
  border-right: 0.2em solid var(--text);
  border-top: 0.2em solid var(--text);
  border-radius: 0 0.3em 0 0;
  z-index: 1;
}

/* 信息展示样式 */
.account-info,
.storage-info {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 1em;
  z-index: 2;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.6em 0;
  border-bottom: 0.15em dashed rgba(0, 0, 0, 0.15);
  transition: transform 0.2s ease;
}

.info-item:hover {
  transform: translateX(0.3em);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 0.85em;
  font-weight: 600;
  color: var(--text);
}

.info-value {
  font-size: 0.85em;
  font-weight: 700;
  color: var(--secondary);
  background: var(--bg);
  padding: 0.2em 0.5em;
  border: 0.12em solid var(--text);
  border-radius: 0.3em;
  box-shadow: 0.2em 0.2em 0 rgba(0, 0, 0, 0.2);
}

/* 存储进度条 */
.storage-item {
  position: relative;
  margin-bottom: 1.5em;
  z-index: 2;
}

.storage-label {
  font-size: 0.85em;
  font-weight: 600;
  color: var(--text);
  margin-bottom: 0.8em;
}

.storage-bar {
  width: 100%;
  height: 1em;
  background: rgba(0, 0, 0, 0.1);
  border: 0.15em solid var(--text);
  border-radius: 0.5em;
  overflow: hidden;
  margin-bottom: 0.5em;
  box-shadow: inset 0.2em 0.2em 0.4em rgba(0, 0, 0, 0.1);
}

.storage-progress {
  height: 100%;
  background: linear-gradient(90deg, var(--secondary), var(--secondary-hover));
  border-radius: 0.3em;
  transition: width 0.6s cubic-bezier(0.23, 1, 0.32, 1);
  position: relative;
  overflow: hidden;
}

.storage-progress::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%
  );
  animation: shine 2s infinite;
}

.storage-text {
  font-size: 0.7em;
  font-weight: 600;
  color: rgba(5, 5, 5, 0.6);
  text-align: center;
}

@keyframes shine {
  0% {
    left: -100%;
  }
  60% {
    left: 100%;
  }
  to {
    left: 100%;
  }
}

/* 动画关键帧 */
@keyframes shine {
  0% {
    left: -100px;
  }
  60% {
    left: 100%;
  }
  to {
    left: 100%;
  }
}

@keyframes shake {
  0%, 100% {
    transform: scale(1.05) translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: scale(1.05) translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: scale(1.05) translateX(2px);
  }
}

/* 图标样式 */
.icon-refresh::before { content: "🔄"; }
.icon-export::before { content: "📤"; }
.icon-import::before { content: "📥"; }
.icon-backup::before { content: "💾"; }

.icon-backup {
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

/* 备份管理模态框样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.backup-manager-modal {
  background: #1a1a2e;
  border-radius: 16px;
  width: 80%;
  max-width: 800px;
  max-height: 70vh;
  overflow: hidden;
  border: 1px solid #374151;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.5);
}

.backup-manager-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #374151;
  background: #16213e;
}

.backup-manager-modal .modal-header h2 {
  margin: 0;
  color: #60a5fa;
  font-size: 1.4rem;
  font-weight: 600;
}

.backup-manager-modal .close-button {
  background: none;
  border: none;
  color: #94a3b8;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.backup-manager-modal .close-button:hover {
  background: #374151;
  color: #e2e8f0;
}

.backup-manager-modal .close-button svg {
  width: 20px;
  height: 20px;
  stroke: currentColor;
  stroke-width: 2;
}

.backup-manager-modal .modal-content {
  padding: 0;
  max-height: calc(70vh - 80px);
  overflow-y: auto;
}

/* 响应式 */
@media (max-width: 1024px) {
  .main-layout {
    grid-template-columns: 1fr;
    grid-template-areas:
      "stats-bar"
      "actions"
      "content";
    height: auto;
  }

  .stats-row {
    grid-template-columns: 1fr;
  }

  .stat-card {
    min-width: 120px;
  }
}

@media (max-width: 768px) {
  .nav-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .data-table {
    font-size: 12px;
    min-width: 600px; /* 在小屏幕上减少最小宽度 */
  }

  .data-table th,
  .data-table td {
    padding: 6px;
  }

  .content {
    padding: 1em; /* 减少内边距 */
  }

  .table-container {
    margin-top: 1em;
    margin-bottom: 1em;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  .data-table {
    font-size: 11px;
    min-width: 500px; /* 进一步减少最小宽度 */
  }

  .data-table th,
  .data-table td {
    padding: 4px;
  }

  .content {
    padding: 0.8em;
  }
}


</style>
