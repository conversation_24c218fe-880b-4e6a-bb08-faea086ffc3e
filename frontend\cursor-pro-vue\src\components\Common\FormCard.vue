<template>
  <form class="form">
    <div id="circleform"></div>
    <div id="introform">
      <p>{{ title }}</p>
      <div id="introformbehind">
        <p>||||||||||||||||||||||||||||||||||||</p>
      </div>
    </div>
    <div id="middleform">
      <slot></slot>
    </div>
  </form>
</template>

<script setup lang="ts">
interface Props {
  title: string
}

const props = defineProps<Props>()
</script>

<style scoped>
.form {
  --background: #006aaaff;
  --color1: #ffbf6a;
  --color2: #ff7c90ff;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 20em;
  background-color: var(--background);
  border: 5px solid var(--color1);
  position: relative;
  overflow: hidden;
  box-shadow: -0.5em 0em var(--color2);
  transition: all 1s ease;
  margin-bottom: 1.5rem;
}

.form input {
  position: relative;
  z-index: 2;
}

.form:hover {
  transform: scale(1.02);
  margin-bottom: 1em;
}

#circleform {
  width: 4.5em;
  height: 4.5em;
  border-bottom-right-radius: 50%;
  background-color: var(--color1);
  position: absolute;
  top: -2.5em;
  left: -2.5em;
  z-index: 1;
  box-shadow:
    0.25em 0.25em 0em 1em var(--background),
    0.25em 0.25em 0em 1.5em var(--color2);
}

#introform {
  width: 100%;
  height: 4em;
  background-color: var(--color1);
  color: blue;
  font-size: 20px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  position: relative;
  z-index: 0;
}

#introformbehind {
  width: 100%;
  height: 3em;
  color: blue;
  font-size: 20px;
  top: -1em;
  position: absolute;
  z-index: -1;
  color: var(--color2);
  animation: translateform 4s infinite linear;
}

@keyframes translateform {
  0% {
    transform: translateX(-2%);
    top: -1.5em;
  }
  5% {
    top: -1em;
  }
  95% {
    top: -1em;
  }
  100% {
    top: -1.5em;
    transform: translateX(25%);
  }
}

#introformbehind p {
  color: var(--color2);
  text-shadow: 0px 0px;
}

.form p {
  color: white;
  font-weight: bold;
  font-family: "Times New Roman", serif;
  padding-right: 0.25em;
  text-shadow: 0em 0.1em 0em var(--color2);
  font-family:
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    "Open Sans",
    "Helvetica Neue",
    sans-serif;
  font-size: 1.15em;
}

#middleform {
  width: 100%;
  flex: 1;
  background-color: var(--color1);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding: 1.5rem 0.25em 1.5rem 0.25em;
  gap: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form {
    min-height: 18em;
  }

  #introform {
    font-size: 18px;
  }

  #middleform {
    padding: 1rem 0.25em;
    gap: 0.8rem;
  }
}
</style>
