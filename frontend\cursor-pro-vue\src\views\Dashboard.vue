<template>
  <div class="content-section">
    <!-- 账号信息卡片 -->
    <div class="shake-container">
      <!-- 鼠标跟踪网格 -->
      <div class="tracker tr-1"></div>
      <div class="tracker tr-2"></div>
      <div class="tracker tr-3"></div>
      <div class="tracker tr-4"></div>
      <div class="tracker tr-5"></div>
      <div class="tracker tr-6"></div>
      <div class="tracker tr-7"></div>
      <div class="tracker tr-8"></div>
      <div class="tracker tr-9"></div>
      <div class="tracker tr-10"></div>
      <div class="tracker tr-11"></div>
      <div class="tracker tr-12"></div>
      <div class="tracker tr-13"></div>
      <div class="tracker tr-14"></div>
      <div class="tracker tr-15"></div>
      <div class="tracker tr-16"></div>
      <div class="tracker tr-17"></div>
      <div class="tracker tr-18"></div>
      <div class="tracker tr-19"></div>
      <div class="tracker tr-20"></div>
      <div class="tracker tr-21"></div>
      <div class="tracker tr-22"></div>
      <div class="tracker tr-23"></div>
      <div class="tracker tr-24"></div>
      <div class="tracker tr-25"></div>

      <div class="account-card" id="shake-card">
        <!-- 顶部装饰条 -->
        <div class="top-decoration"></div>

        <!-- 装饰点 -->
        <div class="dots-decoration">
          <div class="dot"></div>
          <div class="dot"></div>
        </div>

        <!-- 卡片头部 -->
        <div class="card-header-new">
          <div class="email-new">{{ accountInfo.email }}</div>
          <div class="plan-badge-new">{{ accountInfo.plan }} 订阅</div>
        </div>

        <!-- 底部指标 - Kawaii滑块版本 -->
        <div class="kawaii-sliders-container">
          <!-- 剩余天数 -->
          <div class="kawaii-slider-item">
            <div class="kawaii-slider-label">
              <span class="kawaii-slider-icon">🔴</span>
              <span class="kawaii-slider-text">剩余天数</span>
              <span class="kawaii-slider-value">{{ accountInfo.trial_days }} 天</span>
            </div>
            <input
              type="range"
              class="kawaii"
              :value="Math.min(accountInfo.trial_days, 30)"
              max="30"
              min="0"
              readonly
              style="--base: #fe8ce4;"
            />
          </div>

          <!-- 高级用量 -->
          <div class="kawaii-slider-item">
            <div class="kawaii-slider-label">
              <span class="kawaii-slider-icon">🔵</span>
              <span class="kawaii-slider-text">高级用量</span>
              <span class="kawaii-slider-value">{{ accountInfo.pro_used }} / {{ accountInfo.pro_total }}</span>
            </div>
            <input
              type="range"
              class="kawaii"
              :value="accountInfo.pro_percent"
              max="100"
              min="0"
              readonly
              style="--base: #8cc8e4;"
            />
          </div>

          <!-- 基础功能 -->
          <div class="kawaii-slider-item">
            <div class="kawaii-slider-label">
              <span class="kawaii-slider-icon">🟢</span>
              <span class="kawaii-slider-text">基础功能</span>
              <span class="kawaii-slider-value">无限制</span>
            </div>
            <input
              type="range"
              class="kawaii"
              value="100"
              max="100"
              min="0"
              readonly
              style="--base: #6cc484;"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 注册账户列表 -->
    <div class="registered-accounts-section">
      <div class="section-title">
        <span>注册账户列表</span>

        <!-- 小巧备份按钮 -->
        <div class="mini-action-btn backup-mini" @click="showBackups" title="账户备份恢复">
          <div class="mini-icon">
            <div class="folder-mini">
              <div class="folder-front"></div>
              <div class="folder-back"></div>
            </div>
          </div>
          <span>账户备份</span>
          <div class="ripple"></div>
        </div>

        <!-- 小巧保存按钮 -->
        <div class="mini-action-btn save-mini" @click="saveCurrentAccount" :class="{ loading: isSavingAccount }" title="保存当前登录账户">
          <div class="mini-icon">
            <div class="save-disk" :class="{ spinning: isSavingAccount }">
              <div class="disk-hole"></div>
            </div>
          </div>
          <span>{{ isSavingAccount ? '保存中...' : '保存账户' }}</span>
          <div class="ripple"></div>
        </div>
        <button class="star-button" @click="refreshAccounts" :disabled="isRefreshing">
          刷新
          <div class="star-1">
            <svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" version="1.1" style="shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality; fill-rule:evenodd; clip-rule:evenodd" viewBox="0 0 784.11 815.53" xmlns:xlink="http://www.w3.org/1999/xlink">
              <defs></defs>
              <g id="Layer_x0020_1">
                <metadata id="CorelCorpID_0Corel-Layer"></metadata>
                <path class="fil0" d="M392.05 0c-20.9,210.08 -184.06,378.41 -392.05,407.78 207.96,29.37 371.12,197.68 392.05,407.74 20.93,-210.06 184.09,-378.37 392.05,-407.74 -207.98,-29.38 -371.16,-197.69 -392.06,-407.78z"></path>
              </g>
            </svg>
          </div>
          <div class="star-2">
            <svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" version="1.1" style="shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality; fill-rule:evenodd; clip-rule:evenodd" viewBox="0 0 784.11 815.53" xmlns:xlink="http://www.w3.org/1999/xlink">
              <defs></defs>
              <g id="Layer_x0020_1">
                <metadata id="CorelCorpID_0Corel-Layer"></metadata>
                <path class="fil0" d="M392.05 0c-20.9,210.08 -184.06,378.41 -392.05,407.78 207.96,29.37 371.12,197.68 392.05,407.74 20.93,-210.06 184.09,-378.37 392.05,-407.74 -207.98,-29.38 -371.16,-197.69 -392.06,-407.78z"></path>
              </g>
            </svg>
          </div>
          <div class="star-3">
            <svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" version="1.1" style="shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality; fill-rule:evenodd; clip-rule:evenodd" viewBox="0 0 784.11 815.53" xmlns:xlink="http://www.w3.org/1999/xlink">
              <defs></defs>
              <g id="Layer_x0020_1">
                <metadata id="CorelCorpID_0Corel-Layer"></metadata>
                <path class="fil0" d="M392.05 0c-20.9,210.08 -184.06,378.41 -392.05,407.78 207.96,29.37 371.12,197.68 392.05,407.74 20.93,-210.06 184.09,-378.37 392.05,-407.74 -207.98,-29.38 -371.16,-197.69 -392.06,-407.78z"></path>
              </g>
            </svg>
          </div>
          <div class="star-4">
            <svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" version="1.1" style="shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality; fill-rule:evenodd; clip-rule:evenodd" viewBox="0 0 784.11 815.53" xmlns:xlink="http://www.w3.org/1999/xlink">
              <defs></defs>
              <g id="Layer_x0020_1">
                <metadata id="CorelCorpID_0Corel-Layer"></metadata>
                <path class="fil0" d="M392.05 0c-20.9,210.08 -184.06,378.41 -392.05,407.78 207.96,29.37 371.12,197.68 392.05,407.74 20.93,-210.06 184.09,-378.37 392.05,-407.74 -207.98,-29.38 -371.16,-197.69 -392.06,-407.78z"></path>
              </g>
            </svg>
          </div>
          <div class="star-5">
            <svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" version="1.1" style="shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality; fill-rule:evenodd; clip-rule:evenodd" viewBox="0 0 784.11 815.53" xmlns:xlink="http://www.w3.org/1999/xlink">
              <defs></defs>
              <g id="Layer_x0020_1">
                <metadata id="CorelCorpID_0Corel-Layer"></metadata>
                <path class="fil0" d="M392.05 0c-20.9,210.08 -184.06,378.41 -392.05,407.78 207.96,29.37 371.12,197.68 392.05,407.74 20.93,-210.06 184.09,-378.37 392.05,-407.74 -207.98,-29.38 -371.16,-197.69 -392.06,-407.78z"></path>
              </g>
            </svg>
          </div>
          <div class="star-6">
            <svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" version="1.1" style="shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality; fill-rule:evenodd; clip-rule:evenodd" viewBox="0 0 784.11 815.53" xmlns:xlink="http://www.w3.org/1999/xlink">
              <defs></defs>
              <g id="Layer_x0020_1">
                <metadata id="CorelCorpID_0Corel-Layer"></metadata>
                <path class="fil0" d="M392.05 0c-20.9,210.08 -184.06,378.41 -392.05,407.78 207.96,29.37 371.12,197.68 392.05,407.74 20.93,-210.06 184.09,-378.37 392.05,-407.74 -207.98,-29.38 -371.16,-197.69 -392.06,-407.78z"></path>
              </g>
            </svg>
          </div>
        </button>
      </div>
      <div class="registered-accounts-container">
        <!-- 加载动画 -->
        <div v-if="isRefreshing" class="loading-container">
          <div class="loading-wrapper">
            <div class="circle"></div>
            <div class="circle"></div>
            <div class="circle"></div>
            <div class="shadow"></div>
            <div class="shadow"></div>
            <div class="shadow"></div>
          </div>
          <div class="loading-text">正在刷新账户列表...</div>
        </div>

        <!-- 无账户状态 -->
        <div v-else-if="registeredAccounts.length === 0" class="no-accounts">
          <div class="no-accounts-icon">空</div>
          <div class="no-accounts-text">暂无注册账户</div>
          <div class="no-accounts-hint">使用"一键自动注册"创建新账户</div>
        </div>

        <!-- 账户列表 -->
        <div v-else-if="!isRefreshing">
          <div
            v-for="(account, index) in registeredAccounts"
            :key="index"
            class="account-item"
            :data-account-index="index"
          >
            <!-- 账户头部信息 -->
            <div class="account-header">
              <div class="account-number">#{{ index + 1 }}</div>

              <!-- 账户信息组 -->
              <div class="account-info-group">
                <div class="account-info-title">✉️ 账户信息</div>
                <div class="account-info-content account-email" v-html="formatEmail(account.email)"></div>
                <div class="account-info-content register-time">
                  {{ account.source === 'saved' ? '保存时间' : '注册时间' }}: {{ account.saved_time || account.registeredAt || '未知' }}
                </div>
                <button
                  class="copy-btn"
                  @click="copyText(account.email, $event)"
                  title="复制邮箱"
                >
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                  </svg>
                  复制
                </button>
              </div>

              <!-- 密码信息组 -->
              <div class="account-info-group">
                <div class="account-info-title">登录密码</div>
                <div class="account-info-content account-password">{{ account.password || '未知密码' }}</div>
                <div class="account-status" :class="{ active: isCurrentAccount(account), inactive: !isCurrentAccount(account) }">
                  {{ isCurrentAccount(account) ? 'ACTIVE' : 'INACTIVE' }}
                </div>
                <button
                  class="copy-btn"
                  @click="copyText(account.password || '', $event)"
                  title="复制密码"
                >
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                  </svg>
                  复制
                </button>
              </div>

            </div>

            <!-- 账户详情 -->
            <div class="account-details">
              <div class="account-detail-item">
                <div class="account-detail-label">Token状态</div>
                <div class="account-detail-value">{{ getTokenStatus(account.token) }}</div>
              </div>
              <div class="account-detail-item">
                <div class="account-detail-label">保存位置</div>
                <div class="account-detail-value">cursor_accounts.txt</div>
              </div>
              <div class="account-detail-item">
                <div class="account-detail-label">加密备份</div>
                <div class="account-detail-value">{{ account.hasBackup ? '已备份' : '无备份' }}</div>
              </div>
              <div class="account-detail-item">
                <div class="account-detail-label">机器码</div>
                <div class="account-detail-value">{{ account.machine_ids ? '已配置' : '未配置' }}</div>
              </div>
              <div class="account-detail-item">
                <div class="account-detail-label">使用情况</div>
                <div class="account-detail-value">高级 {{ account.used || '0' }}/{{ account.total || '999' }}</div>
              </div>
              <div class="account-detail-item">
                <div class="account-detail-label">账户类型</div>
                <div class="account-detail-value">{{ account.accountType || 'Free' }}</div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="account-actions">
              <button class="action-button copy-button" @click="copyAccountInfo(account)">
                <svg viewBox="0 0 448 512" class="svgIcon">
                  <path d="M208 0H332.1c12.7 0 24.9 5.1 33.9 14.1l67.9 67.9c9 9 14.1 21.2 14.1 33.9V336c0 26.5-21.5 48-48 48H208c-26.5 0-48-21.5-48-48V48c0-26.5 21.5-48 48-48zM48 128h80v64H64V448H256V416h64v48c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V176c0-26.5 21.5-48 48-48z"></path>
                </svg>
              </button>

              <button
                v-if="!account.isActive"
                class="action-button switch-button"
                @click="switchAccount(account)"
              >
                <svg viewBox="0 0 448 512" class="svgIcon">
                  <path d="M438.6 278.6c12.5-12.5 12.5-32.8 0-45.3l-160-160c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L338.8 224 32 224c-17.7 0-32 14.3-32 32s14.3 32 32 32l306.7 0L233.4 393.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l160-160z"></path>
                </svg>
              </button>

              <button class="action-button delete-button" @click="deleteAccount(account)">
                <svg viewBox="0 0 448 512" class="svgIcon">
                  <path d="M135.2 17.7L128 32H32C14.3 32 0 46.3 0 64S14.3 96 32 96H416c17.7 0 32-14.3 32-32s-14.3-32-32-32H320l-7.2-14.3C307.4 6.8 296.3 0 284.2 0H163.8c-12.1 0-23.2 6.8-28.6 17.7zM416 128H32L53.2 467c1.6 25.3 22.6 45 47.9 45H346.9c25.3 0 46.3-19.7 47.9-45L416 128z"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 现代化确认对话框 -->
  <ModernConfirmDialog
    :visible="confirmDialog.visible"
    :type="confirmDialog.type"
    :title="confirmDialog.title"
    :message="confirmDialog.message"
    :account-info="confirmDialog.accountInfo"
    :warnings="confirmDialog.warnings"
    @confirm="handleConfirmDialogConfirm"
    @cancel="handleConfirmDialogCancel"
  />



  <!-- 账户备份恢复对话框 -->
  <BaseModal
    :visible="showBackupDialog"
    title="恢复已删除的账户"
    width="700px"
    @close="showBackupDialog = false"
  >
    <div class="backup-content">
      <div v-if="loadingBackupAccounts" class="loading-text">
        正在获取备份账户列表...
      </div>

      <div v-else-if="backupAccounts.length === 0" class="no-backups">
        <p>暂无已删除的账户</p>
        <p class="hint">删除账户时会自动备份到此处，可以随时恢复</p>
      </div>

      <div v-else class="backup-accounts-list">
        <div class="backup-accounts-header">
          <h4>可恢复的账户 ({{ backupAccounts.length }}个)</h4>
          <p class="hint">选择要恢复的账户，恢复后将重新添加到账户列表中</p>
        </div>

        <div v-for="account in backupAccounts" :key="account.email" class="backup-account-item">
          <div class="account-info">
            <div class="account-email">{{ account.email }}</div>
            <div class="account-details">
              <span v-if="account.plan" class="account-plan">{{ account.plan }}</span>
              <span v-if="account.usage_limit" class="account-usage">{{ account.usage_limit }}</span>
              <span v-if="account.token && account.token !== '需要手动获取Token'" class="account-token-status">已有Token</span>
              <span v-else class="account-token-status no-token">需要获取Token</span>
            </div>
          </div>
          <div class="account-actions">
            <button
              class="card__button card__button--confirm"
              @click="restoreAccountFromBackup(account.email)"
              title="恢复此账户"
            >
              恢复账户
            </button>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <button class="card__button card__button--cancel" @click="showBackupDialog = false">取消</button>
      <button class="card__button card__button--confirm" @click="getBackupAccountList" :disabled="loadingBackupAccounts">
        {{ loadingBackupAccounts ? '获取中...' : '刷新列表' }}
      </button>
    </template>
  </BaseModal>
</template>

<script setup lang="ts">
import { ref, onMounted, onActivated, onDeactivated } from 'vue'
import { realAPI } from '@/api/real-api'
import { useLogStore } from '@/stores/logs'
import { useAppStore } from '@/stores/app'
import { notification } from '@/composables/useNotification'
import ModernConfirmDialog from '@/components/Common/ModernConfirmDialog.vue'
import BaseModal from '@/components/Modal/BaseModal.vue'

import type { RegisteredAccount } from '@/types'

// 定义组件名称（用于keep-alive缓存）
defineOptions({
  name: 'Dashboard'
})

// 日志store
const logStore = useLogStore()
const appStore = useAppStore()

// 账号信息数据
const accountInfo = ref({
  email: '未登录',
  plan: '-',
  trial_days: 0,
  pro_used: 0,
  pro_total: 999,
  pro_percent: 0,
  basic_total: 'No Limit'
})

// 注册账户列表
const registeredAccounts = ref<RegisteredAccount[]>([])

// 刷新状态
const isRefreshing = ref(false)

// 保存账户状态
const isSavingAccount = ref(false)

// 备份相关状态
const backups = ref<any[]>([])
const backupAccounts = ref<any[]>([])
const showBackupDialog = ref(false)

const loadingBackups = ref(false)
const loadingBackupAccounts = ref(false)

// 确认对话框状态
const confirmDialog = ref({
  visible: false,
  type: 'warning' as 'info' | 'warning' | 'danger',
  title: '',
  message: '',
  accountInfo: null as any,
  warnings: [] as string[],
  onConfirm: null as (() => void) | null
})

// 获取账号信息
const getAccountInfo = async () => {
  try {
    // 调用真实API
    const info = await realAPI.getAccountInfo()
    if (info) {
      accountInfo.value = info
      console.log('[Dashboard] 真实API账号信息:', info.email || '未登录')
    }
  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : String(error)

    // 如果是Cursor未登录错误，不抛出异常，只记录一次
    if (errorMsg.includes('未找到Cursor登录token') || errorMsg.includes('请先登录Cursor')) {
      console.log('[Dashboard] Cursor未登录，跳过账户信息获取')
      accountInfo.value = {
        email: "Cursor未登录",
        plan: "请先登录Cursor",
        trial_days: 0,
        pro_used: 0,
        pro_total: 0,
        pro_percent: 0,
        basic_total: "需要登录"
      }
      return // 不抛出异常
    }

    // 其他错误才抛出异常
    console.error('[Dashboard] 获取账号信息失败:', error)
    throw error
  }
}

// 等待API准备好的辅助函数
const waitForAPI = (): Promise<void> => {
  return new Promise((resolve) => {
    // 如果API已经准备好，立即返回
    if ((window as any).pywebview?.api) {
      resolve()
      return
    }

    // 监听API准备好事件
    const onAPIReady = () => {
      window.removeEventListener('api-ready', onAPIReady)
      document.removeEventListener('pywebviewready', onAPIReady)
      resolve()
    }

    // 同时监听两个事件
    window.addEventListener('api-ready', onAPIReady)
    document.addEventListener('pywebviewready', onAPIReady)

    // 定期检查API是否可用
    let checkCount = 0
    const checkInterval = setInterval(() => {
      checkCount++
      if ((window as any).pywebview?.api) {
        clearInterval(checkInterval)
        window.removeEventListener('api-ready', onAPIReady)
        document.removeEventListener('pywebviewready', onAPIReady)
        resolve()
      } else if (checkCount >= 50) { // 5秒超时
        clearInterval(checkInterval)
        window.removeEventListener('api-ready', onAPIReady)
        document.removeEventListener('pywebviewready', onAPIReady)
        resolve()
      }
    }, 100)
  })
}

// 获取注册账户列表
const getRegisteredAccounts = async () => {
  try {
    // 调用真实API
    const accounts = await realAPI.getRegisteredAccounts()
    console.log('[Dashboard] 🔍 API返回的原始数据:', accounts)
    console.log('[Dashboard] 🔍 数据类型:', typeof accounts)
    console.log('[Dashboard] 🔍 是否为数组:', Array.isArray(accounts))

    if (Array.isArray(accounts)) {
      // 对账户进行排序：当前登录账户排在最前面
      const currentEmail = accountInfo.value.email
      const sortedAccounts = accounts.sort((a, b) => {
        // 如果a是当前登录账户，排在前面
        if (a.email === currentEmail) return -1
        // 如果b是当前登录账户，排在前面
        if (b.email === currentEmail) return 1
        // 其他按保存时间排序（最新的在前）
        if (a.saved_time && b.saved_time) {
          return new Date(b.saved_time).getTime() - new Date(a.saved_time).getTime()
        }
        return 0
      })

      registeredAccounts.value = sortedAccounts
      console.log('[Dashboard] ✅ HTTP API注册账户:', accounts.length, '个')
      console.log('[Dashboard] 🔍 账户详情:', sortedAccounts)

      // 如果没有账户，显示提示
      if (accounts.length === 0) {
        notification.info(
          '暂无账户',
          '当前没有注册的账户，请使用"一键自动注册"创建新账户',
          { duration: 4000 }
        )
      }
    } else {
      registeredAccounts.value = []
      console.log('[Dashboard] ❌ HTTP API注册账户: 0 个 (数据不是数组)')

      notification.warning(
        '数据格式异常',
        '服务器返回的数据格式不正确，请联系技术支持',
        { duration: 5000 }
      )
    }
  } catch (error) {
    console.error('[Dashboard] 💥 获取注册账户列表失败:', error)
    registeredAccounts.value = []

    notification.error(
      '获取账户失败',
      '无法连接到服务器，请检查网络连接或稍后重试',
      { duration: 5000 }
    )
  }
}

// 格式化邮箱显示
const formatEmail = (email: string) => {
  if (!email || !email.includes('@')) return email
  const [username, domain] = email.split('@')
  return `${username}@<span class="email-domain">${domain}</span>`
}

// 判断是否为当前登录账户
const isCurrentAccount = (account: any) => {
  return account.email === accountInfo.value.email
}



// 格式化日期
const formatDate = (dateStr?: string) => {
  if (!dateStr) return '未知'
  try {
    return new Date(dateStr).toLocaleDateString('zh-CN')
  } catch {
    return dateStr
  }
}

// 获取状态样式类
const getStatusClass = (account: any) => {
  return account.isActive ? 'status-active' : 'status-inactive'
}

// 获取状态文本
const getStatusText = (account: any) => {
  return account.isActive ? '当前账户' : '备用账户'
}

// 复制文本
const copyText = async (text: string, event: Event) => {
  try {
    await navigator.clipboard.writeText(text)
    const btn = event.target as HTMLElement
    const originalText = btn.textContent
    btn.textContent = '✅ 已复制'
    btn.classList.add('copied')

    setTimeout(() => {
      btn.textContent = originalText
      btn.classList.remove('copied')
    }, 2000)

    // 显示成功提示
    notification.success(
      '复制成功',
      `已复制: ${text.length > 20 ? text.substring(0, 20) + '...' : text}`,
      { duration: 2000 }
    )
  } catch (error) {
    console.error('复制失败:', error)

    // 显示错误提示
    notification.error(
      '复制失败',
      '无法访问剪贴板，请手动复制',
      { duration: 3000 }
    )
  }
}

// 复制账户信息
const copyAccountInfo = async (account: any) => {
  const info = `邮箱: ${account.email}\n密码: ${account.password}\n使用量: ${account.used || 0}/${account.total || 999}`
  try {
    await navigator.clipboard.writeText(info)
    console.log('账户信息已复制')

    // 添加到日志
    logStore.addSuccess(`已复制账户信息: ${account.email}`, 'CopyAccount')

    // 显示成功提示
    notification.success(
      '账户信息已复制',
      `已复制 ${account.email} 的完整信息`,
      { duration: 3000 }
    )
  } catch (error) {
    console.error('复制账户信息失败:', error)

    // 添加到日志
    logStore.addError(`复制账户信息失败: ${account.email} - ${(error as Error).message}`, 'CopyAccount')

    // 显示错误提示
    notification.error(
      '复制失败',
      '无法复制账户信息到剪贴板',
      { duration: 3000 }
    )
  }
}

// 切换账户
const switchAccount = async (account: any) => {
  console.log('切换到账户:', account.email)

  // 显示现代化确认对话框
  confirmDialog.value = {
    visible: true,
    type: 'warning',
    title: '切换账户确认',
    message: '确定要切换到以下账户吗？',
    accountInfo: {
      email: account.email,
      password: account.password
    },
    warnings: [
      '退出当前账户',
      '使用新账户登录',
      '可能需要重启Cursor'
    ],
    onConfirm: () => performAccountSwitch(account)
  }
}

// 执行账户切换
const performAccountSwitch = async (account: any) => {
  confirmDialog.value.visible = false

  try {
    try {
      logStore.addInfo(`开始切换到账户: ${account.email}`, 'SwitchAccount')

      // 显示开始提示
      notification.info(
        '正在切换账户',
        `正在切换到 ${account.email}，请稍候...`,
        { duration: 3000 }
      )

      // 调用后端API切换账户
      const result = await realAPI.switchAccount(account.email, account.password)

      logStore.addSuccess(`账户切换完成: ${result}`, 'SwitchAccount')

      // 显示成功提示
      notification.success(
        '切换成功',
        `已成功切换到账户: ${account.email}`,
        { duration: 4000 }
      )

      // 刷新账户信息
      setTimeout(() => {
        refreshAccounts()
      }, 1000)

    } catch (error) {
      logStore.addError(`切换账户失败: ${(error as Error).message}`, 'SwitchAccount')
      console.error('切换账户失败:', error)

      // 显示错误提示
      notification.error(
        '切换失败',
        `无法切换到账户 ${account.email}: ${(error as Error).message}`,
        { duration: 5000 }
      )
    }
  } catch (error) {
    logStore.addError(`切换账户失败: ${(error as Error).message}`, 'SwitchAccount')
    console.error('切换账户失败:', error)

    // 显示错误提示
    notification.error(
      '切换失败',
      `无法切换到账户 ${account.email}: ${(error as Error).message}`,
      { duration: 5000 }
    )
  }
}

// 确认对话框事件处理
const handleConfirmDialogConfirm = () => {
  if (confirmDialog.value.onConfirm) {
    confirmDialog.value.onConfirm()
  }
}

const handleConfirmDialogCancel = () => {
  confirmDialog.value.visible = false
}

// 删除账户
const deleteAccount = async (account: any) => {
  // 使用统一的确认对话框样式
  confirmDialog.value = {
    visible: true,
    type: 'danger',
    title: '删除账户确认',
    message: `确定要删除以下账户吗？\n\n邮箱: ${account.email}\n注册时间: ${account.registeredAt || '未知'}\n\n警告：\n• 此操作无法撤销\n• 账户信息将永久删除\n• 不会影响Cursor官方账户`,
    accountInfo: {
      email: account.email
    },
    warnings: [
      '此操作无法撤销',
      '账户信息将永久删除',
      '不会影响Cursor官方账户'
    ],
    onConfirm: async () => {
      confirmDialog.value.visible = false
      try {
        logStore.addInfo(`开始删除账户: ${account.email}`, 'DeleteAccount')

        // 显示开始提示
        notification.warning(
          '正在删除账户',
          `正在删除 ${account.email}，请稍候...`,
          { duration: 3000 }
        )

        // 调用后端API删除账户文件中的记录
        const result = await realAPI.deleteAccount(account.email) as any

        // 检查删除结果
        if (result && typeof result === 'object' && result.success) {
          logStore.addSuccess(`账户删除完成: ${result.message}`, 'DeleteAccount')

          // 从前端列表中移除
          registeredAccounts.value = registeredAccounts.value.filter(acc => acc.email !== account.email)

          if (result.backup_file) {
            logStore.addInfo(`已创建备份文件: ${result.backup_file}`, 'DeleteAccount')
          }
        } else {
          throw new Error(result?.error || '删除失败')
        }

        // 显示成功提示
        notification.success(
          '删除成功',
          `已成功删除账户: ${account.email}`,
          { duration: 4000 }
        )

      // 账户列表已在前端更新，无需刷新

    } catch (error) {
      logStore.addError(`删除账户失败: ${(error as Error).message}`, 'DeleteAccount')
      console.error('删除账户失败:', error)

      // 显示错误提示
      notification.error(
        '删除失败',
        `无法删除账户 ${account.email}: ${(error as Error).message}`,
        { duration: 5000 }
      )
      }
    },
    onCancel: () => {
      confirmDialog.value.visible = false
      logStore.addInfo('用户取消删除账户操作', 'DeleteAccount')
    }
  }
}

// 保存当前账户
const saveCurrentAccount = async () => {
  if (isSavingAccount.value) return // 防止重复点击

  console.log('[Dashboard] 用户点击保存当前账户按钮')
  logStore.addInfo('开始保存当前登录账户', 'SaveCurrentAccount')
  isSavingAccount.value = true

  try {
    const result = await realAPI.saveAccount()

    if (result && typeof result === 'object') {
      if (result.success) {
        logStore.addSuccess(`账户保存成功: ${result.message || '当前账户已保存'}`, 'SaveCurrentAccount')
        appStore.showSuccess(`🎉 账户保存成功！\n\n✅ 当前登录账户已保存到列表\n💡 可以在账户列表中查看`)

        // 保存成功后刷新账户列表
        await getRegisteredAccounts()
      } else {
        throw new Error(result.error || '保存失败，未知错误')
      }
    } else {
      // 如果返回的是字符串，按原来的方式处理
      logStore.addSuccess(`账户保存成功: ${result}`, 'SaveCurrentAccount')
      appStore.showSuccess(`🎉 账户保存成功！\n\n✅ 当前登录账户已保存到列表`)
      await getRegisteredAccounts()
    }
  } catch (error) {
    const errorMsg = (error as Error).message
    logStore.addError(`保存账户失败: ${errorMsg}`, 'SaveCurrentAccount')
    appStore.showError(`❌ 保存账户失败\n\n${errorMsg}`)
  } finally {
    isSavingAccount.value = false
  }
}

// 刷新账户列表
const refreshAccounts = async () => {
  if (isRefreshing.value) return // 防止重复点击

  console.log('[Dashboard] 用户点击刷新按钮')
  logStore.addInfo('开始刷新账户列表', 'RefreshAccounts')
  isRefreshing.value = true

  try {
    await getAccountInfo()
    await getRegisteredAccounts()
    console.log('[Dashboard] ✅ 刷新完成')

    // 显示成功提示
    notification.success(
      '刷新成功',
      `已更新账户信息，共 ${registeredAccounts.value.length} 个账户`,
      { duration: 3000 }
    )
    logStore.addSuccess(`账户列表刷新成功，共 ${registeredAccounts.value.length} 个账户`, 'RefreshAccounts')
  } catch (error) {
    console.error('[Dashboard] 刷新失败:', error)

    // 显示错误提示
    notification.error(
      '刷新失败',
      '无法获取最新的账户信息，请检查网络连接',
      { duration: 5000 }
    )
    logStore.addError(`账户列表刷新失败: ${(error as Error).message}`, 'RefreshAccounts')
  } finally {
    // 确保动画至少显示800ms，让用户看到反馈
    setTimeout(() => {
      isRefreshing.value = false
    }, 800)
  }
}

// 获取后端日志并添加到store
const getBackendLogs = async () => {
  try {
    console.log('[Dashboard] 开始获取后端日志...')
    await waitForAPI()
    console.log('[Dashboard] API已准备好')

    // 优先使用HTTP API
    try {
      const logData = await realAPI.getLog()
      console.log('[Dashboard] HTTP API返回日志:', logData)

      // 不再清空现有日志，而是检查是否已经添加过后端日志
      const hasBackendLog = logStore.logs.some(log => log.content.includes('日志功能正常'))

      if (!hasBackendLog) {
        // 将后端日志按行分割并添加到store
        const lines = logData.split('\n')
        console.log('[Dashboard] 日志行数:', lines.length)

        lines.forEach((line: string) => {
          if (line.trim()) {
            logStore.addInfo(line.trim(), 'Backend')
          }
        })

        console.log('[Dashboard] HTTP API日志已加载到store')
      } else {
        console.log('[Dashboard] 后端日志已存在，跳过重复添加')
      }
      return
    } catch (httpError) {
      console.log('[Dashboard] HTTP API调用失败，尝试PyWebView API:', httpError)
    }

    // 如果HTTP API失败，添加错误日志但不清空现有日志
    console.log('[Dashboard] 没有可用的API，添加错误日志')
    logStore.addWarning('无法获取后端日志，API连接失败', 'Backend')
  } catch (error) {
    console.error('[Dashboard] 获取后端日志失败:', error)
    // 如果获取失败，添加错误日志
    logStore.addWarning('无法获取后端日志，API连接失败', 'Backend')
  }
}



// 是否已初始化的标志
const isInitialized = ref(false)
let refreshTimer: number | null = null

// 初始化数据的函数
const initializeData = async () => {
  if (isInitialized.value) {
    console.log('[Dashboard] 已初始化，跳过重复初始化')
    return
  }

  console.log('[Dashboard] 开始初始化数据...')

  // 确保日志存储已初始化（只会执行一次）
  logStore.init()

  // 添加Dashboard启动日志（只会执行一次）
  logStore.initDashboard()

  try {
    await getAccountInfo()
    await getRegisteredAccounts()
    await getBackendLogs()

    // 只在首次初始化时添加完成日志
    if (logStore.logs.filter(log => log.content.includes('Dashboard 初始化完成')).length === 0) {
      logStore.addSuccess('Dashboard 初始化完成', 'Dashboard')
    }

    isInitialized.value = true
    console.log('[Dashboard] ✅ 初始化完成')
  } catch (error) {
    console.error('[Dashboard] ❌ 初始化失败:', error)
  }
}

// 组件挂载时获取数据（只在首次创建时执行）
onMounted(() => {
  console.log('[Dashboard] 组件挂载 - onMounted')

  // 延迟一下确保PyWebView API已准备好
  setTimeout(() => {
    initializeData()
  }, 500)

  // 设置定时刷新
  setupAutoRefresh()
})

// keep-alive激活时（从其他页面切换回来）
onActivated(() => {
  console.log('[Dashboard] 组件激活 - onActivated')

  // 如果已经初始化过，只需要刷新当前账户信息
  if (isInitialized.value) {
    console.log('[Dashboard] 组件已缓存，快速刷新账户信息')
    getAccountInfo() // 只刷新当前账户信息，不重新获取账户列表
  } else {
    // 如果还没初始化（理论上不应该发生），执行完整初始化
    console.log('[Dashboard] 组件未初始化，执行完整初始化')
    setTimeout(() => {
      initializeData()
    }, 100)
  }

  // 重新设置定时器（如果需要）
  setupAutoRefresh()
})

// keep-alive停用时（切换到其他页面）
onDeactivated(() => {
  console.log('[Dashboard] 组件停用 - onDeactivated')

  // 清除定时器，避免在后台继续刷新
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
    console.log('[Dashboard] 已清除定时刷新器')
  }
})

// 设置自动刷新的函数
const setupAutoRefresh = () => {
  // 如果已经有定时器，先清除
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }

  // 定期刷新账号信息 - 添加错误限制和防重复调用
  let consecutiveErrors = 0
  const maxConsecutiveErrors = 3
  let isRefreshing = false // 防止重复调用

  const refreshAccountInfo = async () => {
    // 防止重复调用
    if (isRefreshing) {
      console.log('[Dashboard] 账户信息正在刷新中，跳过本次调用')
      return
    }

    isRefreshing = true
    try {
      await getAccountInfo()
      consecutiveErrors = 0 // 重置错误计数
    } catch (error) {
      consecutiveErrors++
      console.log(`[Dashboard] 账户信息获取失败 (${consecutiveErrors}/${maxConsecutiveErrors})`)

      // 如果连续失败超过限制，停止自动刷新
      if (consecutiveErrors >= maxConsecutiveErrors) {
        console.log('[Dashboard] 连续失败次数过多，停止自动刷新账户信息')
        if (refreshTimer) {
          clearInterval(refreshTimer)
          refreshTimer = null
        }
        return
      }
    } finally {
      isRefreshing = false
    }
  }

  // 延长刷新间隔到60秒，减少频繁调用
  refreshTimer = setInterval(refreshAccountInfo, 60000) // 每60秒刷新一次
  console.log('[Dashboard] 已设置定时刷新器')
}

// 获取备份列表
const getBackupList = async () => {
  try {
    loadingBackups.value = true
    const result = await realAPI.listBackups() as any

    if (result && result.success) {
      backups.value = result.backups || []
      logStore.addInfo(`获取到 ${result.count} 个备份文件`, 'BackupList')
    } else {
      throw new Error(result?.error || '获取备份列表失败')
    }
  } catch (error) {
    console.error('获取备份列表失败:', error)
    logStore.addError(`获取备份列表失败: ${(error as Error).message}`, 'BackupList')
    notification.error(
      '获取失败',
      `获取备份列表失败: ${(error as Error).message}`,
      { duration: 4000 }
    )
  } finally {
    loadingBackups.value = false
  }
}

// 恢复账户
const restoreAccount = async (backupFile: string) => {
  try {
    logStore.addInfo(`开始恢复账户: ${backupFile}`, 'RestoreAccount')

    const result = await realAPI.restoreAccount(backupFile) as any

    if (result && result.success) {
      logStore.addSuccess(`账户恢复完成: ${result.message}`, 'RestoreAccount')

      if (result.current_backup) {
        logStore.addInfo(`已创建当前状态备份: ${result.current_backup}`, 'RestoreAccount')
      }

      // 显示成功提示
      notification.success(
        '恢复成功',
        `已成功从备份文件恢复账户`,
        { duration: 4000 }
      )

      // 关闭对话框并刷新账户列表
      showBackupDialog.value = false
      setTimeout(() => {
        refreshAccounts()
      }, 500)

    } else {
      throw new Error(result?.error || '恢复失败')
    }
  } catch (error) {
    console.error('恢复账户失败:', error)
    logStore.addError(`恢复账户失败: ${backupFile} - ${(error as Error).message}`, 'RestoreAccount')
    notification.error(
      '恢复失败',
      `恢复账户失败: ${(error as Error).message}`,
      { duration: 4000 }
    )
  }
}

// 删除备份文件
const deleteBackupFile = async (backupFile: string) => {
  try {
    logStore.addInfo(`开始删除备份文件: ${backupFile}`, 'DeleteBackup')

    // 调用删除API
    const result = await realAPI.deleteAccountBackup(backupFile) as any

    if (result && result.success) {
      logStore.addSuccess(`备份文件删除成功: ${backupFile}`, 'DeleteBackup')

      notification.success(
        '删除成功',
        `备份文件已删除`,
        { duration: 3000 }
      )

      // 刷新备份列表
      await getBackupList()
    } else {
      throw new Error(result?.error || '删除失败')
    }
  } catch (error) {
    console.error('删除备份文件失败:', error)
    logStore.addError(`删除备份文件失败: ${backupFile} - ${(error as Error).message}`, 'DeleteBackup')
    notification.error(
      '删除失败',
      `删除备份文件失败: ${(error as Error).message}`,
      { duration: 4000 }
    )
  }
}

// 显示备份对话框
const showBackups = async () => {
  showBackupDialog.value = true
  await getBackupAccountList()
}

// 获取备份账户列表
const getBackupAccountList = async () => {
  try {
    loadingBackupAccounts.value = true
    logStore.addInfo('开始获取备份账户列表', 'BackupAccounts')

    const result = await realAPI.listBackupAccounts() as any

    if (result && result.success) {
      backupAccounts.value = result.accounts || []
      logStore.addSuccess(`获取到 ${backupAccounts.value.length} 个备份账户`, 'BackupAccounts')
    } else {
      throw new Error(result?.error || '获取备份账户列表失败')
    }
  } catch (error) {
    console.error('获取备份账户列表失败:', error)
    logStore.addError(`获取备份账户列表失败: ${(error as Error).message}`, 'BackupAccounts')
    notification.error(
      '获取失败',
      `无法获取备份账户列表: ${(error as Error).message}`,
      { duration: 4000 }
    )
    backupAccounts.value = []
  } finally {
    loadingBackupAccounts.value = false
  }
}

// 从备份中恢复账户
const restoreAccountFromBackup = async (email: string) => {
  try {
    logStore.addInfo(`开始恢复账户: ${email}`, 'RestoreAccount')

    // 显示开始提示
    notification.info(
      '正在恢复账户',
      `正在恢复 ${email}，请稍候...`,
      { duration: 3000 }
    )

    const result = await realAPI.restoreAccountFromBackup(email) as any

    if (result && result.success) {
      logStore.addSuccess(`账户恢复完成: ${result.message}`, 'RestoreAccount')

      // 显示成功提示
      notification.success(
        '恢复成功',
        `已成功恢复账户: ${email}`,
        { duration: 4000 }
      )

      // 刷新账户列表和备份账户列表
      await getAccountList()
      await getBackupAccountList()

    } else {
      throw new Error(result?.error || '恢复失败')
    }
  } catch (error) {
    console.error('恢复账户失败:', error)
    logStore.addError(`恢复账户失败: ${email} - ${(error as Error).message}`, 'RestoreAccount')
    notification.error(
      '恢复失败',
      `恢复账户失败: ${(error as Error).message}`,
      { duration: 4000 }
    )
  }
}

// 获取Token真实状态 - 不再欺骗用户
const getTokenStatus = (token: string | undefined) => {
  if (!token) {
    return '未配置'
  }

  // 检查是否是无效的占位符Token
  const invalidTokens = ['已设置', '未能获取到有效Token', '需要手动获取Token', '需要手动获取密码']
  if (invalidTokens.includes(token)) {
    return '无效Token'
  }

  // 检查是否是真实的JWT Token
  if (token.startsWith('eyJ') && token.includes('.')) {
    return '已配置'
  }

  // 其他情况视为可疑
  return '状态未知'
}

// 全局类型已在 src/types/index.ts 中定义
</script>

<style scoped>


/* 注册账户列表样式 */
.registered-accounts-section {
  margin-top: 24px;
}

.section-title {
  color: #60a5fa;
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 星星爆炸刷新按钮样式 */
.star-button {
  position: relative;
  padding: 8px 20px;
  background: #fec195;
  font-size: 14px;
  font-weight: 500;
  color: #181818;
  border: 2px solid #fec195;
  border-radius: 8px;
  box-shadow: 0 0 0 #fec1958c;
  transition: all 0.3s ease-in-out;
  cursor: pointer;
  margin-left: auto;
  overflow: visible;
}

.star-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.star-button .star-1 {
  position: absolute;
  top: 20%;
  left: 20%;
  width: 12px;
  height: auto;
  filter: drop-shadow(0 0 0 #fffdef);
  z-index: -5;
  transition: all 1s cubic-bezier(0.05, 0.83, 0.43, 0.96);
}

.star-button .star-2 {
  position: absolute;
  top: 45%;
  left: 45%;
  width: 8px;
  height: auto;
  filter: drop-shadow(0 0 0 #fffdef);
  z-index: -5;
  transition: all 1s cubic-bezier(0, 0.4, 0, 1.01);
}

.star-button .star-3 {
  position: absolute;
  top: 40%;
  left: 40%;
  width: 4px;
  height: auto;
  filter: drop-shadow(0 0 0 #fffdef);
  z-index: -5;
  transition: all 1s cubic-bezier(0, 0.4, 0, 1.01);
}

.star-button .star-4 {
  position: absolute;
  top: 20%;
  left: 40%;
  width: 6px;
  height: auto;
  filter: drop-shadow(0 0 0 #fffdef);
  z-index: -5;
  transition: all 0.8s cubic-bezier(0, 0.4, 0, 1.01);
}

.star-button .star-5 {
  position: absolute;
  top: 25%;
  left: 45%;
  width: 8px;
  height: auto;
  filter: drop-shadow(0 0 0 #fffdef);
  z-index: -5;
  transition: all 0.6s cubic-bezier(0, 0.4, 0, 1.01);
}

.star-button .star-6 {
  position: absolute;
  top: 5%;
  left: 50%;
  width: 4px;
  height: auto;
  filter: drop-shadow(0 0 0 #fffdef);
  z-index: -5;
  transition: all 0.8s ease;
}

.star-button:hover:not(:disabled) {
  background: transparent;
  color: #fec195;
  box-shadow: 0 0 25px #fec1958c;
}

.star-button:hover:not(:disabled) .star-1 {
  position: absolute;
  top: -80%;
  left: -30%;
  width: 12px;
  height: auto;
  filter: drop-shadow(0 0 10px #fffdef);
  z-index: 2;
}

.star-button:hover:not(:disabled) .star-2 {
  position: absolute;
  top: -25%;
  left: 10%;
  width: 8px;
  height: auto;
  filter: drop-shadow(0 0 10px #fffdef);
  z-index: 2;
}

.star-button:hover:not(:disabled) .star-3 {
  position: absolute;
  top: 55%;
  left: 25%;
  width: 4px;
  height: auto;
  filter: drop-shadow(0 0 10px #fffdef);
  z-index: 2;
}

.star-button:hover:not(:disabled) .star-4 {
  position: absolute;
  top: 30%;
  left: 80%;
  width: 6px;
  height: auto;
  filter: drop-shadow(0 0 10px #fffdef);
  z-index: 2;
}

.star-button:hover:not(:disabled) .star-5 {
  position: absolute;
  top: 25%;
  left: 115%;
  width: 8px;
  height: auto;
  filter: drop-shadow(0 0 10px #fffdef);
  z-index: 2;
}

.star-button:hover:not(:disabled) .star-6 {
  position: absolute;
  top: 5%;
  left: 60%;
  width: 4px;
  height: auto;
  filter: drop-shadow(0 0 10px #fffdef);
  z-index: 2;
}

.star-button .fil0 {
  fill: #fffdef;
}



/* 加载容器样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: #23272e;
  border-radius: 12px;
  border: 1px solid #374151;
}

.loading-text {
  color: #9ca3af;
  font-size: 14px;
  margin-top: 20px;
  text-align: center;
}

/* 加载动画样式 */
.loading-wrapper {
  width: 120px;
  height: 36px;
  position: relative;
  z-index: 1;
}

.circle {
  width: 12px;
  height: 12px;
  position: absolute;
  border-radius: 50%;
  background-color: #ffffff;
  left: 15%;
  transform-origin: 50%;
  animation: circle7124 .5s alternate infinite ease;
}

@keyframes circle7124 {
  0% {
    top: 36px;
    height: 6px;
    border-radius: 50px 50px 25px 25px;
    transform: scaleX(1.7);
  }

  40% {
    height: 12px;
    border-radius: 50%;
    transform: scaleX(1);
  }

  100% {
    top: 0%;
  }
}

.circle:nth-child(2) {
  left: 45%;
  animation-delay: .2s;
}

.circle:nth-child(3) {
  left: auto;
  right: 15%;
  animation-delay: .3s;
}

.shadow {
  width: 12px;
  height: 3px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  position: absolute;
  top: 39px;
  transform-origin: 50%;
  z-index: -1;
  left: 15%;
  filter: blur(1.5px);
  animation: shadow046 .5s alternate infinite ease;
}

@keyframes shadow046 {
  0% {
    transform: scaleX(1.5);
  }

  40% {
    transform: scaleX(1);
    opacity: .7;
  }

  100% {
    transform: scaleX(.2);
    opacity: .4;
  }
}

.shadow:nth-child(4) {
  left: 45%;
  animation-delay: .2s
}

.shadow:nth-child(5) {
  left: auto;
  right: 15%;
  animation-delay: .3s;
}

.registered-accounts-container {
  background: #23272e;
  border-radius: 12px;
  padding: 16px;
  border: 1.5px solid #2d323a;
}

/* 空状态样式 */
.no-accounts {
  text-align: center;
  padding: 40px 20px;
  color: #94a3b8;
}

.no-accounts-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.no-accounts-text {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #e5e5e5;
}

.no-accounts-hint {
  font-size: 0.9rem;
  color: #94a3b8;
  font-style: italic;
}

/* 进度条样式 - 与原版完全一致 */
.progress-icon {
  width: 10px;
  height: 10px;
  border-radius: 2px;
  display: inline-block;
  position: relative;
}

.progress-icon::after {
  content: '';
  position: absolute;
  inset: -2px;
  background: inherit;
  border-radius: 2px;
  opacity: 0.3;
  filter: blur(3px);
  z-index: -1;
}

.icon-red { background: #ff6b6b; }
.icon-blue { background: #4ecdc4; }
.icon-green { background: #45b7d1; }

.progress-container-new {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
  margin: 0 12px;
}

.progress-bar-new {
  height: 100%;
  border-radius: 3px;
  transition: width 0.4s ease;
}

.progress-bar-trial {
  background: rgba(255, 255, 255, 0.4);
}

.progress-bar-usage {
  background: rgba(255, 255, 255, 0.4);
}

.progress-bar-basic {
  background: #10b981;
}

/* 底部指标区域 */
.bottom-horizontal-new {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.metric-row-new {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.metric-label-new {
  color: #ffffff;
  font-size: 0.95rem;
  font-weight: 500;
  min-width: 80px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.metric-value-new {
  color: #ffffff;
  font-size: 0.95rem;
  font-weight: 600;
  margin-left: auto;
  flex-shrink: 0;
}

/* 账户项样式 - 新的卡片风格 */
.account-item {
  --background: #d3d3d3;
  --main-color: #323232;
  --font-color: #323232;
  --font-color-sub: #666;
  --bg-color: #fff;

  background: var(--background);
  border-radius: 5px;
  padding: 20px;
  margin-bottom: 16px;
  border: 2px solid var(--main-color);
  box-shadow: 4px 4px var(--main-color);
  transition: all 0.2s ease;
  position: relative;
  color: var(--font-color);
}

.account-item:hover {
  transform: translateY(-2px);
  box-shadow: 6px 6px var(--main-color);
}

.account-item:last-child {
  margin-bottom: 0;
}

/* 账户颜色变量 */
.account-item[data-account-index="0"] {
  --account-color: #10b981;
  --account-bg: #10b981;
}

.account-item[data-account-index="1"] {
  --account-color: #f59e0b;
  --account-bg: #f59e0b;
}

.account-item[data-account-index="2"] {
  --account-color: #ef4444;
  --account-bg: #ef4444;
}

.account-item[data-account-index="3"] {
  --account-color: #8b5cf6;
  --account-bg: #8b5cf6;
}

.account-item[data-account-index="4"] {
  --account-color: #06b6d4;
  --account-bg: #06b6d4;
}

.account-item[data-account-index="5"] {
  --account-color: #ec4899;
  --account-bg: #ec4899;
}

.account-item[data-account-index="6"] {
  --account-color: #84cc16;
  --account-bg: #84cc16;
}

.account-item[data-account-index="7"] {
  --account-color: #f97316;
  --account-bg: #f97316;
}

.account-item[data-account-index="8"] {
  --account-color: #6366f1;
  --account-bg: #6366f1;
}

.account-item[data-account-index="9"] {
  --account-color: #14b8a6;
  --account-bg: #14b8a6;
}

.account-header {
  display: flex;
  flex-direction: row;
  gap: 16px;
  margin-bottom: 16px;
  padding-bottom: 16px;
  padding-right: 50px; /* 给右上角数字标签留出空间 */
  border-bottom: 2px solid var(--main-color);
  position: relative;
}

.account-number {
  position: absolute;
  top: -12px; /* 往上移动一点点 */
  right: 4px; /* 往右移动一点点 */
  background: var(--account-bg, #10b981);
  color: white;
  padding: 6px 10px;
  border-radius: 5px;
  border: 2px solid var(--main-color);
  box-shadow: 2px 2px var(--main-color);
  font-size: 0.8rem;
  font-weight: 700;
  font-family: 'Fira Mono', 'Consolas', monospace;
  z-index: 10;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.account-info-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 15px;
  background: var(--bg-color);
  border-radius: 5px;
  border: 2px solid var(--main-color);
  box-shadow: 4px 4px var(--main-color);
  transition: all 0.2s ease;
  position: relative;
  flex: 1;
  min-width: 0;
}

.account-info-group:hover {
  transform: translateY(-1px);
  box-shadow: 5px 5px var(--main-color);
}

.account-info-group:hover .copy-btn {
  opacity: 1;
}

.account-info-title {
  color: #7c3aed;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
  font-family: 'Fira Mono', 'Consolas', monospace;
  background: rgba(124, 58, 237, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid rgba(124, 58, 237, 0.3);
}

.account-info-content {
  color: var(--font-color);
  font-family: 'Fira Mono', 'Consolas', monospace;
  font-size: 0.95rem;
  font-weight: 600;
  word-break: break-all;
}

.account-email {
  color: #2563eb !important;
  font-weight: 700;
}

.email-domain {
  color: #7c3aed !important;
  font-weight: 800;
}

.account-password {
  color: #059669 !important;
  font-weight: 700;
}

.register-time {
  font-size: 0.85rem !important;
  color: var(--font-color-sub) !important;
}

.trial-info {
  font-size: 0.85rem !important;
  color: #0891b2 !important;
  font-weight: 700 !important;
}

.copy-btn {
  position: absolute;
  top: 6px;
  right: 6px;
  background: var(--bg-color);
  border: 2px solid var(--main-color);
  border-radius: 5px;
  padding: 3px 5px;
  color: var(--font-color);
  font-size: 0.65rem;
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 3px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  box-shadow: 2px 2px var(--main-color);
}

.copy-btn:hover {
  background: var(--bg-color);
  border-color: var(--main-color);
  color: var(--font-color);
  transform: translateY(-1px);
  box-shadow: 3px 3px var(--main-color);
}

.copy-btn svg {
  width: 10px;
  height: 10px;
}

.copy-btn.copied {
  background: rgba(16, 185, 129, 0.2);
  border-color: rgba(16, 185, 129, 0.4);
  color: #10b981;
}

.account-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px 0;
  border-top: 2px solid var(--main-color);
}

.account-detail-item {
  background: var(--bg-color);
  padding: 10px 12px;
  border-radius: 5px;
  border: 2px solid var(--main-color);
  box-shadow: 2px 2px var(--main-color);
  transition: all 0.2s ease;
}

.account-detail-item:hover {
  transform: translateY(-1px);
  box-shadow: 3px 3px var(--main-color);
}

.account-detail-label {
  color: #059669;
  font-size: 0.7rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
  font-family: 'Fira Mono', 'Consolas', monospace;
  background: rgba(5, 150, 105, 0.1);
  padding: 2px 4px;
  border-radius: 2px;
  border: 1px solid rgba(5, 150, 105, 0.3);
}

.account-detail-value {
  color: var(--font-color);
  font-family: 'Fira Mono', 'Consolas', monospace;
  font-size: 0.85rem;
  font-weight: 600;
  word-break: break-all;
}

/* 特定详情项的颜色 */
.account-detail-item:nth-child(1) .account-detail-value {
  color: #7c3aed; /* Token状态 - 紫色 */
}

.account-detail-item:nth-child(2) .account-detail-value {
  color: #059669; /* 保存位置 - 绿色 */
}

.account-detail-item:nth-child(4) .account-detail-value {
  color: #dc2626; /* 加密备份 - 红色 */
}

.account-detail-item:nth-child(5) .account-detail-value {
  color: #ea580c; /* 机器码 - 橙色 */
}

.account-detail-item:nth-child(6) .account-detail-value {
  color: #0891b2; /* 使用情况 - 青色 */
}

.account-detail-item:nth-child(7) .account-detail-value {
  color: #7c2d12; /* 账户类型 - 棕色 */
}

.account-status {
  font-size: 0.8rem;
  font-weight: 700;
  padding: 2px 6px;
  border-radius: 3px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.account-status.active {
  color: #059669 !important;
  background: rgba(5, 150, 105, 0.1);
  border: 1px solid #059669;
}

.account-status.inactive {
  color: #dc2626 !important;
  background: rgba(220, 38, 38, 0.1);
  border: 1px solid #dc2626;
}

.status-active {
  color: #10b981 !important;
}

.status-inactive {
  color: #94a3b8 !important;
}

.account-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #2d323a;
}

/* 新的动画按钮样式 */
.action-button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgb(20, 20, 20);
  border: none;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.164);
  cursor: pointer;
  transition-duration: .3s;
  overflow: hidden;
  position: relative;
}

.svgIcon {
  width: 12px;
  transition-duration: .3s;
}

.svgIcon path {
  fill: white;
}

.svgIcon polyline,
.svgIcon rect,
.svgIcon line {
  stroke: white;
  fill: none;
}

/* 复制按钮 */
.copy-button:hover {
  width: 140px;
  border-radius: 50px;
  transition-duration: .3s;
  background-color: rgb(34, 197, 94);
  align-items: center;
}

.copy-button:hover .svgIcon {
  width: 50px;
  transition-duration: .3s;
  transform: translateY(60%);
}

.copy-button::before {
  position: absolute;
  top: -20px;
  left: 50%;
  content: "Copy";
  color: white;
  transition-duration: .3s;
  font-size: 2px;
  transform: translateX(-50%);
}

.copy-button:hover::before {
  font-size: 13px;
  opacity: 1;
  transform: translateX(-50%) translateY(30px);
  transition-duration: .3s;
}

/* 切换按钮 */
.switch-button:hover {
  width: 140px;
  border-radius: 50px;
  transition-duration: .3s;
  background-color: rgb(59, 130, 246);
  align-items: center;
}

.switch-button:hover .svgIcon {
  width: 50px;
  transition-duration: .3s;
  transform: translateY(60%);
}

.switch-button::before {
  position: absolute;
  top: -20px;
  left: 50%;
  content: "Switch";
  color: white;
  transition-duration: .3s;
  font-size: 2px;
  transform: translateX(-50%);
}

.switch-button:hover::before {
  font-size: 13px;
  opacity: 1;
  transform: translateX(-50%) translateY(30px);
  transition-duration: .3s;
}

/* 删除按钮 */
.delete-button:hover {
  width: 140px;
  border-radius: 50px;
  transition-duration: .3s;
  background-color: rgb(255, 69, 69);
  align-items: center;
}

.delete-button:hover .svgIcon {
  width: 50px;
  transition-duration: .3s;
  transform: translateY(60%);
}

.delete-button::before {
  position: absolute;
  top: -20px;
  left: 50%;
  content: "Delete";
  color: white;
  transition-duration: .3s;
  font-size: 2px;
  transform: translateX(-50%);
}

.delete-button:hover::before {
  font-size: 13px;
  opacity: 1;
  transform: translateX(-50%) translateY(30px);
  transition-duration: .3s;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-section {
    padding: 16px;
  }

  .account-card {
    margin-bottom: 16px;
  }

  .section-title {
    font-size: 1.1rem;
    flex-wrap: wrap;
    gap: 12px;
  }

  .star-button {
    margin-left: 0;
    font-size: 12px;
    padding: 6px 16px;
  }
}

/* 摇晃效果样式 */
.content-section .shake-container {
  position: relative;
  transition: 200ms;
  perspective: 800px;
  margin: 20px;
  padding: 10px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr 1fr 1fr 1fr;
  gap: 0px 0px;
  grid-template-areas:
    "tr-1 tr-2 tr-3 tr-4 tr-5"
    "tr-6 tr-7 tr-8 tr-9 tr-10"
    "tr-11 tr-12 tr-13 tr-14 tr-15"
    "tr-16 tr-17 tr-18 tr-19 tr-20"
    "tr-21 tr-22 tr-23 tr-24 tr-25";
}

.content-section .tr-1 { grid-area: tr-1; }
.content-section .tr-2 { grid-area: tr-2; }
.content-section .tr-3 { grid-area: tr-3; }
.content-section .tr-4 { grid-area: tr-4; }
.content-section .tr-5 { grid-area: tr-5; }
.content-section .tr-6 { grid-area: tr-6; }
.content-section .tr-7 { grid-area: tr-7; }
.content-section .tr-8 { grid-area: tr-8; }
.content-section .tr-9 { grid-area: tr-9; }
.content-section .tr-10 { grid-area: tr-10; }
.content-section .tr-11 { grid-area: tr-11; }
.content-section .tr-12 { grid-area: tr-12; }
.content-section .tr-13 { grid-area: tr-13; }
.content-section .tr-14 { grid-area: tr-14; }
.content-section .tr-15 { grid-area: tr-15; }
.content-section .tr-16 { grid-area: tr-16; }
.content-section .tr-17 { grid-area: tr-17; }
.content-section .tr-18 { grid-area: tr-18; }
.content-section .tr-19 { grid-area: tr-19; }
.content-section .tr-20 { grid-area: tr-20; }
.content-section .tr-21 { grid-area: tr-21; }
.content-section .tr-22 { grid-area: tr-22; }
.content-section .tr-23 { grid-area: tr-23; }
.content-section .tr-24 { grid-area: tr-24; }
.content-section .tr-25 { grid-area: tr-25; }

.content-section .account-card {
  grid-column: 1 / -1;
  grid-row: 1 / -1;
  z-index: 1;
}

/* tracker样式 */
.content-section .tracker {
  z-index: 200;
  width: 100%;
  height: 100%;
  position: relative;
}

.content-section .tracker:hover {
  cursor: pointer;
}
.content-section .tr-6 { grid-area: tr-6; }
.content-section .tr-7 { grid-area: tr-7; }
.content-section .tr-8 { grid-area: tr-8; }
.content-section .tr-9 { grid-area: tr-9; }
.content-section .tr-10 { grid-area: tr-10; }
.content-section .tr-11 { grid-area: tr-11; }
.content-section .tr-12 { grid-area: tr-12; }
.content-section .tr-13 { grid-area: tr-13; }
.content-section .tr-14 { grid-area: tr-14; }
.content-section .tr-15 { grid-area: tr-15; }
.content-section .tr-16 { grid-area: tr-16; }
.content-section .tr-17 { grid-area: tr-17; }
.content-section .tr-18 { grid-area: tr-18; }
.content-section .tr-19 { grid-area: tr-19; }
.content-section .tr-20 { grid-area: tr-20; }
.content-section .tr-21 { grid-area: tr-21; }
.content-section .tr-22 { grid-area: tr-22; }
.content-section .tr-23 { grid-area: tr-23; }
.content-section .tr-24 { grid-area: tr-24; }
.content-section .tr-25 { grid-area: tr-25; }

.content-section #shake-card {
  transition: 700ms;
}

/* 摇晃动画 - 第一行 */
.content-section .tracker.tr-1:hover ~ #shake-card {
  transition: 125ms ease-in-out;
  transform: rotateX(8deg) rotateY(-4deg) rotateZ(0deg);
}

.content-section .tracker.tr-2:hover ~ #shake-card {
  transition: 125ms ease-in-out;
  transform: rotateX(8deg) rotateY(-2deg) rotateZ(0deg);
}

.content-section .tracker.tr-3:hover ~ #shake-card {
  transition: 125ms ease-in-out;
  transform: rotateX(8deg) rotateY(0deg) rotateZ(0deg);
}

.content-section .tracker.tr-4:hover ~ #shake-card {
  transition: 125ms ease-in-out;
  transform: rotateX(8deg) rotateY(2deg) rotateZ(0deg);
}

.content-section .tracker.tr-5:hover ~ #shake-card {
  transition: 125ms ease-in-out;
  transform: rotateX(8deg) rotateY(4deg) rotateZ(0deg);
}

/* 摇晃动画 - 第二行 */
.content-section .tracker.tr-6:hover ~ #shake-card {
  transition: 125ms ease-in-out;
  transform: rotateX(4deg) rotateY(-4deg) rotateZ(0deg);
}

.content-section .tracker.tr-7:hover ~ #shake-card {
  transition: 125ms ease-in-out;
  transform: rotateX(4deg) rotateY(-2deg) rotateZ(0deg);
}

.content-section .tracker.tr-8:hover ~ #shake-card {
  transition: 125ms ease-in-out;
  transform: rotateX(4deg) rotateY(0deg) rotateZ(0deg);
}

.content-section .tracker.tr-9:hover ~ #shake-card {
  transition: 125ms ease-in-out;
  transform: rotateX(4deg) rotateY(2deg) rotateZ(0deg);
}

.content-section .tracker.tr-10:hover ~ #shake-card {
  transition: 125ms ease-in-out;
  transform: rotateX(4deg) rotateY(4deg) rotateZ(0deg);
}

/* 摇晃动画 - 第三行 */
.content-section .tracker.tr-11:hover ~ #shake-card {
  transition: 125ms ease-in-out;
  transform: rotateX(0deg) rotateY(-4deg) rotateZ(0deg);
}

.content-section .tracker.tr-12:hover ~ #shake-card {
  transition: 125ms ease-in-out;
  transform: rotateX(0deg) rotateY(-2deg) rotateZ(0deg);
}

.content-section .tracker.tr-13:hover ~ #shake-card {
  transition: 125ms ease-in-out;
  transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg);
}

.content-section .tracker.tr-14:hover ~ #shake-card {
  transition: 125ms ease-in-out;
  transform: rotateX(0deg) rotateY(2deg) rotateZ(0deg);
}

.content-section .tracker.tr-15:hover ~ #shake-card {
  transition: 125ms ease-in-out;
  transform: rotateX(0deg) rotateY(4deg) rotateZ(0deg);
}

/* 摇晃动画 - 第四行 */
.content-section .tracker.tr-16:hover ~ #shake-card {
  transition: 125ms ease-in-out;
  transform: rotateX(-4deg) rotateY(-4deg) rotateZ(0deg);
}

.content-section .tracker.tr-17:hover ~ #shake-card {
  transition: 125ms ease-in-out;
  transform: rotateX(-4deg) rotateY(-2deg) rotateZ(0deg);
}

.content-section .tracker.tr-18:hover ~ #shake-card {
  transition: 125ms ease-in-out;
  transform: rotateX(-4deg) rotateY(0deg) rotateZ(0deg);
}

.content-section .tracker.tr-19:hover ~ #shake-card {
  transition: 125ms ease-in-out;
  transform: rotateX(-4deg) rotateY(2deg) rotateZ(0deg);
}

.content-section .tracker.tr-20:hover ~ #shake-card {
  transition: 125ms ease-in-out;
  transform: rotateX(-4deg) rotateY(4deg) rotateZ(0deg);
}

/* 摇晃动画 - 第五行 */
.content-section .tracker.tr-21:hover ~ #shake-card {
  transition: 125ms ease-in-out;
  transform: rotateX(-8deg) rotateY(-4deg) rotateZ(0deg);
}

.content-section .tracker.tr-22:hover ~ #shake-card {
  transition: 125ms ease-in-out;
  transform: rotateX(-8deg) rotateY(-2deg) rotateZ(0deg);
}

.content-section .tracker.tr-23:hover ~ #shake-card {
  transition: 125ms ease-in-out;
  transform: rotateX(-8deg) rotateY(0deg) rotateZ(0deg);
}

.content-section .tracker.tr-24:hover ~ #shake-card {
  transition: 125ms ease-in-out;
  transform: rotateX(-8deg) rotateY(2deg) rotateZ(0deg);
}

.content-section .tracker.tr-25:hover ~ #shake-card {
  transition: 125ms ease-in-out;
  transform: rotateX(-8deg) rotateY(4deg) rotateZ(0deg);
}

/* 防止文本选择 */
.content-section .shake-canvas *,
.content-section .tracker {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 小巧精致按钮样式 */
.mini-action-btn {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 10px;
  color: #e5e5e5;
  font-size: 12px;
  font-weight: 500;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  overflow: hidden;
  margin: 0 6px;
}

.mini-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
  border-color: rgba(255, 255, 255, 0.2);
}

.mini-icon {
  position: relative;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.6s ease;
}

.mini-action-btn:active .ripple {
  width: 100px;
  height: 100px;
  opacity: 0;
}

/* 备份按钮特殊样式 */
.backup-mini {
  background: rgba(59, 130, 246, 0.12);
  border-color: rgba(59, 130, 246, 0.25);
}

.backup-mini:hover {
  background: rgba(59, 130, 246, 0.18);
  border-color: rgba(59, 130, 246, 0.4);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.25);
}

/* 小文件夹图标 */
.folder-mini {
  position: relative;
  width: 16px;
  height: 14px;
}

.folder-back {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 16px;
  height: 10px;
  background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
  border-radius: 2px;
  transition: all 0.3s ease;
}

.folder-front {
  position: absolute;
  top: 0;
  left: 0;
  width: 8px;
  height: 4px;
  background: linear-gradient(135deg, #93c5fd 0%, #60a5fa 100%);
  border-radius: 2px 2px 0 0;
  transition: all 0.3s ease;
}

.backup-mini:hover .folder-back {
  transform: scale(1.1);
  background: linear-gradient(135deg, #93c5fd 0%, #60a5fa 100%);
}

.backup-mini:hover .folder-front {
  transform: translateY(-1px) scale(1.1);
}

/* 保存按钮特殊样式 */
.save-mini {
  background: rgba(16, 185, 129, 0.12);
  border-color: rgba(16, 185, 129, 0.25);
}

.save-mini:hover {
  background: rgba(16, 185, 129, 0.18);
  border-color: rgba(16, 185, 129, 0.4);
  box-shadow: 0 8px 20px rgba(16, 185, 129, 0.25);
}

.save-mini.loading {
  opacity: 0.7;
  cursor: not-allowed;
}

.save-mini.loading:hover {
  transform: none;
  background: rgba(16, 185, 129, 0.12);
  border-color: rgba(16, 185, 129, 0.25);
  box-shadow: none;
}

/* 小磁盘图标 */
.save-disk {
  position: relative;
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, #34d399 0%, #10b981 100%);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.save-disk::before {
  content: '';
  position: absolute;
  top: 3px;
  left: 3px;
  right: 3px;
  height: 1px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 0.5px;
}

.save-disk::after {
  content: '';
  position: absolute;
  bottom: 3px;
  left: 3px;
  right: 3px;
  height: 4px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 1px;
}

.disk-hole {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 4px;
  height: 4px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.save-disk.spinning {
  animation: spin 1s linear infinite;
}

.save-mini:hover .save-disk {
  transform: scale(1.1);
  background: linear-gradient(135deg, #6ee7b7 0%, #34d399 100%);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 备份对话框样式 - 使用新的卡片样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}



.close-button {
  position: absolute;
  top: 15px;
  right: 15px;
  background: #000;
  color: #fff;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.close-button:hover {
  background: #dc2626;
  transform: rotate(90deg);
}

.card__content {
  max-height: 400px;
  overflow-y: auto;
  margin-bottom: 20px;
}

.loading-text {
  text-align: center;
  padding: 40px;
  color: #000;
  font-weight: bold;
}

.no-backups {
  text-align: center;
  padding: 40px;
  color: #000;
}

.no-backups .hint {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

.backup-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.backup-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 3px solid #000;
  background: #f0f0f0;
  transition: all 0.2s;
}

.backup-item:hover {
  background: #e0e0e0;
  transform: translateY(-2px);
}

.backup-info {
  flex: 1;
}

.backup-name {
  font-weight: 900;
  color: #000;
  margin-bottom: 4px;
  text-transform: uppercase;
}

.backup-details {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #000;
  font-weight: bold;
}

.backup-action {
  background: #000;
  color: #fff;
  padding: 2px 6px;
  font-weight: bold;
  text-transform: uppercase;
}

/* 按钮样式 */
.card__button {
  border: 3px solid #000;
  background: #000;
  color: #fff;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: bold;
  text-transform: uppercase;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s;
  min-width: 100px;
}

.card__button--cancel {
  background: #dc2626;
  border-color: #dc2626;
}

.card__button--cancel::before {
  content: "Sure?";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 105%;
  background-color: #f87171;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.card__button--cancel:hover::before {
  transform: translateY(0);
}

.card__button--confirm::before {
  content: "Sure?";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 105%;
  background-color: #5ad641;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.card__button--confirm:hover::before {
  transform: translateY(0);
}

.card__button:active {
  transform: scale(0.95);
}

.card__button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.card__button:disabled::before {
  display: none;
}

/* Kawaii滑块容器样式 */
.kawaii-sliders-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
}

.kawaii-slider-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.kawaii-slider-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #e5e7eb;
  font-size: 0.85rem;
  margin-bottom: 4px;
}

.kawaii-slider-icon {
  font-size: 1rem;
  margin-right: 8px;
}

.kawaii-slider-text {
  flex: 1;
  font-weight: 500;
}

.kawaii-slider-value {
  font-weight: 700;
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  min-width: 60px;
  text-align: center;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Kawaii滑块样式 */
[type="range"].kawaii {
  --base: #fe8ce4;
  --light: color-mix(in sRGB, var(--base) 60%, #fff);
  --lighter: color-mix(in sRGB, var(--base) 30%, #fff);
  --dark: color-mix(in sRGB, var(--base) 95%, #000);
  --transparent: color-mix(in sRGB, var(--base) 0%, #0000);
  appearance: none;
  font-size: 1em;
  width: 100%;
  height: 2em;
  border: 0.5em solid #fff;
  border-radius: 2em;
  box-shadow:
    0 0 1em #0001,
    0 0.25em 0.5em #0001;
  overflow: hidden;
  cursor: default;
}

[type="range"].kawaii::-webkit-slider-runnable-track {
  background:
    radial-gradient(
      circle at 0.75em 0.6em,
      var(--light) calc(0.2em - 1px),
      #0000 0.2em
    ),
    radial-gradient(
      circle at 1.25em 0.6em,
      var(--light) calc(0.2em - 1px),
      #0000 0.2em
    ),
    radial-gradient(
      circle at 5em 0.6em,
      var(--light) calc(0.2em - 1px),
      #0000 0.2em
    ),
    linear-gradient(var(--light) 0 0) 1.25em 0.4em / 3.75em calc(0.4em - 0.5px)
      no-repeat,
    linear-gradient(90deg, var(--base), var(--transparent) 1em),
    linear-gradient(#0000 70%, var(--dark) 80%),
    var(--base);
  border-radius: 2em;
  height: 100%;
  overflow: hidden;
}

[type="range"].kawaii::-webkit-slider-thumb {
  appearance: none;
  height: 2em;
  width: 2em;
  color: var(--lighter);
  background:
    radial-gradient(
      circle at 0.75em 0.6em,
      var(--light) calc(0.2em - 1px),
      #0000 0.2em
    ),
    linear-gradient(90deg, #0000 0.75em, var(--base) 0) 0 0 / 100% 50% no-repeat;
  border-radius: 50%;
  box-shadow:
    inset -0.5em 0 0.5em -0.25em var(--base),
    1em 0 0 0.25em,
    2em 0 0 0.25em,
    3em 0 0 0.25em,
    4em 0 0 0.25em,
    5em 0 0 0.25em,
    6em 0 0 0.25em,
    7em 0 0 0.25em,
    8em 0 0 0.25em,
    9em 0 0 0.25em,
    10em 0 0 0.25em,
    11em 0 0 0.25em,
    12em 0 0 0.25em,
    12em 0 0 0.25em,
    13em 0 0 0.25em,
    14em 0 0 0.25em,
    15em 0 0 0.25em,
    16em 0 0 0.25em,
    17em 0 0 0.25em,
    18em 0 0 0.25em,
    19em 0 0 0.25em;
}

[type="range"].kawaii::-moz-range-track {
  background:
    radial-gradient(
      circle at 0.75em 30%,
      var(--light) calc(0.2em - 1px),
      #0000 0.2em
    ),
    radial-gradient(
      circle at 1.5em 30%,
      var(--light) calc(0.2em - 1px),
      #0000 0.2em
    ),
    radial-gradient(
      circle at 5.5em 30%,
      var(--light) calc(0.2em - 1px),
      #0000 0.2em
    ),
    linear-gradient(var(--light) 0 0) 1.5em calc(15% + 0.18em) / 4em
      calc(0.4em - 0.5px) no-repeat,
    linear-gradient(90deg, var(--base), var(--transparent) 1em),
    linear-gradient(var(--transparent) 70%, var(--dark) 80%),
    var(--base);
  border-radius: 2em;
  height: 100%;
  overflow: hidden;
}

[type="range"].kawaii::-moz-range-thumb {
  appearance: none;
  height: 2em;
  width: 2em;
  border: 0;
  color: var(--lighter);
  background:
    radial-gradient(
      circle at 0.75em 0.6em,
      var(--light) calc(0.2em - 1px),
      #0000 0.2em
    ),
    linear-gradient(90deg, var(--transparent) 0.75em, var(--base) 0) 0 0 / 100%
      50% no-repeat;
  border-radius: 50% 0 50% 50% 0;
  box-shadow:
    inset -0.5em 0 0.5em -0.25em var(--base),
    1em 0 0 0.25em,
    2em 0 0 0.25em,
    3em 0 0 0.25em,
    4em 0 0 0.25em,
    5em 0 0 0.25em,
    6em 0 0 0.25em,
    7em 0 0 0.25em,
    8em 0 0 0.25em,
    9em 0 0 0.25em,
    10em 0 0 0.25em,
    11em 0 0 0.25em,
    12em 0 0 0.25em,
    12em 0 0 0.25em,
    13em 0 0 0.25em,
    14em 0 0 0.25em,
    15em 0 0 0.25em,
    16em 0 0 0.25em,
    17em 0 0 0.25em,
    18em 0 0 0.25em,
    19em 0 0 0.25em;
}

/* 备份内容样式 */
.backup-content {
  max-height: 400px;
  overflow-y: auto;
}

.loading-text {
  text-align: center;
  padding: 20px;
  color: #666;
}

.no-backups {
  text-align: center;
  padding: 20px;
  color: #666;
}

.no-backups .hint {
  font-size: 0.9em;
  color: #999;
  margin-top: 8px;
}

.backup-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.backup-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  background: #f9f9f9;
  transition: all 0.2s ease;
}

.backup-item:hover {
  border-color: #007bff;
  background: #f0f8ff;
}

.backup-info {
  flex: 1;
  min-width: 0;
}

.backup-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  word-break: break-all;
}

.backup-details {
  display: flex;
  gap: 12px;
  font-size: 0.85em;
  color: #666;
}

.backup-action {
  background: #e3f2fd;
  color: #1976d2;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8em;
}

.backup-time {
  color: #666;
}

.backup-size {
  color: #999;
}
/* 备份操作按钮样式 */
.backup-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 备份账户列表样式 */
.backup-accounts-list {
  max-height: 400px;
  overflow-y: auto;
}

.backup-accounts-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e0e0e0;
}

.backup-accounts-header h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-weight: 600;
}

.backup-account-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  margin-bottom: 12px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  background: #f9f9f9;
  transition: all 0.2s ease;
}

.backup-account-item:hover {
  border-color: #007bff;
  background: #f0f8ff;
  transform: translateY(-1px);
}

.account-info {
  flex: 1;
  min-width: 0;
}

.account-email {
  font-weight: 600;
  color: #333;
  margin-bottom: 6px;
  font-size: 1.1em;
}

.account-details {
  display: flex;
  gap: 12px;
  font-size: 0.85em;
  flex-wrap: wrap;
}

.account-plan {
  background: #e8f5e8;
  color: #2e7d32;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.account-usage {
  background: #fff3e0;
  color: #f57c00;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.account-token-status {
  background: #e3f2fd;
  color: #1976d2;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.account-token-status.no-token {
  background: #ffebee;
  color: #d32f2f;
}

.account-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}
</style>
