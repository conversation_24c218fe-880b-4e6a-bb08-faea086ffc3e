<template>
  <!-- 模态框遮罩 -->
  <div
    v-if="visible"
    class="modal-mask"
    @click="handleCancel"
  >
    <!-- 使用新的卡片样式 -->
    <div class="card input-card" @click.stop>
      <span class="card__title">{{ title }}</span>
      <p v-if="message" class="card__content">{{ message }}</p>
      <form class="card__form" @submit.prevent="handleConfirm">
        <input
          ref="inputRef"
          v-model="inputValue"
          required
          type="text"
          :placeholder="placeholder"
          @keydown.esc="handleCancel"
        />
        <div class="button-group">
          <button type="button" class="card__button card__button--cancel" @click="handleCancel">
            {{ cancelText }}
          </button>
          <button type="submit" class="card__button card__button--confirm">
            {{ confirmText }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'

interface InputModalProps {
  visible: boolean
  title: string
  message?: string
  placeholder?: string
  defaultValue?: string
  confirmText?: string
  cancelText?: string
}

interface InputModalEvents {
  'confirm': [value: string]
  'cancel': []
}

const props = withDefaults(defineProps<InputModalProps>(), {
  placeholder: '请输入...',
  defaultValue: '',
  confirmText: '确定',
  cancelText: '取消'
})

const emit = defineEmits<InputModalEvents>()

const inputRef = ref<HTMLInputElement>()
const inputValue = ref('')

// 监听visible变化，重置输入值并聚焦
watch(() => props.visible, async (newVisible) => {
  if (newVisible) {
    inputValue.value = props.defaultValue
    await nextTick()
    inputRef.value?.focus()
  }
})

const handleConfirm = () => {
  emit('confirm', inputValue.value)
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
/* From Uiverse.io by 0xnihilism */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.card {
  width: 400px;
  padding: 20px;
  background: #fff;
  border: 6px solid #000;
  box-shadow: 12px 12px 0 #000;
  transition: transform 0.3s, box-shadow 0.3s;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.card:hover {
  transform: translate(-5px, -5px);
  box-shadow: 17px 17px 0 #000;
}

.card__title {
  font-size: 32px;
  font-weight: 900;
  color: #000;
  text-transform: uppercase;
  margin-bottom: 15px;
  display: block;
  position: relative;
  overflow: hidden;
}

.card__title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 90%;
  height: 3px;
  background-color: #000;
  transform: translateX(-100%);
  transition: transform 0.3s;
}

.card:hover .card__title::after {
  transform: translateX(0);
}

.card__content {
  font-size: 16px;
  line-height: 1.4;
  color: #000;
  margin-bottom: 20px;
}

.card__form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.card__form input {
  padding: 10px;
  border: 3px solid #000;
  font-size: 16px;
  font-family: inherit;
  transition: transform 0.3s;
  width: calc(100% - 26px);
}

.card__form input:focus {
  outline: none;
  transform: scale(1.05);
  background-color: #000;
  color: #ffffff;
}

.button-group {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.card__button {
  border: 3px solid #000;
  background: #000;
  color: #fff;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: bold;
  text-transform: uppercase;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s;
  min-width: 100px;
}

.card__button--cancel {
  background: #dc2626;
  border-color: #dc2626;
}

.card__button--cancel::before {
  content: "Sure?";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 105%;
  background-color: #f87171;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.card__button--cancel:hover::before {
  transform: translateY(0);
}

.card__button--confirm::before {
  content: "Sure?";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 105%;
  background-color: #5ad641;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.card__button--confirm:hover::before {
  transform: translateY(0);
}

.card__button:active {
  transform: scale(0.95);
}

@keyframes glitch {
  0% {
    transform: translate(2px, 2px);
  }
  25% {
    transform: translate(-2px, -2px);
  }
  50% {
    transform: translate(-2px, 2px);
  }
  75% {
    transform: translate(2px, -2px);
  }
  100% {
    transform: translate(2px, 2px);
  }
}

.glitch {
  animation: glitch 0.3s infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card {
    width: 90vw;
    max-width: 350px;
  }

  .button-group {
    flex-direction: column;
  }

  .card__button {
    width: 100%;
  }
}
</style>
