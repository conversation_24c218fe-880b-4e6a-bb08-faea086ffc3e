{"menu": {"title": "可用选项", "exit": "退出程序", "reset": "重置机器ID", "register": "注册新的Cursor账户", "register_google": "使用自己的Google账户注册", "register_github": "使用自己的GitHub账户注册", "register_manual": "使用自定义邮箱注册Cursor", "quit": "关闭Cursor应用", "select_language": "更改语言", "select_chrome_profile": "选择Chrome配置文件", "input_choice": "请输入您的选择 ({choices})", "invalid_choice": "选择无效，请输入 {choices} 范围内的数字", "program_terminated": "程序已被用户终止", "error_occurred": "发生错误：{error}，请重试", "press_enter": "按回车键退出", "disable_auto_update": "禁用 Cursor 自动更新", "lifetime_access_enabled": "永久订阅", "totally_reset": "完全重置 Cursor", "outdate": "过时", "temp_github_register": "临时GitHub注册", "admin_required": "运行可执行文件，需要管理员权限", "admin_required_continue": "继续使用当前版本...", "coming_soon": "即将推出", "fixed_soon": "即将修复", "contribute": "贡献项目", "config": "显示配置", "delete_google_account": "删除 Cursor Google 账号", "continue_prompt": "继续？(y/N): ", "operation_cancelled_by_user": "操作被用户取消", "exiting": "退出中 ……", "bypass_version_check": "绕过 Cursor 版本检查", "check_user_authorized": "检查用户授权", "bypass_token_limit": "绕过 Token 限制", "language_config_saved": "语言配置保存成功", "lang_invalid_choice": "选择无效。请输入以下选项之一：({lang_choices})", "restore_machine_id": "从备份恢复机器ID", "manual_custom_auth": "手动自定义验证"}, "languages": {"ar": "阿拉伯语", "en": "英语", "zh_cn": "简体中文", "zh_tw": "繁体中文", "vi": "越南语", "nl": "荷兰语", "de": "德语", "fr": "法语", "pt": "葡萄牙语", "ru": "俄语", "tr": "土耳其语", "bg": "保加利亚语", "es": "西班牙语", "it": "意大利语", "ja": "日语"}, "quit_cursor": {"start": "开始退出 Cursor", "no_process": "未发现运行中的 Cursor 进程", "terminating": "正在终止进程 {pid}", "waiting": "等待进程退出", "success": "所有 Cursor 进程已正常关闭", "timeout": "以下进程未能在规定时间内关闭: {pids}", "error": "关闭 Cursor 进程时发生错误: {error}"}, "reset": {"title": "Cursor 机器标识重置工具", "checking": "检查配置文件", "not_found": "配置文件未找到", "no_permission": "无法读取或写入配置文件，请检查文件权限", "reading": "读取当前配置", "creating_backup": "创建配置备份", "backup_exists": "备份文件已存在，跳过备份步骤", "generating": "生成新机器标识", "saving_json": "保存新配置到JSON", "success": "机器标识重置成功", "new_id": "新机器标识", "permission_error": "权限错误: {error}", "run_as_admin": "请尝试以管理员身份运行此程序", "process_error": "重置进程错误: {error}", "updating_sqlite": "更新SQLite数据库", "updating_pair": "更新键值对", "sqlite_success": "SQLite数据库更新成功", "sqlite_error": "SQLite数据库更新失败: {error}", "press_enter": "按回车键退出", "updating_system_ids": "更新系统ID", "system_ids_updated": "系统ID更新成功", "system_ids_update_failed": "系统ID更新失败: {error}", "unsupported_os": "不支持的操作系统: {os}", "linux_path_not_found": "Linux路径未找到", "windows_guid_updated": "Windows GUID更新成功", "windows_permission_denied": "Windows权限拒绝", "windows_guid_update_failed": "Windows GUID更新失败", "macos_uuid_updated": "macOS UUID更新成功", "plutil_command_failed": "plutil命令失败", "macos_uuid_update_failed": "macOS UUID更新失败", "start_patching": "开始修补getMachineId", "current_version": "当前Cursor版本: {version}", "patch_completed": "getMachineId修补完成", "patch_failed": "getMachineId修补失败: {error}", "version_check_passed": "Cursor版本检查通过", "file_modified": "文件已修改", "version_less_than_0_45": "Cursor版本 < 0.45.0，跳过getMachineId修补", "detecting_version": "检测Cursor版本", "patching_getmachineid": "修补getMachineId", "version_greater_than_0_45": "Cursor版本 >= 0.45.0，修补getMachineId", "permission_denied": "权限拒绝: {error}", "backup_created": "备份已创建", "update_success": "更新成功", "update_failed": "更新失败: {error}", "windows_machine_guid_updated": "Windows机器GUID更新成功", "reading_package_json": "读取package.json {path}", "invalid_json_object": "JSON对象无效", "no_version_field": "package.json中没有版本字段", "version_field_empty": "版本字段为空", "invalid_version_format": "版本格式无效: {version}", "found_version": "找到版本: {version}", "version_parse_error": "版本解析错误: {error}", "package_not_found": "package.json未找到: {path}", "check_version_failed": "检查版本失败: {error}", "stack_trace": "堆栈跟踪", "version_too_low": "Cursor版本太低: {version} < 0.45.0", "no_write_permission": "没有写入权限: {path}", "path_not_found": "路径未找到: {path}", "modify_file_failed": "修改文件失败: {error}", "windows_machine_id_updated": "Windows机器ID更新成功", "update_windows_machine_id_failed": "更新Windows机器ID失败: {error}", "update_windows_machine_guid_failed": "更新Windows机器GUID失败: {error}", "file_not_found": "文件未找到: {path}"}, "register": {"title": "Cursor 注册工具", "start": "正在启动注册流程...", "browser_started": "浏览器已成功打开", "password_success": "密码设置成功", "password_error": "无法设置密码：{error}，请重试", "waiting_for_page_load": "页面加载中...", "mailbox": "已成功访问邮箱", "waiting_for_second_verification": "等待邮箱验证...", "waiting_for_verification_code": "等待验证码...", "first_verification_passed": "初始验证通过", "register_start": "开始注册流程", "form_submitted": "表单已提交，开始验证...", "filling_form": "填写注册信息", "visiting_url": "访问URL", "basic_info": "基本信息提交完成", "handling_turnstile": "正在处理安全验证...", "retry_verification": "正在重试验证...", "detect_turnstile": "正在检查安全验证...", "verification_success": "安全验证通过", "starting_browser": "正在打开浏览器...", "form_success": "表单提交成功", "handle_turnstile": "处理 Turnstile 验证", "no_turnstile": "未检测到 Turnstile 验证", "turnstile_passed": "验证通过", "verification_start": "开始获取验证码", "verification_timeout": "获取验证码超时", "verification_not_found": "未找到验证码", "try_get_code": "第 {attempt} 次尝试获取验证码 | 剩余时间: {time}秒", "get_account": "获取账户信息", "get_token": "获取 Cursor Session Token", "token_success": "Token 获取成功", "token_attempt": "第 {attempt} 次尝试未获取到 Token，{time}秒后重试", "token_max_attempts": "已达到最大尝试次数({max})，获取 Token 失败", "token_failed": "获取 Token 失败: {error}", "account_error": "获取账户信息失败: {error}", "press_enter": "按回车键退出", "browser_start": "正在启动浏览器", "open_mailbox": "正在打开邮箱页面", "email_error": "获取邮箱地址失败", "setup_error": "邮箱设置出错: {error}", "start_getting_verification_code": "开始获取验证码，将在60秒内尝试...", "get_verification_code_timeout": "获取验证码超时", "get_verification_code_success": "成功获取验证码", "try_get_verification_code": "第 {attempt} 次尝试未获取到验证码，剩余时间: {remaining_time}秒", "verification_code_filled": "验证码填写完成", "login_success_and_jump_to_settings_page": "成功登录并跳转到设置页面", "detect_login_page": "检测到登录页面，开始登录...", "cursor_registration_completed": "注册完成！", "set_password": "设置密码", "basic_info_submitted": "基本信息提交完成", "cursor_auth_info_updated": "Cursor 认证信息更新成功", "cursor_auth_info_update_failed": "Cursor 认证信息更新失败", "reset_machine_id": "重置机器ID", "account_info_saved": "账户信息已保存", "save_account_info_failed": "保存账户信息失败", "get_email_address": "获取邮箱地址", "register_process_error": "注册流程错误: {error}", "update_cursor_auth_info": "更新Cursor认证信息", "setting_password": "设置密码", "manual_code_input": "手动输入验证码", "manual_email_input": "手动输入邮箱", "suggest_email": "推荐邮箱地址: {suggested_email}", "use_suggested_email_or_enter": "输入\"yes\"使用此邮箱或直接输入您想使用的邮箱地址:", "password": "密码", "first_name": "名字", "last_name": "姓氏", "exit_signal": "退出信号", "email_address": "邮箱地址", "config_created": "配置已创建", "verification_failed": "验证失败", "verification_error": "验证错误: {error}", "config_option_added": "配置项已添加: {option}", "config_updated": "配置已更新", "password_submitted": "密码已提交", "total_usage": "总使用量: {usage}", "setting_on_password": "设置密码", "getting_code": "获取验证码，将在60秒内尝试...", "browser_path_invalid": "{browser} 路径无效，使用默认路径", "using_browser": "正在使用 {browser} 浏览器: {path}", "using_browser_profile": "使用 {browser} 配置文件: {user_data_dir}", "make_sure_browser_is_properly_installed": "确保 {browser} 已正确安装", "try_install_browser": "尝试使用包管理器安装浏览器", "tracking_processes": "正在跟踪 {count} 个 {browser} 进程", "no_new_processes_detected": "未检测到新的 {browser} 进程", "could_not_track_processes": "无法跟踪 {browser} 进程: {error}", "human_verify_error": "无法验证用户是人类，正在重试...", "max_retries_reached": "已达到最大重试次数，注册失败。", "using_tempmail_plus": "使用TempMailPlus进行邮箱验证", "tempmail_plus_enabled": "TempMailPlus已启用", "tempmail_plus_disabled": "TempMailPlus已禁用", "tempmail_plus_config_missing": "TempMailPlus配置缺失", "tempmail_plus_email_missing": "未配置TempMailPlus邮箱", "tempmail_plus_epin_missing": "未配置TempMailPlus epin", "tempmail_plus_initialized": "TempMailPlus初始化成功", "tempmail_plus_init_failed": "TempMailPlus初始化失败：{error}", "tempmail_plus_verification_started": "开始TempMailPlus验证流程", "tempmail_plus_verification_completed": "TempMailPlus验证成功完成", "tempmail_plus_verification_failed": "TempMailPlus验证失败：{error}", "checking_login_confirmation": "检查登录确认对话框", "login_confirmation_detected": "检测到登录确认页面", "clicking_login_confirmation": "点击登录确认按钮", "login_confirmation_success": "登录确认成功", "login_confirmation_failed_continue": "登录确认失败，继续访问设置页面", "login_confirmation_error": "登录确认处理错误: {error}", "js_click_failed": "JavaScript点击失败: {error}", "already_logged_in": "已经登录成功"}, "auth": {"title": "Cursor 认证管理器", "checking_auth": "检查认证文件", "auth_not_found": "未找到认证文件", "auth_file_error": "认证文件错误: {error}", "reading_auth": "读取认证文件", "updating_auth": "更新认证信息", "auth_updated": "认证信息更新成功", "auth_update_failed": "认证信息更新失败: {error}", "auth_file_created": "认证文件已创建", "auth_file_create_failed": "认证文件创建失败: {error}", "press_enter": "按回车键退出", "connected_to_database": "已连接到数据库", "database_updated_successfully": "数据库更新成功", "database_connection_closed": "数据库连接已关闭", "updating_pair": "更新键值对", "db_not_found": "未找到数据库文件：{path}", "db_permission_error": "无法访问数据库文件，请检查权限", "db_connection_error": "连接数据库失败：{error}", "reset_machine_id": "重置机ID", "path_error": "获取数据库路径失败: {error}", "init_failed": "认证管理器初始化失败"}, "control": {"generate_email": "生成新邮箱", "select_domain": "选择随机域名", "copy_email": "复制邮箱地址", "enter_mailbox": "进入邮箱", "blocked_domain": "被屏蔽的域名", "refresh_mailbox": "刷新邮箱", "check_verification": "检查验证码", "verification_found": "找到验证码", "verification_not_found": "未找到验证码", "browser_error": "浏览器控制错误: {error}", "navigation_error": "导航错误: {error}", "email_copy_error": "邮箱复制错误: {error}", "mailbox_error": "邮箱错误: {error}", "token_saved_to_file": "Token已保存到 cursor_tokens.txt", "navigate_to": "导航到 {url}", "generate_email_success": "生成邮箱成功", "select_email_domain": "选择邮箱域名", "select_email_domain_success": "选择邮箱域名成功", "get_email_name": "获取邮箱名称", "get_email_name_success": "获取邮箱名称成功", "get_email_address": "获取邮箱地址", "get_email_address_success": "获取邮箱地址成功", "enter_mailbox_success": "进入邮箱成功", "found_verification_code": "找到验证码", "get_cursor_session_token": "获取Cursor Session Token", "get_cursor_session_token_success": "获取Cursor Session Token成功", "get_cursor_session_token_failed": "获取Cursor Session Token失败", "save_token_failed": "保存Token失败", "no_valid_verification_code": "没有有效的验证码", "database_updated_successfully": "数据库成功更新", "database_connection_closed": "数据库连接关闭"}, "email": {"starting_browser": "启动浏览器", "visiting_site": "访问 邮箱服务网站", "create_success": "邮箱创建成功", "create_failed": "邮箱创建失败", "create_error": "邮箱创建错误: {error}", "refreshing": "刷新邮箱", "refresh_success": "邮箱刷新成功", "refresh_error": "邮箱刷新错误: {error}", "refresh_button_not_found": "未找到刷新按钮", "verification_found": "找到验证码", "verification_not_found": "未找到验证码", "verification_error": "验证错误: {error}", "verification_code_found": "找到验证码", "verification_code_not_found": "未找到验证码", "verification_code_error": "验证码错误: {error}", "address": "邮箱地址", "all_domains_blocked": "所有域名都被屏蔽了，切换服务", "no_available_domains_after_filtering": "过滤后没有可用域名", "switching_service": "切换到 {service} 服务", "domains_list_error": "获取域名列表失败: {error}", "failed_to_get_available_domains": "获取可用域名失败", "blocked_domains_loaded": "加载了 {count} 个被屏蔽的域名", "domains_excluded": "排除了 {domains} 个被屏蔽的域名", "failed_to_create_account": "创建账户失败", "account_creation_error": "账户创建错误: {error}", "blocked_domains": "被屏蔽的域名: {domains}", "blocked_domains_loaded_error": "加载被屏蔽的域名失败: {error}", "blocked_domains_loaded_success": "加载被屏蔽的域名成功", "blocked_domains_loaded_timeout": "加载被屏蔽的域名超时: {timeout}秒", "blocked_domains_loaded_timeout_error": "加载被屏蔽的域名超时错误: {error}", "available_domains_loaded": "获取到 {count} 个可用域名", "domains_filtered": "过滤后剩餘 {count} 個可用域名", "trying_to_create_email": "尝试创建邮箱: {email}", "domain_blocked": "域名被屏蔽: {domain}", "using_chrome_profile": "使用 Chrome 配置文件: {user_data_dir}", "no_display_found": "未找到显示器。确保 X 服务器正在运行。", "try_export_display": "尝试: export DISPLAY=:0", "extension_load_error": "加载插件失败: {error}", "make_sure_chrome_chromium_is_properly_installed": "确保 Chrome/Chromium 已正确安装", "try_install_chromium": "尝试: sudo apt install chromium-browser"}, "update": {"title": "禁用 Cursor 自动更新", "disable_success": "自动更新禁用成功", "disable_failed": "禁用自动更新失败: {error}", "press_enter": "按回车键退出", "start_disable": "开始禁用自动更新", "killing_processes": "杀死进程", "processes_killed": "进程已杀死", "removing_directory": "删除目录", "directory_removed": "目录已删除", "creating_block_file": "创建阻止文件", "block_file_created": "阻止文件已创建", "clearing_update_yml": "清空 update.yml 文件", "update_yml_cleared": "update.yml 文件已清空", "update_yml_not_found": "update.yml 文件未找到", "clear_update_yml_failed": "清空 update.yml 文件失败: {error}", "unsupported_os": "不支持的操作系统: {system}", "remove_directory_failed": "删除目录失败: {error}", "create_block_file_failed": "创建阻止文件失败: {error}", "directory_locked": "目录被锁定: {path}", "yml_locked": "update.yml 文件被锁定", "block_file_locked": "阻止文件被锁定", "yml_already_locked": "update.yml 文件已锁定", "block_file_already_locked": "阻止文件已锁定", "block_file_locked_error": "阻止文件锁定错误: {error}", "yml_locked_error": "update.yml 文件锁定错误: {error}", "block_file_already_locked_error": "阻止文件已锁定错误: {error}", "yml_already_locked_error": "update.yml 文件已锁定错误: {error}"}, "updater": {"checking": "检查更新...", "new_version_available": "有新版本可用! (当前版本: {current}, 最新版本: {latest})", "updating": "正在更新到最新版本。程序将自动重启。", "up_to_date": "您使用的是最新版本。", "check_failed": "检查更新失败: {error}", "continue_anyway": "继续使用当前版本...", "update_confirm": "是否要更新到最新版本? (Y/n)", "update_skipped": "跳过更新。", "invalid_choice": "选择无效。请输入 'Y' 或 'n'.", "development_version": "开发版本 {current} > {latest}", "changelog_title": "更新日志", "rate_limit_exceeded": "GitHub API 速率限制超过。跳过更新检查。"}, "totally_reset": {"title": "完全重置 Cursor", "checking_config": "正在检查配置文件", "config_not_found": "未找到配置文件", "no_permission": "无法读取或写入配置文件，请检查文件权限", "reading_config": "正在读取当前配置", "creating_backup": "正在创建配置备份", "backup_exists": "备份文件已存在，跳过备份步骤", "generating_new_machine_id": "正在生成新的机器 ID", "saving_new_config": "正在将新配置保存为 JSON", "success": "Cursor 重置成功", "error": "Cursor 重置失败：{error}", "press_enter": "按回车键退出", "reset_machine_id": "重置机器 ID", "database_connection_closed": "数据库连接已关闭", "database_updated_successfully": "数据库更新成功", "connected_to_database": "已连接到数据库", "updating_pair": "正在更新键值对", "db_not_found": "未找到数据库文件，路径：{path}", "db_permission_error": "无法访问数据库文件，请检查权限", "db_connection_error": "连接数据库失败：{error}", "feature_title": "功能介绍", "feature_1": "完全移除 Cursor AI 设置和配置", "feature_2": "清除所有缓存数据，包括 AI 历史记录和提示", "feature_3": "重置机器 ID 以绕过试用检测", "feature_4": "创建新的随机机器标识符", "feature_5": "移除自定义扩展和偏好设置", "feature_6": "重置试用信息和激活数据", "feature_7": "深度扫描隐藏的授权和试用相关文件", "feature_8": "安全保留非 Cursor 文件和应用程序", "feature_9": "兼容 Windows、macOS 和 Linux", "disclaimer_title": "免责声明", "disclaimer_1": "该工具将永久删除所有 Cursor AI 设置、", "disclaimer_2": "配置和缓存数据。此操作无法撤销。", "disclaimer_3": "您的代码文件 **不会** 受到影响，工具设计为", "disclaimer_4": "仅针对 Cursor AI 编辑器文件和试用检测机制。", "disclaimer_5": "系统中的其他应用程序不会受到影响。", "disclaimer_6": "运行此工具后，您需要重新设置 Cursor AI。", "disclaimer_7": "请自行承担风险", "confirm_title": "您确定要继续吗？", "confirm_1": "该操作将删除所有 Cursor AI 设置、", "confirm_2": "配置和缓存数据。此操作无法撤销。", "confirm_3": "您的代码文件 **不会** 受到影响，工具设计为", "confirm_4": "仅针对 Cursor AI 编辑器文件和试用检测机制。", "confirm_5": "系统中的其他应用程序不会受到影响。", "confirm_6": "运行此工具后，您需要重新设置 Cursor AI。", "confirm_7": "请自行承担风险", "invalid_choice": "请输入 'Y' 或 'n'", "skipped_for_safety": "出于安全原因跳过（非 Cursor 相关）：{path}", "deleted": "已删除：{path}", "error_deleting": "删除 {path} 时出错：{error}", "not_found": "未找到文件：{path}", "resetting_machine_id": "正在重置机器 ID 以绕过试用检测...", "created_machine_id": "已创建新的机器 ID：{path}", "error_creating_machine_id": "创建机器 ID 文件 {path} 时出错：{error}", "error_searching": "在 {path} 搜索文件时出错：{error}", "created_extended_trial_info": "已创建新的扩展试用信息：{path}", "error_creating_trial_info": "创建试用信息文件 {path} 时出错：{error}", "resetting_cursor_ai_editor": "正在重置 Cursor AI 编辑器... 请稍候。", "reset_cancelled": "重置已取消，未进行任何更改。", "windows_machine_id_modification_skipped": "跳过 Windows 机器 ID 修改：{error}", "linux_machine_id_modification_skipped": "跳过 Linux machine-id 修改：{error}", "note_complete_machine_id_reset_may_require_running_as_administrator": "注意：完整的机器 ID 重置可能需要以管理员身份运行", "note_complete_system_machine_id_reset_may_require_sudo_privileges": "注意：完整的系统 machine-id 重置可能需要 sudo 权限", "windows_registry_instructions": "📝 注意：在 Windows 上进行完整重置，您可能还需要清理注册表项。", "windows_registry_instructions_2": "   运行 'regedit' 并搜索 HKEY_CURRENT_USER\\Software\\ 下包含 'Cursor' 或 'CursorAI' 的键并删除它们。\n", "reset_log_1": "Cursor AI 已完全重置并绕过试用检测！", "reset_log_2": "请重启系统以使更改生效。", "reset_log_3": "您需要重新安装 Cursor AI，现在应该有一个新的试用期。", "reset_log_4": "为了获得最佳效果，建议还可以：", "reset_log_5": "注册新试用时使用不同的邮箱地址", "reset_log_6": "如果可能，使用 VPN 更改 IP 地址", "reset_log_7": "访问 Cursor AI 网站前清除浏览器的 Cookie 和缓存", "reset_log_8": "如果仍有问题，尝试将 Cursor AI 安装到不同位置", "reset_log_9": "如遇到任何问题，请到 Github Issue Tracker 提交问题：https://github.com/yeongpin/cursor-pro/issues", "unexpected_error": "发生意外错误：{error}", "report_issue": "请在 Github Issue Tracker 报告此问题：https://github.com/yeongpin/cursor-pro/issues", "keyboard_interrupt": "用户中断进程，正在退出...", "return_to_main_menu": "返回主菜单...", "process_interrupted": "进程已中断，正在退出...", "press_enter_to_return_to_main_menu": "按回车键返回主菜单...", "removing_known": "正在移除已知的试用/授权文件", "performing_deep_scan": "正在进行深度扫描以查找其他试用/授权文件", "found_additional_potential_license_trial_files": "发现 {count} 个其他潜在的试用/授权文件", "checking_for_electron_localstorage_files": "正在检查 Electron localStorage 文件", "no_additional_license_trial_files_found_in_deep_scan": "深度扫描中未发现其他试用/授权文件", "removing_electron_localstorage_files": "正在移除 Electron localStorage 文件", "electron_localstorage_files_removed": "Electron localStorage 文件已移除", "electron_localstorage_files_removal_error": "移除 Electron localStorage 文件时出错：{error}", "removing_electron_localstorage_files_completed": "Electron localStorage 文件移除完成", "warning_title": "警告", "warning_1": "此操作将删除所有 Cursor AI 设置、", "warning_2": "配置和缓存数据。此操作无法撤销。", "warning_3": "您的代码文件 **不会** 受到影响，工具设计为", "warning_4": "仅针对 Cursor AI 编辑器文件和试用检测机制。", "warning_5": "系统中的其他应用程序不会受到影响。", "warning_6": "运行此工具后，您需要重新设置 Cursor AI。", "warning_7": "请自行承担风险", "removed": "已删除：{path}", "failed_to_reset_machine_guid": "无法重置机器 GUID", "failed_to_remove": "无法删除：{path}", "failed_to_delete_file": "无法删除文件：{path}", "failed_to_delete_directory": "无法删除目录：{path}", "failed_to_delete_file_or_directory": "无法删除文件或目录：{path}", "deep_scanning": "正在进行深度扫描以查找其他试用/授权文件", "resetting_cursor": "正在重置 Cursor AI 编辑器... 请稍候。", "completed_in": "完成时间：{time} 秒", "cursor_reset_completed": "Cursor AI 编辑器已完全重置且绕过试用检测！", "cursor_reset_failed": "Cursor AI 编辑器重置失败：{error}", "cursor_reset_cancelled": "Cursor AI 编辑器重置已取消，未进行任何更改。", "operation_cancelled": "操作已取消，未进行任何更改。", "advanced_tab_error": "错误查找高级选项卡：{错误}", "already_on_settings": "已经在设置页面上", "advanced_tab_retry": "找不到高级选项卡，尝试{尝试}/{max_attempts}", "navigating_to_settings": "导航到设置页面...", "advanced_tab_not_found": "多次尝试后找不到高级标签", "login_redirect_failed": "登录重定向失败，尝试直接导航...", "delete_input_error": "错误查找删除输入：{error}", "delete_button_clicked": "单击删除帐户按钮", "direct_advanced_navigation": "尝试直接导航到高级选项卡", "delete_input_retry": "删除未找到输入，尝试{尝试}/{max_attempts}", "delete_input_not_found_continuing": "找不到删除确认输入，试图继续继续", "delete_button_retry": "找不到删除按钮，尝试{尝试}/{max_attempts}", "advanced_tab_clicked": "单击高级选项卡", "found_danger_zone": "发现的危险区域部分", "delete_input_not_found": "多次尝试后找不到删除确认输入", "delete_button_not_found": "多次尝试后找不到帐户按钮", "delete_button_error": "错误查找删除按钮：{错误}"}, "github_register": {"title": "GitHub + Cursor AI 注册自动化", "features_header": "功能", "feature1": "使用 1secmail 生成临时邮箱", "feature2": "使用随机凭证注册新的 GitHub 账户", "feature3": "自动验证 GitHub 邮箱", "feature4": "使用 GitHub 认证登录 Cursor AI", "feature5": "重置机器 ID 以绕过试用检测", "feature6": "保存所有凭证到文件", "warnings_header": "警告", "warning1": "此脚本自动化账户创建，可能违反 GitHub/Cursor 服务条款", "warning2": "需要互联网访问和管理员权限", "warning3": "CAPTCHA 或额外验证可能会中断自动化", "warning4": "请负责任地使用，风险自负", "confirm": "您确定要继续吗？", "invalid_choice": "无效选择。请输入 'yes' 或 'no'", "cancelled": "操作已取消", "program_terminated": "程序已由用户终止", "starting_automation": "开始自动化...", "github_username": "GitHub 用户名", "github_password": "GitHub 密码", "email_address": "邮箱地址", "credentials_saved": "这些凭证已保存到 github_cursor_accounts.txt", "completed_successfully": "GitHub + Cursor 注册成功", "registration_encountered_issues": "GitHub + Cursor 注册遇到问题", "check_browser_windows_for_manual_intervention_or_try_again_later": "检查浏览器窗口进行手动干预或稍后再试"}, "account_info": {"subscription": "订阅", "trial_remaining": "剩余试用", "days": "天", "subscription_not_found": "订阅信息未找到", "email_not_found": "邮箱未找到", "failed_to_get_account": "获取账户信息失败", "config_not_found": "配置未找到。", "failed_to_get_usage": "获取使用信息失败", "failed_to_get_subscription": "获取订阅信息失败", "failed_to_get_email": "获取邮箱地址失败", "failed_to_get_token": "获取 token 失败", "failed_to_get_account_info": "获取账户信息失败", "title": "账户信息", "email": "邮箱", "token": "Token", "usage": "使用量", "subscription_type": "订阅类型", "remaining_trial": "剩余试用", "days_remaining": "剩余天数", "premium": "高级", "pro": "专业", "pro_trial": "专业试用", "team": "团队", "enterprise": "企业", "free": "免费", "active": "活跃", "inactive": "非活跃", "premium_usage": "高级使用量", "basic_usage": "基础使用量", "usage_not_found": "使用量未找到", "lifetime_access_enabled": "永久访问已启用", "token_not_found": "Token 未找到"}, "config": {"config_not_available": "配置未找到。", "configuration": "配置", "enabled": "已启用", "disabled": "已禁用", "config_directory": "配置目录", "neither_cursor_nor_cursor_directory_found": "未找到 Cursor 或 Cursor 目录", "please_make_sure_cursor_is_installed_and_has_been_run_at_least_once": "请确保 Cursor 已安装并至少运行一次", "storage_directory_not_found": "未找到存储目录", "storage_file_found": "找到存储文件", "file_size": "文件大小", "file_permissions": "文件权限", "file_owner": "文件所有者", "file_group": "文件组", "error_getting_file_stats": "获取文件统计信息时出错", "permission_denied": "权限拒绝", "try_running": "尝试运行: {command}", "and": "和", "storage_file_is_empty": "存储文件为空", "the_file_might_be_corrupted_please_reinstall_cursor": "文件可能已损坏，请重新安装 Cursor", "storage_file_not_found": "未找到存储文件", "error_checking_linux_paths": "检查 Linux 路径时出错", "config_option_added": "添加配置选项", "config_updated": "配置更新", "config_created": "配置已创建", "config_setup_error": "配置设置错误", "storage_file_is_valid_and_contains_data": "存储文件有效且包含数据", "error_reading_storage_file": "读取存储文件时出错", "also_checked": "也检查了 {path}", "backup_created": "备份创建: {path}", "config_removed": "配置文件已删除用于强制更新", "backup_failed": "备份失败: {error}", "force_update_failed": "强制更新配置失败: {error}", "config_force_update_disabled": "配置文件强制更新已禁用，跳过强制更新", "config_force_update_enabled": "配置文件强制更新已启用，正在执行强制更新", "documents_path_not_found": "找不到文档路径，使用当前目录", "config_dir_created": "已创建配置目录: {path}", "using_temp_dir": "由于错误使用临时目录: {path} (错误: {error})"}, "oauth": {"authentication_button_not_found": "未找到认证按钮", "authentication_failed": "认证失败: {error}", "found_cookies": "找到 {count} 个 <PERSON>ie", "token_extraction_error": "Token 提取错误: {error}", "authentication_successful": "认证成功 - 邮箱: {email}", "missing_authentication_data": "缺少认证数据: {data}", "failed_to_delete_account": "删除账户失败: {error}", "invalid_authentication_type": "无效的认证类型", "auth_update_success": "认证更新成功", "browser_closed": "浏览器已关闭", "auth_update_failed": "认证更新失败", "google_start": "Google 开始", "github_start": "G<PERSON><PERSON> 开始", "usage_count": "使用次数: {usage}", "account_has_reached_maximum_usage": "账户已达到最大使用量, {deleting}", "starting_new_authentication_process": "开始新的认证过程...", "failed_to_delete_expired_account": "删除过期账户失败", "could_not_check_usage_count": "无法检查使用次数: {error}", "found_email": "找到邮箱: {email}", "could_not_find_email": "未找到邮箱: {error}", "could_not_find_usage_count": "未找到使用次数: {error}", "already_on_settings_page": "已处于设置页面", "failed_to_extract_auth_info": "提取认证信息失败: {error}", "no_chrome_profiles_found": "未找到 Chrome 配置文件, 使用默认配置文件", "found_default_chrome_profile": "找到默认 Chrome 配置文件", "using_first_available_chrome_profile": "使用第一个可用的 Chrome 配置文件: {profile}", "error_finding_chrome_profile": "找不到 Chrome 配置文件, 使用默认配置文件: {error}", "initializing_browser_setup": "初始化浏览器设置...", "detected_platform": "检测平台: {platform}", "running_as_root_warning": "以 root 运行不推荐用于浏览器自动化", "consider_running_without_sudo": "考虑不使用 sudo 运行脚本", "no_compatible_browser_found": "未找到兼容的浏览器。请安装 Google Chrome 或 Chromium。", "supported_browsers": "支持的浏览器: {platform}", "using_browser_profile": "使用浏览器配置文件: {profile}", "starting_browser": "正在启动浏览器: {path}", "browser_setup_completed": "浏览器设置完成", "browser_setup_failed": "浏览器设置失败: {error}", "try_running_without_sudo_admin": "尝试不使用 sudo/管理员权限运行", "redirecting_to_authenticator_cursor_sh": "重定向到 authenticator.cursor.sh...", "starting_google_authentication": "开始 Google 认证...", "starting_github_authentication": "开始 Github 认证...", "waiting_for_authentication": "等待认证...", "page_changed_checking_auth": "页面改变, 检查认证...", "status_check_error": "状态检查错误: {error}", "authentication_timeout": "认证超时", "account_is_still_valid": "账户仍然有效 (使用量: {usage})", "starting_re_authentication_process": "开始重新认证过程...", "starting_new_google_authentication": "开始新的 Google 认证...", "failed_to_delete_account_or_re_authenticate": "删除账户或重新认证失败: {error}", "navigating_to_authentication_page": "正在导航到认证页面...", "please_select_your_google_account_to_continue": "请选择您的 Google 账户以继续...", "found_browser_data_directory": "找到浏览器数据目录: {path}", "authentication_successful_getting_account_info": "认证成功, 获取账户信息...", "warning_could_not_kill_existing_browser_processes": "警告: 无法杀死现有浏览器进程: {error}", "browser_failed_to_start": "浏览器启动失败: {error}", "browser_failed": "浏览器启动失败: {error}", "browser_failed_to_start_fallback": "浏览器启动失败: {error}", "user_data_dir_not_found": "{browser} 用户数据目录未找到：{path}，将尝试使用 Chrome", "error_getting_user_data_directory": "获取用户数据目录出错：{error}", "warning_browser_close": "警告：这将关闭所有正在运行的 {browser} 进程", "killing_browser_processes": "正在关闭 {browser} 进程...", "profile_selection_error": "配置文件选择过程中出错: {error}", "using_configured_browser_path": "使用配置的 {browser} 路径: {path}", "browser_not_found_trying_chrome": "未找到 {browser}，尝试使用 Chrome 代替", "found_chrome_at": "找到 Chrome: {path}", "found_browser_user_data_dir": "找到 {browser} 用户数据目录: {path}", "select_profile": "选择要使用的 {browser} 配置文件：", "profile_list": "可用 {browser} 配置文件：", "chrome_permissions_fixed": "已修复 Chrome 用户数据目录权限", "chrome_permissions_fix_failed": "修复 Chrome 权限失败: {error}"}, "browser_profile": {"title": "浏览器配置文件选择", "select_profile": "选择要使用的{browser}配置文件：", "profile_list": "可用{browser}配置文件：", "default_profile": "默认配置文件", "profile": "配置文件 {number}", "no_profiles": "未找到{browser}配置文件", "error_loading": "加载{browser}配置文件时出错：{error}", "profile_selected": "已选择配置文件：{profile}", "invalid_selection": "选择无效。请重试"}, "account_delete": {"title": "Cursor Google 账号删除工具", "warning": "警告：这将永久删除您的 Cursor 账号。此操作无法撤销。", "cancelled": "账号删除已取消。", "starting_process": "开始账号删除过程...", "google_button_not_found": "未找到 Google 登录按钮", "logging_in": "正在使用 Google 登录...", "waiting_for_auth": "等待 Google 验证...", "login_successful": "登录成功", "unexpected_page": "登录后页面异常：{url}", "trying_settings": "尝试导航到设置页面...", "select_google_account": "请选择您的 Google 账号...", "auth_timeout": "认证超时，继续执行...", "navigating_to_settings": "正在导航到设置页面...", "already_on_settings": "已在设置页面", "login_redirect_failed": "登录重定向失败，尝试直接导航...", "advanced_tab_not_found": "多次尝试后未找到高级选项卡", "advanced_tab_retry": "未找到高级选项卡，尝试 {attempt}/{max_attempts}", "advanced_tab_error": "查找高级选项卡时出错：{error}", "advanced_tab_clicked": "已点击高级选项卡", "direct_advanced_navigation": "尝试直接导航到高级选项卡", "delete_button_not_found": "多次尝试后未找到删除账号按钮", "delete_button_retry": "未找到删除按钮，尝试 {attempt}/{max_attempts}", "delete_button_error": "查找删除按钮时出错：{error}", "delete_button_clicked": "已点击删除账号按钮", "delete_input_not_found": "多次尝试后未找到删除确认输入框", "delete_input_retry": "未找到删除输入框，尝试 {attempt}/{max_attempts}", "delete_input_error": "查找删除输入框时出错：{error}", "delete_input_not_found_continuing": "未找到删除确认输入框，尝试继续执行...", "typed_delete": "已在确认框中输入\"Delete\"", "confirm_button_not_found": "多次尝试后未找到确认按钮", "confirm_button_retry": "未找到确认按钮，尝试 {attempt}/{max_attempts}", "confirm_button_error": "查找确认按钮时出错：{error}", "account_deleted": "账号删除成功！", "error": "账号删除过程中出错：{error}", "success": "您的 Cursor 账号已成功删除！", "failed": "账号删除过程失败或已取消。", "interrupted": "账号删除过程被用户中断。", "unexpected_error": "意外错误：{error}", "found_email": "找到邮箱：{email}", "email_not_found": "未找到邮箱: {error}", "found_danger_zone": "已找到危险区域部分", "confirm_prompt": "您确定要继续吗？(y/N): "}, "bypass": {"starting": "开始绕过 Cursor 版本限制...", "found_product_json": "找到 product.json: {path}", "no_write_permission": "没有写入权限: {path}", "read_failed": "读取 product.json 失败: {error}", "current_version": "当前版本: {version}", "backup_created": "备份创建: {path}", "version_updated": "版本从 {old} 更新到 {new}", "write_failed": "写入 product.json 失败: {error}", "no_update_needed": "不需要更新。当前版本 {version} 已 >= 0.46.0", "bypass_failed": "绕过版本限制失败: {error}", "stack_trace": "堆栈跟踪", "localappdata_not_found": "LOCALAPPDATA 环境变量未找到", "product_json_not_found": "product.json 未在常见 Linux 路径中找到", "unsupported_os": "不支持的操作系统: {system}", "file_not_found": "文件未找到: {path}", "title": "Cursor 版本绕过工具", "description": "此工具修改 Cursor 的 product.json 以绕过版本限制", "menu_option": "绕过 Cursor 版本检查"}, "auth_check": {"checking_authorization": "检查授权...", "token_source": "从数据库获取 token 或手动输入？（d/m, 默认: d）", "getting_token_from_db": "从数据库获取 token...", "token_found_in_db": "在数据库中找到 token", "token_not_found_in_db": "在数据库中未找到 token", "cursor_acc_info_not_found": "cursor_acc_info.py 未找到", "error_getting_token_from_db": "从数据库获取 token 时出错: {error}", "enter_token": "请输入您的 Cursor token: ", "token_length": "token 长度: {length}", "usage_response_status": "使用情况响应状态: {response}", "unexpected_status_code": "意外状态码: {code}", "jwt_token_warning": "token 似乎是 JWT 格式，但 API 检查返回意外状态码。token 可能有效但 API 访问受限。", "invalid_token": "无效的 token", "user_authorized": "用户已授权", "user_unauthorized": "用户未授权", "request_timeout": "请求超时", "connection_error": "连接错误", "check_error": "检查授权时出错: {error}", "authorization_successful": "授权成功", "authorization_failed": "授权失败", "operation_cancelled": "操作已取消", "unexpected_error": "意外错误: {error}", "error_generating_checksum": "生成校验和时出错: {error}", "checking_usage_information": "检查使用情况...", "check_usage_response": "检查使用情况响应: {response}", "usage_response": "使用情况响应: {response}"}, "bypass_token_limit": {"title": "绕过 Token 限制工具", "description": "此工具修改 workbench.desktop.main.js 文件以绕过 token 限制", "press_enter": "按回车键继续..."}, "token": {"refreshing": "正在刷新令牌...", "refresh_success": "令牌刷新成功！有效期 {days} 天（到期时间: {expire}）", "no_access_token": "响应中没有访问令牌", "refresh_failed": "令牌刷新失败: {error}", "invalid_response": "刷新服务器返回无效的 JSON 响应", "server_error": "刷新服务器错误: HTTP {status}", "request_timeout": "刷新服务器请求超时", "connection_error": "连接刷新服务器错误", "unexpected_error": "令牌刷新过程中出现意外错误: {error}", "extraction_error": "提取令牌时出错: {error}"}, "restore": {"title": "从备份恢复机器ID", "starting": "正在启动机器ID恢复进程", "no_backups_found": "未找到备份文件", "available_backups": "可用的备份文件", "select_backup": "选择要恢复的备份", "to_cancel": "取消操作", "operation_cancelled": "操作已取消", "invalid_selection": "选择无效", "please_enter_number": "请输入有效的数字", "missing_id": "缺少ID: {id}", "read_backup_failed": "读取备份文件失败: {error}", "current_file_not_found": "未找到当前存储文件", "current_backup_created": "已创建当前存储文件的备份", "storage_updated": "存储文件已成功更新", "update_failed": "更新存储文件失败: {error}", "sqlite_not_found": "未找到SQLite数据库", "updating_sqlite": "正在更新SQLite数据库", "updating_pair": "正在更新键值对", "sqlite_updated": "SQLite数据库已成功更新", "sqlite_update_failed": "更新SQLite数据库失败: {error}", "machine_id_backup_created": "已创建machineId文件的备份", "backup_creation_failed": "创建备份失败: {error}", "machine_id_updated": "machineId文件已成功更新", "machine_id_update_failed": "更新machineId文件失败: {error}", "updating_system_ids": "正在更新系统ID", "system_ids_update_failed": "更新系统ID失败: {error}", "permission_denied": "权限被拒绝。请尝试以管理员身份运行", "windows_machine_guid_updated": "Windows机器GUID已成功更新", "update_windows_machine_guid_failed": "更新Windows机器GUID失败: {error}", "windows_machine_id_updated": "Windows机器ID已成功更新", "update_windows_machine_id_failed": "更新Windows机器ID失败: {error}", "sqm_client_key_not_found": "未找到SQMClient注册表项", "update_windows_system_ids_failed": "更新Windows系统ID失败: {error}", "macos_platform_uuid_updated": "macOS平台UUID已成功更新", "failed_to_execute_plutil_command": "执行plutil命令失败", "update_macos_system_ids_failed": "更新macOS系统ID失败: {error}", "ids_to_restore": "要恢复的机器ID", "confirm": "您确定要恢复这些ID吗？", "success": "机器ID已成功恢复", "process_error": "恢复过程错误: {error}", "press_enter": "按Enter键继续"}, "manual_auth": {"token_verification_skipped": "跳过令牌验证（Check_user_authorized.py找不到）", "auth_updated_successfully": "身份验证信息成功更新了！", "auth_type_selected": "选定的身份验证类型：{type}", "proceed_prompt": "继续？ （y/n）：", "token_required": "需要令牌", "continue_anyway": "无论如何继续？ （y/n）：", "auth_type_google": "谷歌", "auth_type_github": "github", "random_email_generated": "生成的随机电子邮件：{电子邮件}", "verifying_token": "验证令牌有效性...", "auth_type_prompt": "选择身份验证类型：", "operation_cancelled": "操作取消了", "error": "错误：{错误}", "email_prompt": "输入电子邮件（留空白以获取随机电子邮件）：", "auth_type_auth0": "auth_0（默认）", "token_verification_error": "错误验证令牌：{error}", "token_prompt": "输入光标令牌（access_token/refresh_token）：", "invalid_token": "无效的令牌。身份验证中止。", "confirm_prompt": "请确认以下信息：", "auth_update_failed": "无法更新身份验证信息", "title": "手动Cursor身份验证", "token_verified": "令牌成功验证了！", "updating_database": "更新Cursor身份验证数据库..."}, "tempmail": {"general_error": "发生错误：{error}", "no_email": "找不到Cursor验证电子邮件", "configured_email": "配置的电子邮件：{email}", "config_error": "配置文件错误：{error}", "extract_code_failed": "提取验证代码失败：{error}", "no_code": "无法获得验证代码", "check_email_failed": "检查电子邮件失败：{error}", "checking_email": "检查Cursor验证电子邮件...", "email_found": "找到Cursor验证电子邮件", "verification_code": "验证代码：{code}"}, "backup_session": {"restore_success": "会话恢复成功", "restore_failed": "会话恢复失败", "backup_success": "会话备份成功", "backup_failed": "会话备份失败", "backup_in_progress": "正在备份会话...", "restore_in_progress": "正在恢复会话...", "refresh_token_no_token_error": "无法刷新Token：未提供Token", "refresh_token_attempt": "正在尝试刷新Token...", "refresh_token_success": "Token刷新成功", "refresh_token_verification_failed": "新Token验证失败", "refresh_token_invalid_response": "刷新Token响应无效或格式错误", "refresh_token_parse_error": "解析刷新Token响应时出错", "refresh_token_unauthorized": "Token刷新未授权，可能已过期", "refresh_token_failed": "Token刷新失败，状态码", "refresh_token_request_error": "Token刷新请求错误", "refresh_token_retry": "正在重试刷新Token，尝试次数", "refresh_token_original_valid": "原Token仍然有效，将继续使用", "refresh_token_all_attempts_failed": "所有Token刷新尝试均失败", "verify_token_error": "验证Token时出错"}}