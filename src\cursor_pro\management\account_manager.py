import os
from colorama import Fore, Style
import re

# Define emoji constants
EMOJI = {
    'SUCCESS': '✅',
    'ERROR': '❌',
    'INFO': 'ℹ️'
}

class AccountManager:
    def __init__(self, translator=None):
        self.translator = translator
        self.accounts_file = 'cursor_accounts.txt'
    
    def save_account_info(self, email, password, token, total_usage):
        """Save account information to file"""
        try:
            # 获取机器码信息
            machine_ids = self._get_machine_ids()

            with open(self.accounts_file, 'a', encoding='utf-8') as f:
                f.write(f"\n{'='*50}\n")
                f.write(f"Email: {email}\n")
                f.write(f"Password: {password}\n")
                f.write(f"Token: {token}\n")
                f.write(f"Usage Limit: {total_usage}\n")

                # 保存机器码信息
                if machine_ids:
                    f.write(f"Machine IDs:\n")
                    for key, value in machine_ids.items():
                        if value:  # 只保存非空值
                            f.write(f"  {key}: {value}\n")

                f.write(f"{'='*50}\n")

            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} {self.translator.get('register.account_info_saved') if self.translator else 'Account information saved'}...{Style.RESET_ALL}")
            return True
            
        except Exception as e:
            error_msg = self.translator.get('register.save_account_info_failed', error=str(e)) if self.translator else f'Failed to save account information: {str(e)}'
            print(f"{Fore.RED}{EMOJI['ERROR']} {error_msg}{Style.RESET_ALL}")
            return False
    
    def get_last_email_domain(self):
        """Get the domain from the last used email"""
        try:
            if not os.path.exists(self.accounts_file):
                return None
            
            # Only read the last 1KB of data from the file
            with open(self.accounts_file, 'rb') as f:
                # Get file size
                f.seek(0, os.SEEK_END)
                file_size = f.tell()
                
                if file_size == 0:
                    return None
                
                # Determine the number of bytes to read, maximum 1KB
                read_size = min(1024, file_size)
                
                # Move to the appropriate position to start reading
                f.seek(file_size - read_size)
                
                # Read the end data
                data = f.read(read_size).decode('utf-8', errors='ignore')
            
            # Split by lines and search in reverse
            lines = data.split('\n')
            for line in reversed(lines):
                if line.strip().startswith('Email:'):
                    email = line.split('Email:')[1].strip()
                    # Extract domain part (after @)
                    if '@' in email:
                        return email.split('@')[1]
                    return None
            
            # If no email is found in the last 1KB
            return None
            
        except Exception as e:
            error_msg = self.translator.get('account.get_last_email_domain_failed', error=str(e)) if self.translator else f'Failed to get the last used email domain: {str(e)}'
            print(f"{Fore.RED}{EMOJI['ERROR']} {error_msg}{Style.RESET_ALL}")
            return None
    
    def get_registered_accounts(self):
        """获取已注册的账户列表"""
        try:
            if not os.path.exists(self.accounts_file):
                return []

            accounts = []
            with open(self.accounts_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 按分隔符分割账户信息
            account_blocks = content.split('=' * 50)

            for block in account_blocks:
                if not block.strip():
                    continue

                account = {}
                lines = block.strip().split('\n')

                for line in lines:
                    if ':' in line:
                        key, value = line.split(':', 1)
                        key = key.strip()
                        value = value.strip()

                        if key == 'Email':
                            account['email'] = value
                        elif key == 'Password':
                            account['password'] = value
                        elif key == 'Token':
                            account['access_token'] = value
                        elif key == 'Usage Limit':
                            account['usage_limit'] = value

                if account.get('email'):
                    accounts.append(account)

            return accounts

        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 获取账户列表失败: {str(e)}{Style.RESET_ALL}")
            return []

    def suggest_email(self, first_name, last_name):
        """Generate a suggested email based on first and last name with the last used domain"""
        try:
            # Get the last used email domain
            domain = self.get_last_email_domain()
            if not domain:
                return None

            # Generate email prefix from first and last name (lowercase)
            email_prefix = f"{first_name.lower()}.{last_name.lower()}"

            # Combine prefix and domain
            suggested_email = f"{email_prefix}@{domain}"
            
            return suggested_email
        
        except Exception as e:
            error_msg = self.translator.get('account.suggest_email_failed', error=str(e)) if self.translator else f'Failed to suggest email: {str(e)}'
            print(f"{Fore.RED}{EMOJI['ERROR']} {error_msg}{Style.RESET_ALL}")
            return None

    def _get_machine_ids(self):
        """获取当前机器码信息"""
        try:
            from cursor_pro.management.config import get_config
            import sys
            import json
            import os

            config = get_config()
            if not config:
                return None

            # 获取路径
            if sys.platform == "win32":
                storage_path = config.get('WindowsPaths', 'storage_path')
            elif sys.platform == 'linux':
                storage_path = config.get('LinuxPaths', 'storage_path')
            elif sys.platform == 'darwin':
                storage_path = config.get('MacPaths', 'storage_path')
            else:
                return None

            if not os.path.exists(storage_path):
                return None

            # 读取机器码信息
            with open(storage_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            # 提取机器码
            machine_ids = {
                "telemetry.devDeviceId": data.get("telemetry.devDeviceId", ""),
                "telemetry.macMachineId": data.get("telemetry.macMachineId", ""),
                "telemetry.machineId": data.get("telemetry.machineId", ""),
                "telemetry.sqmId": data.get("telemetry.sqmId", ""),
                "storage.serviceMachineId": data.get("storage.serviceMachineId", "")
            }

            return machine_ids
        except Exception as e:
            print(f"{Fore.YELLOW}{EMOJI['INFO']} 获取机器码信息失败: {str(e)}{Style.RESET_ALL}")
            return None
