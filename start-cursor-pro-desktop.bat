@echo off
chcp 65001 >nul 2>&1
title Cursor Pro - 桌面版启动器
color 0B

echo.
echo ========================================
echo   🖥️ Cursor Pro 桌面版启动器
echo ========================================
echo.

echo [1/2] Starting backend API server...
set "PYTHONPATH=%cd%\src"
start "Cursor Pro Backend" cmd /k "title Cursor Pro Backend API && set PYTHONPATH=%cd%\src && python -m cursor_pro.core.api_server"
echo ✅ Backend server started in separate window

echo.
echo [2/2] Starting desktop application...
echo Waiting 3 seconds for backend to initialize...
timeout /t 3 /nobreak >nul

cd frontend\cursor-pro-vue
echo Starting Electron desktop app...

if exist "C:\Program Files\nodejs\npm.cmd" (
    "C:\Program Files\nodejs\npm.cmd" run electron:dev
) else (
    npm run electron:dev
)

echo.
echo Desktop application closed
echo Backend server is still running in separate window
pause
