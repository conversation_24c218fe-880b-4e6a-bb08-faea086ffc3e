/**
 * 日志相关API服务
 * 处理日志的获取、过滤、导出等操作
 */

import { BaseAPI } from '../base'
import type {
  LogEntry,
  LogsRequest,
  LogsResponse,
  PaginationParams
} from '../types'

/**
 * 日志服务类
 * 继承BaseAPI，提供日志相关的API调用
 */
export class LogService extends BaseAPI {
  /**
   * 获取日志列表
   */
  async getLogs(params?: LogsRequest & PaginationParams): Promise<LogsResponse> {
    console.log('🔄 [LogService] 获取日志列表:', params)

    const response = await this.get<LogsResponse>('logs', params)
    console.log('📡 [LogService] 日志列表响应:', response)
    
    return response.data
  }

  /**
   * 获取实时日志流
   */
  async getRealtimeLogs(): Promise<EventSource> {
    console.log('🔄 [LogService] 建立实时日志连接')

    const url = `${this.baseURL || 'http://localhost:8080'}/api/logs/stream`
    const eventSource = new EventSource(url)
    
    eventSource.onopen = () => {
      console.log('📡 [LogService] 实时日志连接已建立')
    }
    
    eventSource.onerror = (error) => {
      console.error('❌ [LogService] 实时日志连接错误:', error)
    }
    
    return eventSource
  }

  /**
   * 添加日志条目
   */
  async addLog(entry: Omit<LogEntry, 'id' | 'timestamp'>): Promise<LogEntry> {
    console.log('🔄 [LogService] 添加日志条目:', entry)

    const response = await this.post<LogEntry>('logs', entry)
    console.log('📡 [LogService] 添加日志响应:', response)
    
    return response.data
  }

  /**
   * 批量添加日志
   */
  async addLogs(entries: Array<Omit<LogEntry, 'id' | 'timestamp'>>): Promise<LogEntry[]> {
    console.log('🔄 [LogService] 批量添加日志:', entries.length, '条')

    const response = await this.post<LogEntry[]>('logs/batch', { entries })
    console.log('📡 [LogService] 批量添加日志响应:', response)
    
    return response.data
  }

  /**
   * 清空日志
   */
  async clearLogs(filters?: {
    level?: string
    source?: string
    olderThan?: string
  }): Promise<{
    success: boolean
    deletedCount: number
  }> {
    console.log('🔄 [LogService] 清空日志:', filters)

    const response = await this.delete<{
      success: boolean
      deletedCount: number
    }>('logs', filters)
    
    console.log('📡 [LogService] 清空日志响应:', response)
    return response.data
  }

  /**
   * 导出日志
   */
  async exportLogs(params?: LogsRequest & {
    format?: 'json' | 'csv' | 'txt'
  }): Promise<Blob> {
    console.log('🔄 [LogService] 导出日志:', params)

    const response = await this.get<any>('logs/export', params)
    
    // 根据格式创建Blob
    const format = params?.format || 'json'
    let mimeType = 'application/json'
    let content = JSON.stringify(response.data, null, 2)
    
    if (format === 'csv') {
      mimeType = 'text/csv'
      content = this.convertToCSV(response.data.logs || [])
    } else if (format === 'txt') {
      mimeType = 'text/plain'
      content = this.convertToText(response.data.logs || [])
    }
    
    const blob = new Blob([content], { type: mimeType })
    console.log('📡 [LogService] 日志导出完成')
    
    return blob
  }

  /**
   * 获取日志统计
   */
  async getLogStats(params?: {
    startTime?: string
    endTime?: string
    groupBy?: 'level' | 'source' | 'hour' | 'day'
  }): Promise<{
    total: number
    byLevel: Record<string, number>
    bySource: Record<string, number>
    timeline: Array<{
      time: string
      count: number
    }>
  }> {
    console.log('🔄 [LogService] 获取日志统计:', params)

    const response = await this.get<{
      total: number
      byLevel: Record<string, number>
      bySource: Record<string, number>
      timeline: Array<{
        time: string
        count: number
      }>
    }>('logs/stats', params)
    
    console.log('📡 [LogService] 日志统计响应:', response)
    return response.data
  }

  /**
   * 搜索日志
   */
  async searchLogs(query: string, params?: {
    level?: string
    source?: string
    startTime?: string
    endTime?: string
    limit?: number
  }): Promise<LogsResponse> {
    console.log('🔄 [LogService] 搜索日志:', query, params)

    const response = await this.get<LogsResponse>('logs/search', {
      q: query,
      ...params
    })
    
    console.log('📡 [LogService] 日志搜索响应:', response)
    return response.data
  }

  /**
   * 获取日志详情
   */
  async getLogDetail(logId: string): Promise<LogEntry & {
    context?: LogEntry[]
    related?: LogEntry[]
  }> {
    console.log('🔄 [LogService] 获取日志详情:', logId)

    const response = await this.get<LogEntry & {
      context?: LogEntry[]
      related?: LogEntry[]
    }>(`logs/${logId}`)
    
    console.log('📡 [LogService] 日志详情响应:', response)
    return response.data
  }

  /**
   * 设置日志级别
   */
  async setLogLevel(level: 'debug' | 'info' | 'warn' | 'error'): Promise<boolean> {
    console.log('🔄 [LogService] 设置日志级别:', level)

    const response = await this.post<{ success: boolean }>('logs/level', { level })
    console.log('📡 [LogService] 设置日志级别响应:', response)
    
    return response.data.success
  }

  /**
   * 获取日志配置
   */
  async getLogConfig(): Promise<{
    level: string
    maxSize: number
    maxFiles: number
    retention: number
    sources: string[]
  }> {
    console.log('🔄 [LogService] 获取日志配置')

    const response = await this.get<{
      level: string
      maxSize: number
      maxFiles: number
      retention: number
      sources: string[]
    }>('logs/config')
    
    console.log('📡 [LogService] 日志配置响应:', response)
    return response.data
  }

  /**
   * 更新日志配置
   */
  async updateLogConfig(config: {
    level?: string
    maxSize?: number
    maxFiles?: number
    retention?: number
  }): Promise<boolean> {
    console.log('🔄 [LogService] 更新日志配置:', config)

    const response = await this.put<{ success: boolean }>('logs/config', config)
    console.log('📡 [LogService] 更新日志配置响应:', response)
    
    return response.data.success
  }

  /**
   * 转换为CSV格式
   */
  private convertToCSV(logs: LogEntry[]): string {
    if (logs.length === 0) return ''
    
    const headers = ['timestamp', 'level', 'message', 'source']
    const rows = logs.map(log => [
      log.timestamp,
      log.level,
      `"${log.message.replace(/"/g, '""')}"`,
      log.source || ''
    ])
    
    return [headers.join(','), ...rows.map(row => row.join(','))].join('\n')
  }

  /**
   * 转换为文本格式
   */
  private convertToText(logs: LogEntry[]): string {
    return logs.map(log => 
      `[${log.timestamp}] ${log.level.toUpperCase()}: ${log.message}${log.source ? ` (${log.source})` : ''}`
    ).join('\n')
  }
}

// 创建日志服务实例
export const logService = new LogService()

// 添加默认拦截器
logService.addRequestInterceptor(async (config) => {
  // 为日志请求添加特殊标识
  config.headers = {
    ...config.headers,
    'X-Service': 'LogService'
  }
  return config
})

logService.addResponseInterceptor(async (response) => {
  // 记录日志API的响应时间
  const responseTime = Date.now() - (response.timestamp || 0)
  if (responseTime > 1000) {
    console.warn(`⚠️ [LogService] 慢查询检测: ${responseTime}ms`)
  }
  return response
})

logService.addErrorInterceptor(async (error) => {
  // 日志服务的错误不应该影响主要功能
  console.error('❌ [LogService] 日志服务错误:', {
    type: error.type,
    message: error.message,
    status: error.status
  })
  return error
})
