"""
遗留路径适配器
为现有代码提供向后兼容的路径解析，逐步迁移到新的数据路径系统
"""

import os
from pathlib import Path
from typing import Union, Optional
from cursor_pro.management.data_path import get_data_path_manager


class LegacyPathAdapter:
    """遗留路径适配器"""
    
    def __init__(self):
        self.path_manager = get_data_path_manager()
        self.project_dir = Path(__file__).parent.absolute()
        
        # 文件类型映射
        self.file_type_mapping = {
            # 数据文件
            'cursor_accounts.txt': 'data',
            'cursor_accounts_backup.txt': 'data', 
            'cursor_pro.db': 'data',
            'block_domain.txt': 'data',
            
            # 配置文件
            'config.ini': 'config',
            'settings.json': 'config',
            'register_config.json': 'config',
            
            # 日志文件
            'app.log': 'logs',
            'debug.log': 'logs',
            'error.log': 'logs',
            
            # 备份文件
            'backup': 'backups',
        }
    
    def resolve_file_path(self, filename: str, create_if_missing: bool = True) -> Path:
        """
        解析文件路径，优先使用新位置，如果不存在则检查旧位置
        
        Args:
            filename: 文件名
            create_if_missing: 如果文件不存在是否创建目录结构
            
        Returns:
            Path: 解析后的文件路径
        """
        # 确定文件类型
        file_type = self._determine_file_type(filename)
        
        # 获取新位置的路径
        new_path = self.path_manager.get_file_path(file_type, filename)
        
        # 如果新位置存在文件，直接返回
        if new_path.exists():
            return new_path
        
        # 检查旧位置（项目目录）
        old_path = self.project_dir / filename
        if old_path.exists():
            # 如果旧位置存在，提示用户迁移
            print(f"⚠️  发现旧位置的文件: {old_path}")
            print(f"   建议运行数据迁移工具将其移动到: {new_path}")
            return old_path
        
        # 两个位置都不存在，返回新位置（用于创建新文件）
        if create_if_missing:
            # 确保目录存在
            new_path.parent.mkdir(parents=True, exist_ok=True)
        
        return new_path
    
    def _determine_file_type(self, filename: str) -> str:
        """确定文件类型"""
        # 直接匹配
        if filename in self.file_type_mapping:
            return self.file_type_mapping[filename]
        
        # 模式匹配
        filename_lower = filename.lower()
        
        if any(pattern in filename_lower for pattern in ['backup', '.bak']):
            return 'backups'
        elif filename_lower.endswith('.log'):
            return 'logs'
        elif filename_lower.endswith(('.ini', '.json', '.yaml', '.yml', '.toml')):
            return 'config'
        elif filename_lower.endswith(('.db', '.sqlite', '.sqlite3')):
            return 'data'
        elif filename_lower.endswith(('.tmp', '.temp')):
            return 'temp'
        else:
            # 默认为数据文件
            return 'data'
    
    def get_accounts_file(self) -> Path:
        """获取账户文件路径"""
        return self.resolve_file_path('cursor_accounts.txt')
    
    def get_accounts_backup_file(self) -> Path:
        """获取账户备份文件路径"""
        return self.resolve_file_path('cursor_accounts_backup.txt')
    
    def get_config_file(self) -> Path:
        """获取配置文件路径"""
        return self.resolve_file_path('config.ini')
    
    def get_database_file(self) -> Path:
        """获取数据库文件路径"""
        return self.resolve_file_path('cursor_pro.db')
    
    def get_block_domain_file(self) -> Path:
        """获取域名屏蔽文件路径"""
        return self.resolve_file_path('block_domain.txt')
    
    def list_backup_files(self, pattern: str = "*.txt") -> list:
        """列出备份文件"""
        backup_files = []
        
        # 检查新位置
        new_backups = self.path_manager.list_files('backups', pattern)
        backup_files.extend(new_backups)
        
        # 检查旧位置
        old_backup_patterns = [
            'accounts_backup_*.txt',
            'accounts_restore_backup_*.txt',
            '*.backup'
        ]
        
        for backup_pattern in old_backup_patterns:
            old_backups = list(self.project_dir.glob(backup_pattern))
            backup_files.extend(old_backups)
        
        return backup_files
    
    def safe_read_file(self, filename: str, encoding: str = 'utf-8', default: str = '') -> str:
        """安全读取文件内容"""
        try:
            file_path = self.resolve_file_path(filename, create_if_missing=False)
            if file_path.exists():
                return file_path.read_text(encoding=encoding)
            else:
                return default
        except Exception as e:
            print(f"读取文件失败 {filename}: {e}")
            return default
    
    def safe_write_file(self, filename: str, content: str, encoding: str = 'utf-8') -> bool:
        """安全写入文件内容"""
        try:
            file_path = self.resolve_file_path(filename, create_if_missing=True)
            file_path.write_text(content, encoding=encoding)
            return True
        except Exception as e:
            print(f"写入文件失败 {filename}: {e}")
            return False
    
    def safe_append_file(self, filename: str, content: str, encoding: str = 'utf-8') -> bool:
        """安全追加文件内容"""
        try:
            file_path = self.resolve_file_path(filename, create_if_missing=True)
            with open(file_path, 'a', encoding=encoding) as f:
                f.write(content)
            return True
        except Exception as e:
            print(f"追加文件失败 {filename}: {e}")
            return False
    
    def file_exists(self, filename: str) -> bool:
        """检查文件是否存在"""
        file_path = self.resolve_file_path(filename, create_if_missing=False)
        return file_path.exists()
    
    def get_file_size(self, filename: str) -> int:
        """获取文件大小"""
        try:
            file_path = self.resolve_file_path(filename, create_if_missing=False)
            if file_path.exists():
                return file_path.stat().st_size
            return 0
        except Exception:
            return 0
    
    def get_file_mtime(self, filename: str) -> float:
        """获取文件修改时间"""
        try:
            file_path = self.resolve_file_path(filename, create_if_missing=False)
            if file_path.exists():
                return file_path.stat().st_mtime
            return 0.0
        except Exception:
            return 0.0
    
    def create_backup_filename(self, original_filename: str, suffix: str = None) -> str:
        """创建备份文件名"""
        from datetime import datetime
        
        if suffix is None:
            suffix = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        name_parts = original_filename.rsplit('.', 1)
        if len(name_parts) == 2:
            name, ext = name_parts
            return f"{name}_backup_{suffix}.{ext}"
        else:
            return f"{original_filename}_backup_{suffix}"
    
    def get_migration_status(self) -> dict:
        """获取迁移状态信息"""
        status = {
            'files_in_old_location': [],
            'files_in_new_location': [],
            'migration_needed': False
        }
        
        # 检查常见文件
        common_files = [
            'cursor_accounts.txt',
            'cursor_accounts_backup.txt', 
            'cursor_pro.db',
            'config.ini',
            'block_domain.txt'
        ]
        
        for filename in common_files:
            old_path = self.project_dir / filename
            new_path = self.path_manager.get_file_path(self._determine_file_type(filename), filename)
            
            if old_path.exists():
                status['files_in_old_location'].append(filename)
                status['migration_needed'] = True
            
            if new_path.exists():
                status['files_in_new_location'].append(filename)
        
        return status


# 全局实例
legacy_adapter = LegacyPathAdapter()


def get_legacy_adapter() -> LegacyPathAdapter:
    """获取遗留路径适配器实例"""
    return legacy_adapter


# 便捷函数，用于快速替换现有代码中的文件操作
def get_file_path(filename: str) -> str:
    """获取文件路径（字符串格式，兼容现有代码）"""
    return str(legacy_adapter.resolve_file_path(filename))


def read_file(filename: str, encoding: str = 'utf-8', default: str = '') -> str:
    """读取文件内容"""
    return legacy_adapter.safe_read_file(filename, encoding, default)


def write_file(filename: str, content: str, encoding: str = 'utf-8') -> bool:
    """写入文件内容"""
    return legacy_adapter.safe_write_file(filename, content, encoding)


def append_file(filename: str, content: str, encoding: str = 'utf-8') -> bool:
    """追加文件内容"""
    return legacy_adapter.safe_append_file(filename, content, encoding)


def file_exists(filename: str) -> bool:
    """检查文件是否存在"""
    return legacy_adapter.file_exists(filename)


if __name__ == "__main__":
    # 测试代码
    adapter = LegacyPathAdapter()
    
    print("=== 遗留路径适配器测试 ===")
    
    # 测试路径解析
    test_files = [
        'cursor_accounts.txt',
        'config.ini',
        'app.log',
        'backup_test.txt'
    ]
    
    for filename in test_files:
        path = adapter.resolve_file_path(filename, create_if_missing=False)
        file_type = adapter._determine_file_type(filename)
        print(f"{filename:20} -> {file_type:8} -> {path}")
    
    # 显示迁移状态
    print("\n=== 迁移状态 ===")
    status = adapter.get_migration_status()
    print(f"需要迁移: {status['migration_needed']}")
    print(f"旧位置文件: {status['files_in_old_location']}")
    print(f"新位置文件: {status['files_in_new_location']}")
