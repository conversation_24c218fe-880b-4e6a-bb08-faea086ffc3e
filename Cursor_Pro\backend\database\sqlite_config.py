#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQLite配置模块
为Cursor Pro提供SQLite数据库配置
"""

import os
import sqlite3
from pathlib import Path
from typing import Optional

def get_user_documents_path() -> str:
    """获取用户文档目录路径"""
    if os.name == 'nt':  # Windows
        return os.path.expanduser('~\\Documents')
    else:  # macOS/Linux
        return os.path.expanduser('~')

def get_sqlite_db_path() -> str:
    """获取SQLite数据库文件路径"""
    try:
        # 优先使用统一的数据路径管理器
        from data_path_manager import get_data_path_manager
        path_manager = get_data_path_manager()
        return str(path_manager.get_data_file('cursor_pro.db'))
    except ImportError:
        # 降级到原来的逻辑（向后兼容）
        if os.name == 'nt':  # Windows
            # 使用用户文档目录下的.cursor-pro文件夹
            docs_path = get_user_documents_path()
            base_dir = os.path.join(docs_path, '.cursor-pro')
        else:  # macOS/Linux
            base_dir = os.path.join(os.path.expanduser('~'), '.cursor-pro')

        # 确保目录存在
        os.makedirs(base_dir, exist_ok=True)

        return os.path.join(base_dir, 'cursor_pro.db')

def init_database(db_path: Optional[str] = None) -> bool:
    """初始化数据库（别名）"""
    return init_sqlite_database(db_path)

def init_sqlite_database(db_path: Optional[str] = None) -> bool:
    """初始化SQLite数据库"""
    if db_path is None:
        db_path = get_sqlite_db_path()
    
    try:
        # 确保数据库目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # 连接数据库（如果不存在会自动创建）
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建基本表结构
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email TEXT UNIQUE NOT NULL,
                machine_id TEXT,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                key TEXT PRIMARY KEY,
                value TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                level TEXT,
                message TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 插入默认数据
        cursor.execute('''
            INSERT OR IGNORE INTO accounts (id, email, machine_id, status)
            VALUES (1, '<EMAIL>', 'local-device', 'active')
        ''')
        
        conn.commit()
        conn.close()
        
        print(f"✅ SQLite数据库初始化成功: {db_path}")
        return True
        
    except Exception as e:
        print(f"❌ SQLite数据库初始化失败: {e}")
        return False

def test_sqlite_connection(db_path: Optional[str] = None) -> bool:
    """测试SQLite数据库连接"""
    if db_path is None:
        db_path = get_sqlite_db_path()
    
    try:
        conn = sqlite3.connect(db_path, timeout=5)
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM accounts")
        count = cursor.fetchone()[0]
        conn.close()
        
        print(f"✅ SQLite数据库连接正常，账户数量: {count}")
        return True
        
    except Exception as e:
        print(f"❌ SQLite数据库连接失败: {e}")
        return False

def db_manager():
    """数据库管理器（兼容性函数）"""
    return {
        'get_db_path': get_sqlite_db_path,
        'init_database': init_database,
        'test_connection': test_sqlite_connection
    }

def get_sqlite_info() -> dict:
    """获取SQLite数据库信息"""
    db_path = get_sqlite_db_path()
    
    info = {
        'db_path': db_path,
        'exists': os.path.exists(db_path),
        'size': 0,
        'tables': []
    }
    
    if info['exists']:
        try:
            info['size'] = os.path.getsize(db_path)
            
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 获取表列表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            info['tables'] = [row[0] for row in cursor.fetchall()]
            
            conn.close()
            
        except Exception as e:
            print(f"获取数据库信息失败: {e}")
    
    return info

if __name__ == "__main__":
    print("🗄️ Cursor Pro - SQLite数据库配置")
    print("=" * 50)
    
    # 获取数据库信息
    info = get_sqlite_info()
    print(f"数据库路径: {info['db_path']}")
    print(f"数据库存在: {info['exists']}")
    
    if info['exists']:
        print(f"数据库大小: {info['size']} 字节")
        print(f"数据表: {', '.join(info['tables'])}")
        
        # 测试连接
        test_sqlite_connection()
    else:
        print("数据库不存在，正在初始化...")
        if init_sqlite_database():
            print("✅ 数据库初始化完成")
            test_sqlite_connection()
        else:
            print("❌ 数据库初始化失败")
