<template>
  <div class="content-section">
    <!-- 现代化设置页面 -->
    <div style="padding: 20px 0;">
      <div class="settings-title">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="12" cy="12" r="3"></circle>
          <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
        </svg>
        一键注册配置中心
      </div>

      <!-- 主要配置区域 - 卡片式布局 -->
      <div class="settings-container">
        <div class="modern-settings-grid">
          <!-- 邮箱配置组件 -->
          <EmailSection
            v-model:email="settings.email"
            v-model:tempmail-domain="settings.tempmailDomain"
            v-model:tempmail-epin="settings.tempmailEpin"
          />

          <!-- 个人信息组件 -->
          <PersonalSection
            v-model:first-name="settings.firstName"
            v-model:last-name="settings.lastName"
            v-model:password="settings.password"
          />

          <!-- 注册选项组件 -->
          <RegisterSection
            v-model:region="settings.region"
            v-model:register-mode="settings.registerMode"
          />

          <!-- 高级选项组件 -->
          <AdvancedSection
            v-model:show-browser="settings.showBrowser"
            v-model:timeout="settings.timeout"
            v-model:retry-count="settings.retryCount"
          />
        </div>

        <!-- 操作按钮区域 -->
        <div class="modern-action-grid">
        <AnimatedButton
          type="save"
          @click="handleSave"
          :loading="saving"
        >
          保存配置
        </AnimatedButton>

        <AnimatedButton
          type="load"
          @click="handleLoad"
          :loading="loading"
        >
          加载配置
        </AnimatedButton>

        <AnimatedButton
          type="reset"
          @click="handleReset"
        >
          重置默认
        </AnimatedButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { registerConfig } from '@/services/data-services'
// import { dataSyncService } from '@/services/data-sync' // 已禁用数据同步
import type { RegisterConfig } from '@/utils/data-manager'
import EmailSection from './EmailSection.vue'
import PersonalSection from './PersonalSection.vue'
import RegisterSection from './RegisterSection.vue'
import AdvancedSection from './AdvancedSection.vue'
import AnimatedButton from '../Common/AnimatedButtonNew.vue'

// 使用统一数据管理

// 默认设置
const defaultSettings: RegisterConfig = {
  email: '',
  tempmailDomain: '',
  tempmailEpin: '',
  firstName: '',
  lastName: '',
  password: '',
  region: 'US',
  registerMode: 'auto',
  showBrowser: false,
  timeout: 300,
  retryCount: 3
}

// 响应式数据
const settings = ref<RegisterConfig>({ ...defaultSettings })
const saving = ref(false)
const loading = ref(false)

// 保存配置
const handleSave = async () => {
  console.log('🔄 开始保存配置:', settings.value)

  saving.value = true
  ElMessage.info('正在保存配置...')

  try {
    const success = await registerConfig.saveRegisterConfig(settings.value)

    if (success) {
      // 数据同步已禁用，直接显示成功
      console.log('✅ 注册配置保存成功')
    }
  } catch (error) {
    console.error('❌ 保存配置失败:', error)
    ElMessage.error('保存配置失败，请稍后重试')
  } finally {
    saving.value = false
  }
}

// 加载配置
const handleLoad = async () => {
  console.log('🔄 开始加载配置')

  loading.value = true
  ElMessage.info('正在加载配置...')

  try {
    const result = await registerConfig.getRegisterConfig()
    console.log('📡 加载配置结果:', result)

    if (result) {
      Object.assign(settings.value, result)
      ElMessage.success('✅ 配置加载成功！')
      console.log('✅ 配置加载成功:', result)
    } else {
      ElMessage.warning('⚠️ 没有找到保存的配置，使用默认配置')
      console.log('⚠️ 使用默认配置')
    }
  } catch (error) {
    console.error('❌ 加载配置失败:', error)
    ElMessage.error('加载配置失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 重置配置
const handleReset = () => {
  console.log('🔄 开始重置配置')
  
  ElMessage.info('正在重置配置...')
  settings.value = { ...defaultSettings }
  
  ElMessage.success('✅ 配置已重置为默认值')
  console.log('✅ 配置重置完成')
}

// 页面加载时自动加载保存的配置
onMounted(async () => {
  console.log('🔄 Settings页面加载，自动加载配置...')

  try {
    const result = await registerConfig.getRegisterConfig()
    console.log('📡 自动加载配置结果:', result)

    if (result && typeof result === 'object') {
      Object.assign(settings.value, result)
      console.log('✅ 自动加载配置成功，已更新UI:', result)
    } else {
      console.log('⚠️ 没有找到保存的配置，使用默认值')
    }

    // 数据同步已禁用，无需队列同步
  } catch (error) {
    console.error('❌ 自动加载配置失败:', error)
    // 自动加载失败不显示错误提示，静默处理
  }
})
</script>

<style scoped>
/* Settings页面特定样式 */

.settings-title {
  color: #58a6ff;
  font-size: 1.12rem;
  margin-bottom: 32px;
  text-align: center;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  letter-spacing: 1px;
}

/* 配置中心背景容器 */
.settings-container {
  background: #23272e;
  border-radius: 12px;
  padding: 24px;
  border: 1.5px solid #2d323a;
  margin-bottom: 32px;
}

.modern-settings-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  margin-bottom: 24px;
}

.modern-action-grid {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modern-settings-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .modern-action-grid {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }

  .content-section {
    padding: 8px 16px 8px 16px;
  }
}

/* 确保组件在content-section内正确显示 */
.content-section > div {
  width: 100%;
}
</style>
