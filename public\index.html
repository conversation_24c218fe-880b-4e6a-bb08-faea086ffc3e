<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cursor Pro API</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        h1 { color: #fff; text-align: center; margin-bottom: 30px; }
        .status { background: #4CAF50; padding: 10px 20px; border-radius: 25px; display: inline-block; margin: 20px 0; }
        .endpoint { background: rgba(255, 255, 255, 0.2); padding: 15px; border-radius: 10px; margin: 10px 0; font-family: monospace; }
        .api-list { margin: 30px 0; }
        .api-item { margin: 15px 0; padding: 15px; background: rgba(255, 255, 255, 0.1); border-radius: 10px; }
        .method { background: #2196F3; color: white; padding: 5px 10px; border-radius: 5px; font-size: 12px; margin-right: 10px; }
        a { color: #FFD700; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Cursor Pro API</h1>
        
        <div class="status">
            ✅ API 运行正常
        </div>
        
        <h2>📋 可用端点</h2>
        <div class="api-list">
            <div class="api-item">
                <span class="method">GET</span>
                <strong>/api/check-version</strong>
                <p>绕过Cursor版本检查的主要API端点</p>
                <div class="endpoint">
                    <a href="/api/check-version" target="_blank">https://cursorpro-api.vercel.app/api/check-version</a>
                </div>
            </div>
            
            <div class="api-item">
                <span class="method">GET</span>
                <strong>/api/</strong>
                <p>通用API信息端点</p>
                <div class="endpoint">
                    <a href="/api/" target="_blank">https://cursorpro-api.vercel.app/api/</a>
                </div>
            </div>
        </div>
        
        <h2>🔧 使用说明</h2>
        <p>这个API用于绕过Cursor编辑器的版本检查。将您的Cursor配置指向这些端点即可。</p>
        
        <h2>📊 API响应示例</h2>
        <div class="endpoint">
{
  "hasUpdate": false,
  "version": "0.42.4",
  "status": "success",
  "message": "Version check bypassed successfully"
}
        </div>
        
        <p style="text-align: center; margin-top: 40px; opacity: 0.8;">
            <small>Powered by cursor-pro | Created by tul345</small>
        </p>
    </div>
</body>
</html>
