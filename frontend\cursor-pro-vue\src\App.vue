<script setup lang="ts">
import { onMounted, computed, ref } from 'vue'
import AppLayout from './components/Layout/AppLayout.vue'
// import WindowControls from './components/Common/WindowControls.vue' // 已在MainContent中使用
import ConfirmModal from './components/Modal/ConfirmModal.vue'
import CopyModal from './components/Modal/CopyModal.vue'
import InputModal from './components/Modal/InputModal.vue'

import ConfigModal from './components/Modal/ConfigModal.vue'
import InfoModal from './components/Modal/InfoModal.vue'
import OAuthModal from './components/OAuth/OAuthModal.vue'
import AdvancedModal from './components/Advanced/AdvancedModal.vue'
import LanguageModal from './components/Settings/LanguageModal.vue'
import NotificationContainer from './components/Common/NotificationContainer.vue'
import { useAppStore } from './stores/app'
import { useLogStore } from './stores/logs'
import { useAccountStore } from './stores/account'

const appStore = useAppStore()
const logStore = useLogStore()
const accountStore = useAccountStore()

// 检测是否在 Electron 环境中
const isElectron = ref(true) // 临时设为 true 来测试显示

// 计算属性
const confirmDialog = computed(() => appStore.confirmDialog)
const modals = computed(() => appStore.modals)

// 模态框状态
const copyModalVisible = ref(false)
const copyModalMessage = ref('')
const inputModalVisible = ref(false)
const inputModalConfig = ref({
  title: '',
  message: '',
  placeholder: '',
  defaultValue: '',
  onConfirm: null as ((value: string) => void) | null
})

const configModalVisible = ref(false)
const oauthModalVisible = ref(false)
const advancedModalVisible = ref(false)
const languageModalVisible = ref(false)

// 信息模态框状态
const infoModalVisible = ref(false)
const infoModalConfig = ref({
  title: '',
  data: {}
})

// 验证码模态框状态
const verificationCodeModalVisible = ref(false)
const verificationCodeModalData = ref({
  code: '',
  email: '',
  subject: '',
  time: ''
})

// 方法
const showCopyModal = (message: string) => {
  copyModalMessage.value = message
  copyModalVisible.value = true
}

const showInputModal = (config: any) => {
  inputModalConfig.value = config
  inputModalVisible.value = true
}



const showConfigModal = () => {
  configModalVisible.value = true
}

const showOAuthModal = () => {
  oauthModalVisible.value = true
}

const showAdvancedModal = () => {
  advancedModalVisible.value = true
}

const showLanguageModal = () => {
  languageModalVisible.value = true
}

const showInfoModal = (title: string, data: any) => {
  infoModalConfig.value = { title, data }
  infoModalVisible.value = true
}

const showVerificationCodeModal = (data: any) => {
  verificationCodeModalData.value = data
  verificationCodeModalVisible.value = true
}

const handleInputConfirm = (value: string) => {
  if (inputModalConfig.value.onConfirm) {
    inputModalConfig.value.onConfirm(value)
  }
  inputModalVisible.value = false
}

const handleInputCancel = () => {
  inputModalVisible.value = false
}



const handleOAuthSuccess = (data: any) => {
  // OAuth认证成功处理
  logStore.addSuccess(`OAuth认证成功: ${data.email}`, 'OAuth')
  accountStore.updateAccountInfo(data)
}

// 初始化应用
onMounted(async () => {
  appStore.init()
  logStore.init()
  await accountStore.init()

  // 强制切换到dashboard，避免路由问题
  setTimeout(() => {
    appStore.forceToDashboard()
  }, 100)

  // 全局方法注册（供其他组件调用）
  ;(window as any).showCopyModal = showCopyModal
  ;(window as any).showInputModal = showInputModal
  ;(window as any).showInfoModal = showInfoModal
  ;(window as any).showVerificationCodeModal = showVerificationCodeModal

  ;(window as any).showConfigModal = showConfigModal
  ;(window as any).showOAuthModal = showOAuthModal
  ;(window as any).showAdvancedModal = showAdvancedModal
  ;(window as any).showLanguageModal = showLanguageModal
})
</script>

<template>
  <!-- 窗口控制栏已移至MainContent中 -->

  <AppLayout />

  <!-- 全局确认对话框 -->
  <ConfirmModal
    :visible="confirmDialog.visible"
    :title="confirmDialog.title"
    :message="confirmDialog.message"
    @confirm="(selectedSteps) => appStore.confirmAction(selectedSteps)"
    @cancel="appStore.cancelAction"
  />

  <!-- 复制成功提示 -->
  <CopyModal
    v-model:visible="copyModalVisible"
    :message="copyModalMessage"
  />

  <!-- 输入对话框 -->
  <InputModal
    :visible="inputModalVisible"
    :title="inputModalConfig.title"
    :message="inputModalConfig.message"
    :placeholder="inputModalConfig.placeholder"
    :default-value="inputModalConfig.defaultValue"
    @confirm="handleInputConfirm"
    @cancel="handleInputCancel"
  />



  <!-- 配置显示对话框 -->
  <ConfigModal
    :visible="configModalVisible"
    @close="configModalVisible = false"
  />

  <!-- 信息显示对话框 -->
  <InfoModal
    :visible="infoModalVisible"
    :title="infoModalConfig.title"
    :data="infoModalConfig.data"
    @close="infoModalVisible = false"
  />

  <!-- OAuth认证对话框 -->
  <OAuthModal
    :visible="oauthModalVisible"
    @close="oauthModalVisible = false"
    @success="handleOAuthSuccess"
  />

  <!-- 高级功能对话框 -->
  <AdvancedModal
    :visible="advancedModalVisible"
    @close="advancedModalVisible = false"
  />

  <!-- 语言设置对话框 -->
  <LanguageModal
    :visible="languageModalVisible"
    @close="languageModalVisible = false"
  />

  <!-- 全局通知容器 -->
  <NotificationContainer />
</template>

<style>
/* 全局样式 */
body {
  margin: 0;
  padding: 0;
  background: #181c20;
  color: #e5e5e5;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  height: 100vh;
  overflow: hidden;
}

#app {
  height: 100vh;
}

/* 重置默认样式 */
* {
  box-sizing: border-box;
}

button {
  font-family: inherit;
}

input, select, textarea {
  font-family: inherit;
}
</style>
