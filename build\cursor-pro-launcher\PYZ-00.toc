('C:\\Users\\<USER>\\Desktop\\cursor-pro\\build\\cursor-pro-launcher\\PYZ-00.pyz',
 [('__future__', 'D:\\Python\\Lib\\__future__.py', 'PYMODULE'),
  ('_colorize', 'D:\\Python\\Lib\\_colorize.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\Python\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'D:\\Python\\Lib\\_compression.py', 'PYMODULE'),
  ('_opcode_metadata', 'D:\\Python\\Lib\\_opcode_metadata.py', 'PYMODULE'),
  ('_py_abc', 'D:\\Python\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\Python\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\Python\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'D:\\Python\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'D:\\Python\\Lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'D:\\Python\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\Python\\Lib\\ast.py', 'PYMODULE'),
  ('base64', 'D:\\Python\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'D:\\Python\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\Python\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\Python\\Lib\\calendar.py', 'PYMODULE'),
  ('contextlib', 'D:\\Python\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\Python\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\Python\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\Python\\Lib\\csv.py', 'PYMODULE'),
  ('dataclasses', 'D:\\Python\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\Python\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\Python\\Lib\\decimal.py', 'PYMODULE'),
  ('dis', 'D:\\Python\\Lib\\dis.py', 'PYMODULE'),
  ('email', 'D:\\Python\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\Python\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Python\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'D:\\Python\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase', 'D:\\Python\\Lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.base64mime', 'D:\\Python\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'D:\\Python\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\Python\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'D:\\Python\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'D:\\Python\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\Python\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'D:\\Python\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'D:\\Python\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\Python\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\Python\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'D:\\Python\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\Python\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\Python\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'D:\\Python\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'D:\\Python\\Lib\\email\\utils.py', 'PYMODULE'),
  ('fnmatch', 'D:\\Python\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\Python\\Lib\\fractions.py', 'PYMODULE'),
  ('getopt', 'D:\\Python\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'D:\\Python\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\Python\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\Python\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\Python\\Lib\\hashlib.py', 'PYMODULE'),
  ('importlib', 'D:\\Python\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\Python\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Python\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Python\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\Python\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\Python\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Python\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Python\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Python\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Python\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Python\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Python\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Python\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers', 'D:\\Python\\Lib\\importlib\\readers.py', 'PYMODULE'),
  ('importlib.resources',
   'D:\\Python\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Python\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Python\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'D:\\Python\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Python\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Python\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Python\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\Python\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'D:\\Python\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\Python\\Lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'D:\\Python\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'D:\\Python\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'D:\\Python\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\Python\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'D:\\Python\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'D:\\Python\\Lib\\lzma.py', 'PYMODULE'),
  ('numbers', 'D:\\Python\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\Python\\Lib\\opcode.py', 'PYMODULE'),
  ('pathlib', 'D:\\Python\\Lib\\pathlib\\__init__.py', 'PYMODULE'),
  ('pathlib._abc', 'D:\\Python\\Lib\\pathlib\\_abc.py', 'PYMODULE'),
  ('pathlib._local', 'D:\\Python\\Lib\\pathlib\\_local.py', 'PYMODULE'),
  ('pickle', 'D:\\Python\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\Python\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'D:\\Python\\Lib\\py_compile.py', 'PYMODULE'),
  ('quopri', 'D:\\Python\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\Python\\Lib\\random.py', 'PYMODULE'),
  ('selectors', 'D:\\Python\\Lib\\selectors.py', 'PYMODULE'),
  ('shutil', 'D:\\Python\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\Python\\Lib\\signal.py', 'PYMODULE'),
  ('socket', 'D:\\Python\\Lib\\socket.py', 'PYMODULE'),
  ('statistics', 'D:\\Python\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\Python\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\Python\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\Python\\Lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'D:\\Python\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\Python\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\Python\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\Python\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'D:\\Python\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\Python\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\Python\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('typing', 'D:\\Python\\Lib\\typing.py', 'PYMODULE'),
  ('urllib', 'D:\\Python\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\Python\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('zipfile', 'D:\\Python\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path', 'D:\\Python\\Lib\\zipfile\\_path\\__init__.py', 'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\Python\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE')])
