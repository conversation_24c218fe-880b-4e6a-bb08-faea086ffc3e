#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版本控制管理面板
用于管理版本更新策略
"""

import tkinter as tk
from tkinter import ttk, messagebox
import requests
import json
from datetime import datetime

class AdminPanel:
    """管理员控制面板"""
    
    def __init__(self):
        self.api_base = "https://your-domain.com/api/v1"  # 替换为你的API地址
        self.admin_token = "your-admin-token"  # 替换为你的管理员token
        
        self.root = tk.Tk()
        self.root.title("Cursor Pro - 版本控制面板")
        self.root.geometry("800x600")
        
        self.create_widgets()
        self.load_current_config()
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建笔记本控件
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 版本配置页面
        self.create_config_tab(notebook)
        
        # 统计数据页面
        self.create_stats_tab(notebook)
        
        # 快速操作页面
        self.create_actions_tab(notebook)
    
    def create_config_tab(self, notebook):
        """创建版本配置页面"""
        config_frame = ttk.Frame(notebook)
        notebook.add(config_frame, text="版本配置")
        
        # 当前版本配置
        ttk.Label(config_frame, text="最新版本:", font=("Arial", 10, "bold")).grid(row=0, column=0, sticky="w", padx=5, pady=5)
        self.latest_version_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.latest_version_var, width=20).grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(config_frame, text="最低支持版本:", font=("Arial", 10, "bold")).grid(row=1, column=0, sticky="w", padx=5, pady=5)
        self.min_version_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.min_version_var, width=20).grid(row=1, column=1, padx=5, pady=5)
        
        ttk.Label(config_frame, text="下载地址:", font=("Arial", 10, "bold")).grid(row=2, column=0, sticky="w", padx=5, pady=5)
        self.download_url_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.download_url_var, width=50).grid(row=2, column=1, columnspan=2, padx=5, pady=5)
        
        # 强制更新选项
        self.force_update_var = tk.BooleanVar()
        ttk.Checkbutton(config_frame, text="强制更新（所有旧版本必须更新）", variable=self.force_update_var).grid(row=3, column=0, columnspan=2, sticky="w", padx=5, pady=5)
        
        # 更新消息
        ttk.Label(config_frame, text="更新消息:", font=("Arial", 10, "bold")).grid(row=4, column=0, sticky="nw", padx=5, pady=5)
        self.update_message_text = tk.Text(config_frame, width=60, height=4)
        self.update_message_text.grid(row=4, column=1, columnspan=2, padx=5, pady=5)
        
        # 废弃版本列表
        ttk.Label(config_frame, text="废弃版本 (逗号分隔):", font=("Arial", 10, "bold")).grid(row=5, column=0, sticky="w", padx=5, pady=5)
        self.deprecated_versions_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.deprecated_versions_var, width=50).grid(row=5, column=1, columnspan=2, padx=5, pady=5)
        
        # 阻止版本列表
        ttk.Label(config_frame, text="阻止版本 (逗号分隔):", font=("Arial", 10, "bold")).grid(row=6, column=0, sticky="w", padx=5, pady=5)
        self.blocked_versions_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.blocked_versions_var, width=50).grid(row=6, column=1, columnspan=2, padx=5, pady=5)
        
        # 按钮
        button_frame = ttk.Frame(config_frame)
        button_frame.grid(row=7, column=0, columnspan=3, pady=20)
        
        ttk.Button(button_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="重新加载", command=self.load_current_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="测试连接", command=self.test_connection).pack(side=tk.LEFT, padx=5)
    
    def create_stats_tab(self, notebook):
        """创建统计数据页面"""
        stats_frame = ttk.Frame(notebook)
        notebook.add(stats_frame, text="使用统计")
        
        # 统计信息显示
        self.stats_text = tk.Text(stats_frame, width=80, height=30)
        scrollbar = ttk.Scrollbar(stats_frame, orient="vertical", command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=scrollbar.set)
        
        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 刷新按钮
        ttk.Button(stats_frame, text="刷新统计", command=self.load_stats).pack(pady=5)
    
    def create_actions_tab(self, notebook):
        """创建快速操作页面"""
        actions_frame = ttk.Frame(notebook)
        notebook.add(actions_frame, text="快速操作")
        
        # 快速操作按钮
        ttk.Label(actions_frame, text="快速操作", font=("Arial", 14, "bold")).pack(pady=10)
        
        # 发布新版本
        release_frame = ttk.LabelFrame(actions_frame, text="发布新版本", padding=10)
        release_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(release_frame, text="新版本号:").grid(row=0, column=0, sticky="w", padx=5)
        self.new_version_var = tk.StringVar()
        ttk.Entry(release_frame, textvariable=self.new_version_var, width=20).grid(row=0, column=1, padx=5)
        
        self.new_force_update_var = tk.BooleanVar()
        ttk.Checkbutton(release_frame, text="强制更新", variable=self.new_force_update_var).grid(row=0, column=2, padx=5)
        
        ttk.Button(release_frame, text="发布版本", command=self.release_new_version).grid(row=0, column=3, padx=5)
        
        # 紧急操作
        emergency_frame = ttk.LabelFrame(actions_frame, text="紧急操作", padding=10)
        emergency_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(emergency_frame, text="启用强制更新", command=self.enable_force_update).pack(side=tk.LEFT, padx=5)
        ttk.Button(emergency_frame, text="禁用强制更新", command=self.disable_force_update).pack(side=tk.LEFT, padx=5)
        ttk.Button(emergency_frame, text="阻止当前版本", command=self.block_current_version).pack(side=tk.LEFT, padx=5)
    
    def load_current_config(self):
        """加载当前配置"""
        try:
            headers = {'Authorization': f'Bearer {self.admin_token}'}
            response = requests.get(f"{self.api_base}/stats", headers=headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                config = data.get('current_config', {})
                
                self.latest_version_var.set(config.get('latest_version', ''))
                self.min_version_var.set(config.get('min_supported_version', ''))
                self.download_url_var.set(config.get('download_url', ''))
                self.force_update_var.set(config.get('force_update', False))
                
                self.update_message_text.delete(1.0, tk.END)
                self.update_message_text.insert(1.0, config.get('update_message', ''))
                
                self.deprecated_versions_var.set(', '.join(config.get('deprecated_versions', [])))
                self.blocked_versions_var.set(', '.join(config.get('blocked_versions', [])))
                
                messagebox.showinfo("成功", "配置加载成功")
            else:
                messagebox.showerror("错误", f"加载配置失败: {response.status_code}")
                
        except Exception as e:
            messagebox.showerror("错误", f"加载配置失败: {e}")
    
    def save_config(self):
        """保存配置"""
        try:
            config = {
                'latest_version': self.latest_version_var.get(),
                'min_supported_version': self.min_version_var.get(),
                'download_url': self.download_url_var.get(),
                'force_update': self.force_update_var.get(),
                'update_message': self.update_message_text.get(1.0, tk.END).strip(),
                'deprecated_versions': [v.strip() for v in self.deprecated_versions_var.get().split(',') if v.strip()],
                'blocked_versions': [v.strip() for v in self.blocked_versions_var.get().split(',') if v.strip()]
            }
            
            headers = {
                'Authorization': f'Bearer {self.admin_token}',
                'Content-Type': 'application/json'
            }
            
            response = requests.post(f"{self.api_base}/config", 
                                   headers=headers, 
                                   json=config, 
                                   timeout=10)
            
            if response.status_code == 200:
                messagebox.showinfo("成功", "配置保存成功")
            else:
                messagebox.showerror("错误", f"保存配置失败: {response.status_code}")
                
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def load_stats(self):
        """加载统计数据"""
        try:
            headers = {'Authorization': f'Bearer {self.admin_token}'}
            response = requests.get(f"{self.api_base}/stats", headers=headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                self.stats_text.delete(1.0, tk.END)
                
                # 显示统计信息
                stats_info = f"""=== Cursor Pro 使用统计 ===
更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

总用户数: {data.get('total_unique_users', 0)}

版本分布:
"""
                
                for version, stats in data.get('version_distribution', {}).items():
                    stats_info += f"  版本 {version}: {stats['unique_users']} 用户, {stats['total_requests']} 次请求\n"
                
                stats_info += "\n当前配置:\n"
                config = data.get('current_config', {})
                for key, value in config.items():
                    stats_info += f"  {key}: {value}\n"
                
                self.stats_text.insert(1.0, stats_info)
            else:
                messagebox.showerror("错误", f"加载统计失败: {response.status_code}")
                
        except Exception as e:
            messagebox.showerror("错误", f"加载统计失败: {e}")
    
    def test_connection(self):
        """测试连接"""
        try:
            response = requests.get(f"{self.api_base.replace('/api/v1', '')}/health", timeout=5)
            if response.status_code == 200:
                messagebox.showinfo("成功", "API连接正常")
            else:
                messagebox.showerror("错误", f"API连接失败: {response.status_code}")
        except Exception as e:
            messagebox.showerror("错误", f"连接测试失败: {e}")
    
    def release_new_version(self):
        """发布新版本"""
        new_version = self.new_version_var.get().strip()
        if not new_version:
            messagebox.showerror("错误", "请输入新版本号")
            return
        
        if messagebox.askyesno("确认", f"确定要发布版本 {new_version} 吗？"):
            self.latest_version_var.set(new_version)
            self.force_update_var.set(self.new_force_update_var.get())
            self.save_config()
    
    def enable_force_update(self):
        """启用强制更新"""
        if messagebox.askyesno("确认", "确定要启用强制更新吗？所有旧版本用户都必须更新！"):
            self.force_update_var.set(True)
            self.save_config()
    
    def disable_force_update(self):
        """禁用强制更新"""
        self.force_update_var.set(False)
        self.save_config()
    
    def block_current_version(self):
        """阻止当前版本"""
        version = tk.simpledialog.askstring("阻止版本", "请输入要阻止的版本号:")
        if version:
            current_blocked = self.blocked_versions_var.get()
            if current_blocked:
                new_blocked = f"{current_blocked}, {version}"
            else:
                new_blocked = version
            self.blocked_versions_var.set(new_blocked)
            self.save_config()
    
    def run(self):
        """运行管理面板"""
        self.root.mainloop()

if __name__ == '__main__':
    import tkinter.simpledialog
    
    panel = AdminPanel()
    panel.run()
