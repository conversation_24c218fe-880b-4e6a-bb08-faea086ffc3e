import os
from colorama import Fore, Style, init
import time
import random
# from faker import Faker  # 使用内置替代方案
from cursor_pro.auth.cursor_auth import CursorAuth
from cursor_pro.anti_detection.machine_reset import MachineIDResetter
from cursor_pro.auth.token_manager import get_token_from_cookie
from cursor_pro.management.config import get_config
from cursor_pro.management.account_manager import AccountManager

os.environ["PYTHONVERBOSE"] = "0"
os.environ["PYINSTALLER_VERBOSE"] = "0"

# Initialize colorama
init()

# 导入日志系统
try:
    import cursor_pro.anti_detection.machine_reset as reset_machine_manual
    def log_to_gui(msg):
        """将消息同时输出到控制台和GUI日志"""
        print(msg)
        # 移除颜色码后发送到GUI日志
        import re
        clean_msg = re.sub(r'\x1b\[[0-9;]*m', '', str(msg))
        reset_machine_manual.log(f"[注册] {clean_msg}")
except ImportError:
    def log_to_gui(msg):
        """如果无法导入日志系统，只输出到控制台"""
        print(msg)

# Define emoji constants
EMOJI = {
    'START': '🚀',
    'FORM': '📝',
    'VERIFY': '🔄',
    'PASSWORD': '🔑',
    'CODE': '📱',
    'DONE': '✨',
    'ERROR': '❌',
    'WAIT': '⏳',
    'SUCCESS': '✅',
    'MAIL': '📧',
    'KEY': '🔐',
    'UPDATE': '🔄',
    'INFO': 'ℹ️'
}

class CursorRegistration:
    def __init__(self, translator=None):
        self.translator = translator
        # Set to display mode
        os.environ['BROWSER_HEADLESS'] = 'False'
        self.browser = None
        self.controller = None
        self.sign_up_url = "https://authenticator.cursor.sh/sign-up"
        self.settings_url = "https://www.cursor.com/settings"
        self.email_address = None
        self.signup_tab = None
        self.email_tab = None

        # Add configuration attributes
        self.tempmail_domain = None
        self.tempmail_epin = None
        self.master_email = None  # Master email for receiving verification codes
        self.show_browser = True  # Default to show browser
        self.timeout = 300  # Default timeout
        self.retry_count = 3  # Default retry count

        # generate account information using built-in methods
        self.password = self._generate_password()
        self.first_name = self._generate_first_name()
        self.last_name = self._generate_last_name()

        # modify the first letter of the first name(keep the original function)
        new_first_letter = random.choice("ABCDEFGHIJKLMNOPQRSTUVWXYZ")
        self.first_name = new_first_letter + self.first_name[1:]

        log_to_gui(f"\n{Fore.CYAN}{EMOJI['PASSWORD']} {self.translator.get('register.password')}: {self.password} {Style.RESET_ALL}")
        log_to_gui(f"{Fore.CYAN}{EMOJI['FORM']} {self.translator.get('register.first_name')}: {self.first_name} {Style.RESET_ALL}")
        log_to_gui(f"{Fore.CYAN}{EMOJI['FORM']} {self.translator.get('register.last_name')}: {self.last_name} {Style.RESET_ALL}")

    def _generate_password(self, length=12):
        """Generate password"""
        import string
        chars = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(random.choice(chars) for _ in range(length))

    def _generate_first_name(self):
        """Generate first name"""
        first_names = ["John", "Jane", "Mike", "Sarah", "David", "Emma", "Chris", "Lisa", "Alex", "Maria"]
        return random.choice(first_names)

    def _generate_last_name(self):
        """Generate last name"""
        last_names = ["Smith", "Johnson", "Brown", "Davis", "Miller", "Wilson", "Moore", "Taylor", "Anderson", "Thomas"]
        return random.choice(last_names)

    def setup_email(self):
        """Setup Email"""
        try:
            # If email_address is already set (from GUI config), use it directly
            if self.email_address:
                log_to_gui(f"{Fore.CYAN}{EMOJI['MAIL']} {self.translator.get('register.email_address')}: {self.email_address}" + "\n" + f"{Style.RESET_ALL}")
                return True

            # Try to get a suggested email
            account_manager = AccountManager(self.translator)
            suggested_email = account_manager.suggest_email(self.first_name, self.last_name)

            if suggested_email:
                log_to_gui(f"{Fore.CYAN}{EMOJI['START']} {self.translator.get('register.suggest_email', suggested_email=suggested_email) if self.translator else f'Suggested email: {suggested_email}'}")
                log_to_gui(f"{Fore.CYAN}{EMOJI['START']} {self.translator.get('register.use_suggested_email_or_enter') if self.translator else 'Type \"yes\" to use this email or enter your own email:'}")
                user_input = input().strip()

                if user_input.lower() == 'yes' or user_input.lower() == 'y':
                    self.email_address = suggested_email
                else:
                    # User input is their own email address
                    self.email_address = user_input
            else:
                # If there's no suggested email
                log_to_gui(f"{Fore.CYAN}{EMOJI['START']} {self.translator.get('register.manual_email_input') if self.translator else 'Please enter your email address:'}")
                self.email_address = input().strip()

            # Validate if the email is valid
            if '@' not in self.email_address:
                log_to_gui(f"{Fore.RED}{EMOJI['ERROR']} {self.translator.get('register.invalid_email') if self.translator else 'Invalid email address'}{Style.RESET_ALL}")
                return False

            log_to_gui(f"{Fore.CYAN}{EMOJI['MAIL']} {self.translator.get('register.email_address')}: {self.email_address}" + "\n" + f"{Style.RESET_ALL}")
            return True

        except Exception as e:
            log_to_gui(f"{Fore.RED}{EMOJI['ERROR']} {self.translator.get('register.email_setup_failed', error=str(e))}{Style.RESET_ALL}")
            return False

    def get_verification_code(self):
        """Manually Get Verification Code"""
        try:
            log_to_gui(f"{Fore.CYAN}{EMOJI['CODE']} {self.translator.get('register.manual_code_input') if self.translator else 'Please enter the verification code:'}")
            code = input().strip()

            if not code.isdigit() or len(code) != 6:
                log_to_gui(f"{Fore.RED}{EMOJI['ERROR']} {self.translator.get('register.invalid_code') if self.translator else 'Invalid verification code'}{Style.RESET_ALL}")
                return None

            return code

        except Exception as e:
            log_to_gui(f"{Fore.RED}{EMOJI['ERROR']} {self.translator.get('register.code_input_failed', error=str(e))}{Style.RESET_ALL}")
            return None

    def register_cursor(self):
        """Register Cursor"""
        browser_tab = None
        try:
            log_to_gui(f"{Fore.CYAN}{EMOJI['START']} {self.translator.get('register.register_start')}...{Style.RESET_ALL}")

            # Check if tempmail_plus is enabled (GUI mode or config file)
            email_tab = None

            # First check if we have GUI config with tempmail settings
            if hasattr(self, 'tempmail_domain') and self.tempmail_domain and hasattr(self, 'tempmail_epin') and self.tempmail_epin:
                # GUI mode with tempmail configuration
                epin = self.tempmail_epin

                # For nnn223.xyz domain, emails are forwarded to the master email
                # So we <NAME_EMAIL> but check emails at the master email
                if self.tempmail_domain == 'nnn223.xyz':
                    try:
                        # Add email_tabs directory to Python path
                        import sys
                        import os
                        email_tabs_path = os.path.join(os.path.dirname(__file__), 'email_tabs')
                        if email_tabs_path not in sys.path:
                            sys.path.insert(0, email_tabs_path)
                        from tempmail_plus_tab import TempMailPlusTab
                        # Use the master email to receive verification codes
                        # even though we register with a random @nnn223.xyz address
                        receiving_email = self.master_email if self.master_email else "<EMAIL>"
                        email_tab = TempMailPlusTab(receiving_email, epin, self.translator)
                        log_to_gui(f"{Fore.CYAN}{EMOJI['MAIL']} 注册邮箱: {self.email_address}{Style.RESET_ALL}")
                        log_to_gui(f"{Fore.CYAN}{EMOJI['MAIL']} 验证码接收邮箱: {receiving_email}{Style.RESET_ALL}")
                        log_to_gui(f"{Fore.CYAN}{EMOJI['MAIL']} 使用临时邮箱自动获取验证码{Style.RESET_ALL}")
                    except Exception as e:
                        log_to_gui(f"{Fore.YELLOW}⚠️ 临时邮箱配置失败，将使用手动输入模式: {str(e)}{Style.RESET_ALL}")
                        email_tab = None
                else:
                    # For other domains, use the registration email directly
                    try:
                        # Add email_tabs directory to Python path
                        import sys
                        import os
                        email_tabs_path = os.path.join(os.path.dirname(__file__), 'email_tabs')
                        if email_tabs_path not in sys.path:
                            sys.path.insert(0, email_tabs_path)
                        from tempmail_plus_tab import TempMailPlusTab
                        email_tab = TempMailPlusTab(self.email_address, epin, self.translator)
                        log_to_gui(f"{Fore.CYAN}{EMOJI['MAIL']} 使用临时邮箱自动获取验证码: {self.email_address}{Style.RESET_ALL}")
                    except Exception as e:
                        log_to_gui(f"{Fore.YELLOW}⚠️ 临时邮箱配置失败，将使用手动输入模式: {str(e)}{Style.RESET_ALL}")
                        email_tab = None
            else:
                # Check config file for tempmail_plus settings
                config = get_config(self.translator)
                if config and config.has_section('TempMailPlus'):
                    if config.getboolean('TempMailPlus', 'enabled'):
                        email = config.get('TempMailPlus', 'email')
                        epin = config.get('TempMailPlus', 'epin')
                        if email and epin:
                            # Add email_tabs directory to Python path
                            import sys
                            import os
                            email_tabs_path = os.path.join(os.path.dirname(__file__), 'email_tabs')
                            if email_tabs_path not in sys.path:
                                sys.path.insert(0, email_tabs_path)
                            from tempmail_plus_tab import TempMailPlusTab
                            email_tab = TempMailPlusTab(email, epin, self.translator)
                            log_to_gui(f"{Fore.CYAN}{EMOJI['MAIL']} {self.translator.get('register.using_tempmail_plus')}{Style.RESET_ALL}")

            # Check if anti-detection is available
            try:
                from cursor_pro.anti_detection.integration import smart_fill_signup_form, should_continue_registration, ANTI_DETECTION_AVAILABLE
                if ANTI_DETECTION_AVAILABLE and should_continue_registration():
                    log_to_gui(f"{Fore.GREEN}🛡️ 使用防检测模式注册{Style.RESET_ALL}")
                    use_anti_detection = True
                else:
                    log_to_gui(f"{Fore.YELLOW}📝 使用原始注册模式{Style.RESET_ALL}")
                    use_anti_detection = False
            except ImportError:
                log_to_gui(f"{Fore.YELLOW}⚠️ 防检测模块不可用，使用原始模式{Style.RESET_ALL}")
                use_anti_detection = False

            # Use new_signup.py for registration
            from cursor_pro.auth.signup import main as new_signup_main

            # Execute new registration process, passing translator
            result, browser_tab = new_signup_main(
                email=self.email_address,
                password=self.password,
                first_name=self.first_name,
                last_name=self.last_name,
                email_tab=email_tab,  # Pass email_tab if tempmail_plus is enabled
                controller=self,  # Pass self instead of self.controller
                translator=self.translator
            )
            
            if result:
                # Use the returned browser instance to get account information
                self.signup_tab = browser_tab  # Save browser instance
                success = self._get_account_info()
                
                # Close browser after getting information
                if browser_tab:
                    try:
                        browser_tab.quit()
                    except:
                        pass
                
                return success
            
            return False

        except Exception as e:
            log_to_gui(f"{Fore.RED}{EMOJI['ERROR']} {self.translator.get('register.register_process_error', error=str(e))}{Style.RESET_ALL}")
            return False
        finally:
            # Ensure browser is closed in any case
            if browser_tab:
                try:
                    browser_tab.quit()
                except:
                    pass
                
    def _get_account_info(self):
        """Get Account Information and Token"""
        try:
            self.signup_tab.get(self.settings_url)
            time.sleep(2)
            
            usage_selector = (
                "css:div.col-span-2 > div > div > div > div > "
                "div:nth-child(1) > div.flex.items-center.justify-between.gap-2 > "
                "span.font-mono.text-sm\\/\\[0\\.875rem\\]"
            )
            usage_ele = self.signup_tab.ele(usage_selector)
            total_usage = "未知"
            if usage_ele:
                total_usage = usage_ele.text.split("/")[-1].strip()

            log_to_gui(f"Total Usage: {total_usage}\n")
            log_to_gui(f"{Fore.CYAN}{EMOJI['WAIT']} {self.translator.get('register.get_token')}...{Style.RESET_ALL}")
            max_attempts = 30
            retry_interval = 2
            attempts = 0

            while attempts < max_attempts:
                try:
                    cookies = self.signup_tab.cookies()
                    for cookie in cookies:
                        if cookie.get("name") == "WorkosCursorSessionToken":
                            # Skip refresh for new registration tokens
                            token = get_token_from_cookie(cookie["value"], self.translator, skip_refresh=True)
                            log_to_gui(f"{Fore.GREEN}{EMOJI['SUCCESS']} {self.translator.get('register.token_success')}{Style.RESET_ALL}")

                            # 保存账户信息并检查结果
                            save_success = self._save_account_info(token, total_usage)
                            if save_success:
                                log_to_gui(f"{Fore.GREEN}{EMOJI['SUCCESS']} {self.translator.get('register.account_info_saved')}{Style.RESET_ALL}")
                                return True
                            else:
                                log_to_gui(f"{Fore.RED}{EMOJI['ERROR']} {self.translator.get('register.save_account_info_failed')}{Style.RESET_ALL}")
                                # 即使保存失败，注册本身是成功的，但需要明确标识
                                log_to_gui(f"{Fore.YELLOW}{EMOJI['WARNING']} 注册成功但账户信息保存失败，请手动保存{Style.RESET_ALL}")
                                return True  # 注册成功，但保存失败

                    attempts += 1
                    if attempts < max_attempts:
                        log_to_gui(f"{Fore.YELLOW}{EMOJI['WAIT']} {self.translator.get('register.token_attempt', attempt=attempts, time=retry_interval)}{Style.RESET_ALL}")
                        time.sleep(retry_interval)
                    else:
                        log_to_gui(f"{Fore.RED}{EMOJI['ERROR']} {self.translator.get('register.token_max_attempts', max=max_attempts)}{Style.RESET_ALL}")

                except Exception as e:
                    log_to_gui(f"{Fore.RED}{EMOJI['ERROR']} {self.translator.get('register.token_failed', error=str(e))}{Style.RESET_ALL}")
                    attempts += 1
                    if attempts < max_attempts:
                        log_to_gui(f"{Fore.YELLOW}{EMOJI['WAIT']} {self.translator.get('register.token_attempt', attempt=attempts, time=retry_interval)}{Style.RESET_ALL}")
                        time.sleep(retry_interval)

            return False

        except Exception as e:
            log_to_gui(f"{Fore.RED}{EMOJI['ERROR']} {self.translator.get('register.account_error', error=str(e))}{Style.RESET_ALL}")
            return False

    def _save_account_info(self, token, total_usage):
        """Save Account Information to File"""
        try:
            # Update authentication information first
            log_to_gui(f"{Fore.CYAN}{EMOJI['KEY']} {self.translator.get('register.update_cursor_auth_info')}...{Style.RESET_ALL}")
            if self.update_cursor_auth(email=self.email_address, access_token=token, refresh_token=token, auth_type="Auth_0"):
                log_to_gui(f"{Fore.GREEN}{EMOJI['SUCCESS']} {self.translator.get('register.cursor_auth_info_updated')}...{Style.RESET_ALL}")

                # 验证数据一致性（修复登录状态不一致问题）
                if self._verify_auth_consistency():
                    log_to_gui(f"{Fore.GREEN}{EMOJI['SUCCESS']} 认证信息一致性验证通过{Style.RESET_ALL}")
                else:
                    log_to_gui(f"{Fore.YELLOW}{EMOJI['INFO']} 认证信息一致性验证失败，尝试清理缓存{Style.RESET_ALL}")
                    self._clear_auth_cache()
            else:
                log_to_gui(f"{Fore.RED}{EMOJI['ERROR']} {self.translator.get('register.cursor_auth_info_update_failed')}...{Style.RESET_ALL}")

            # Reset machine ID
            log_to_gui(f"{Fore.CYAN}{EMOJI['UPDATE']} {self.translator.get('register.reset_machine_id')}...{Style.RESET_ALL}")
            resetter = MachineIDResetter(self.translator)  # Create instance with translator
            if not resetter.reset_machine_ids():  # Call reset_machine_ids method directly
                raise Exception("Failed to reset machine ID")

            # Save account information to file using AccountManager
            account_manager = AccountManager(self.translator)
            if account_manager.save_account_info(self.email_address, self.password, token, total_usage):
                return True
            else:
                return False

        except Exception as e:
            log_to_gui(f"{Fore.RED}{EMOJI['ERROR']} {self.translator.get('register.save_account_info_failed', error=str(e))}{Style.RESET_ALL}")
            return False

    def start(self):
        """Start Registration Process"""
        try:
            if self.setup_email():
                if self.register_cursor():
                    log_to_gui(f"\n{Fore.GREEN}{EMOJI['DONE']} {self.translator.get('register.cursor_registration_completed')}...{Style.RESET_ALL}")
                    return True
            return False
        finally:
            # Close email tab
            if hasattr(self, 'temp_email'):
                try:
                    self.temp_email.close()
                except:
                    pass

    def update_cursor_auth(self, email=None, access_token=None, refresh_token=None, auth_type="Auth_0"):
        """Convenient function to update Cursor authentication information"""
        auth_manager = CursorAuth(translator=self.translator)
        return auth_manager.update_auth(email, access_token, refresh_token, auth_type)

    def _verify_auth_consistency(self):
        """验证认证信息一致性（修复登录状态不一致问题）"""
        try:
            import cursor_pro.core.account_info as cursor_acc_info

            # 获取当前存储的邮箱信息
            paths = cursor_acc_info.get_token_from_config()
            if not paths:
                return False

            # 从SQLite获取邮箱（应该是最新的）
            sqlite_email = cursor_acc_info.get_email_from_sqlite(paths['sqlite_path'])

            # 检查是否与注册的邮箱一致
            if sqlite_email == self.email_address:
                log_to_gui(f"{Fore.GREEN}{EMOJI['SUCCESS']} SQLite中的邮箱与注册邮箱一致: {sqlite_email}{Style.RESET_ALL}")
                return True
            else:
                log_to_gui(f"{Fore.YELLOW}{EMOJI['INFO']} SQLite中的邮箱({sqlite_email})与注册邮箱({self.email_address})不一致{Style.RESET_ALL}")
                return False

        except Exception as e:
            log_to_gui(f"{Fore.RED}{EMOJI['ERROR']} 验证认证一致性失败: {str(e)}{Style.RESET_ALL}")
            return False

    def _clear_auth_cache(self):
        """清理认证缓存（修复登录状态不一致问题）"""
        try:
            import cursor_pro.core.account_info as cursor_acc_info
            from cursor_pro.management.config import get_config
            import json
            import os

            # 获取配置路径
            paths = cursor_acc_info.get_token_from_config()
            if not paths:
                return False

            # 清理storage.json中的旧认证信息
            storage_path = paths['storage_path']
            if os.path.exists(storage_path):
                try:
                    with open(storage_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    # 清理可能的旧邮箱信息
                    keys_to_clear = ['cursorAuth/cachedEmail', 'cursorAuth/email']
                    for key in keys_to_clear:
                        if key in data:
                            del data[key]
                            log_to_gui(f"{Fore.CYAN}{EMOJI['UPDATE']} 已清理storage.json中的 {key}{Style.RESET_ALL}")

                    # 写回文件
                    with open(storage_path, 'w', encoding='utf-8') as f:
                        json.dump(data, f, indent=2)

                    log_to_gui(f"{Fore.GREEN}{EMOJI['SUCCESS']} 已清理storage.json缓存{Style.RESET_ALL}")

                except Exception as e:
                    log_to_gui(f"{Fore.YELLOW}{EMOJI['INFO']} 清理storage.json失败: {str(e)}{Style.RESET_ALL}")

            return True

        except Exception as e:
            log_to_gui(f"{Fore.RED}{EMOJI['ERROR']} 清理认证缓存失败: {str(e)}{Style.RESET_ALL}")
            return False

def main(translator=None, config=None):
    """Main function to be called from main.py or GUI"""
    log_to_gui(f"\n{Fore.CYAN}{'='*50}{Style.RESET_ALL}")
    log_to_gui(f"{Fore.CYAN}{EMOJI['START']} {translator.get('register.title')}{Style.RESET_ALL}")
    log_to_gui(f"{Fore.CYAN}{'='*50}{Style.RESET_ALL}")

    registration = CursorRegistration(translator)

    # If config is provided (from GUI), apply it to the registration instance
    if config:
        # Always generate a temporary email for registration (never use the master email for registration)
        if config.get('tempmailDomain'):
            # Generate a random email with the specified domain
            import string
            random_part = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
            registration.email_address = f"{random_part}@{config['tempmailDomain']}"
        else:
            # Use default temporary email generation
            registration.email_address = f"{registration.first_name.lower()}.{registration.last_name.lower()}.{random.randint(1000, 9999)}@nnn223.xyz"

        if config.get('firstName'):
            registration.first_name = config['firstName']
        if config.get('lastName'):
            registration.last_name = config['lastName']
        if config.get('password'):
            registration.password = config['password']
        if config.get('email'):
            # The master email for receiving verification codes
            registration.master_email = config['email']
        if config.get('tempmailDomain'):
            registration.tempmail_domain = config['tempmailDomain']
        if config.get('tempmailEpin'):
            registration.tempmail_epin = config['tempmailEpin']
        if config.get('showBrowser') is not None:
            registration.show_browser = config['showBrowser']
            # Set browser headless mode based on showBrowser setting
            os.environ['BROWSER_HEADLESS'] = 'False' if config['showBrowser'] else 'True'
        if config.get('timeout'):
            registration.timeout = config['timeout']
        if config.get('retryCount'):
            registration.retry_count = config['retryCount']

    result = registration.start()

    # If called from GUI, return the result directly
    if config:
        return result

    log_to_gui(f"\n{Fore.CYAN}{'='*50}{Style.RESET_ALL}")
    input(f"{EMOJI['INFO']} {translator.get('register.press_enter')}...")

if __name__ == "__main__":
    from cursor_pro.core.main import translator as main_translator
    main(main_translator)