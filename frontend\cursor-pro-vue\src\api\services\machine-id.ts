/**
 * 机器ID相关API服务
 * 处理机器ID的获取、重置、伪造、恢复等操作
 */

import { BaseAPI } from '../base'
import type {
  MachineIdInfo,
  MachineIdRequest,
  MachineIdResponse
} from '../types'

/**
 * 机器ID服务类
 * 继承BaseAPI，提供机器ID相关的API调用
 */
export class MachineIdService extends BaseAPI {
  /**
   * 获取当前机器ID
   */
  async getMachineId(): Promise<MachineIdResponse> {
    console.log('🔄 [MachineIdService] 获取机器ID')

    const response = await this.get<MachineIdResponse>('machine-id')
    console.log('📡 [MachineIdService] 获取机器ID响应:', response)
    
    return response.data
  }

  /**
   * 重置机器ID
   */
  async resetMachineId(options?: { force?: boolean; backup?: boolean }): Promise<MachineIdResponse> {
    console.log('🔄 [MachineIdService] 重置机器ID:', options)

    const request: MachineIdRequest = {
      action: 'reset',
      options
    }

    const response = await this.post<MachineIdResponse>('machine-id/reset', request)
    console.log('📡 [MachineIdService] 重置机器ID响应:', response)
    
    return response.data
  }

  /**
   * 生成机器ID
   */
  async generateMachineId(): Promise<MachineIdResponse> {
    console.log('🔄 [MachineIdService] 生成机器ID')

    const request: MachineIdRequest = {
      action: 'generate',
      options: { force: true }
    }

    const response = await this.post<MachineIdResponse>('machine-id/generate', request)
    console.log('📡 [MachineIdService] 生成机器ID响应:', response)

    return response.data
  }

  /**
   * 恢复机器ID
   */
  async restoreMachineId(backupId?: string): Promise<MachineIdResponse> {
    console.log('🔄 [MachineIdService] 恢复机器ID:', backupId)

    const response = await this.post<MachineIdResponse>('machine-id/restore', {
      backupId
    })
    console.log('📡 [MachineIdService] 恢复机器ID响应:', response)
    
    return response.data
  }

  /**
   * 伪造恢复机器ID
   */
  async fakeRestoreMachineId(): Promise<MachineIdResponse> {
    console.log('🔄 [MachineIdService] 伪造恢复机器ID')

    const response = await this.post<MachineIdResponse>('machine-id/fake-restore')
    console.log('📡 [MachineIdService] 伪造恢复机器ID响应:', response)
    
    return response.data
  }

  /**
   * 获取机器ID历史记录
   */
  async getMachineIdHistory(): Promise<{
    history: Array<{
      id: string
      machineId: string
      timestamp: string
      action: 'create' | 'reset' | 'fake' | 'restore'
      status: 'active' | 'inactive'
    }>
  }> {
    console.log('🔄 [MachineIdService] 获取机器ID历史')

    const response = await this.get<{
      history: Array<{
        id: string
        machineId: string
        timestamp: string
        action: 'create' | 'reset' | 'fake' | 'restore'
        status: 'active' | 'inactive'
      }>
    }>('machine-id/history')
    
    console.log('📡 [MachineIdService] 机器ID历史响应:', response)
    return response.data
  }

  /**
   * 验证机器ID
   */
  async validateMachineId(machineId: string): Promise<{
    valid: boolean
    reason?: string
    suggestions?: string[]
  }> {
    console.log('🔄 [MachineIdService] 验证机器ID:', machineId)

    const response = await this.post<{
      valid: boolean
      reason?: string
      suggestions?: string[]
    }>('machine-id/validate', { machineId })
    
    console.log('📡 [MachineIdService] 机器ID验证响应:', response)
    return response.data
  }

  /**
   * 生成新的机器ID
   */
  async generateMachineId(options?: {
    format?: 'uuid' | 'custom'
    prefix?: string
    length?: number
  }): Promise<MachineIdResponse> {
    console.log('🔄 [MachineIdService] 生成新机器ID:', options)

    const request: MachineIdRequest = {
      action: 'generate',
      options
    }

    const response = await this.post<MachineIdResponse>('machine-id/generate', request)
    console.log('📡 [MachineIdService] 生成机器ID响应:', response)
    
    return response.data
  }

  /**
   * 备份当前机器ID
   */
  async backupMachineId(name?: string): Promise<{
    success: boolean
    backupId: string
    message: string
  }> {
    console.log('🔄 [MachineIdService] 备份机器ID:', name)

    const response = await this.post<{
      success: boolean
      backupId: string
      message: string
    }>('machine-id/backup', { name })
    
    console.log('📡 [MachineIdService] 备份机器ID响应:', response)
    return response.data
  }

  /**
   * 获取机器ID备份列表
   */
  async getMachineIdBackups(): Promise<{
    backups: Array<{
      id: string
      name: string
      machineId: string
      createdAt: string
      size: number
    }>
  }> {
    console.log('🔄 [MachineIdService] 获取机器ID备份列表')

    const response = await this.get<{
      backups: Array<{
        id: string
        name: string
        machineId: string
        createdAt: string
        size: number
      }>
    }>('machine-id/backups')
    
    console.log('📡 [MachineIdService] 机器ID备份列表响应:', response)
    return response.data
  }

  /**
   * 删除机器ID备份
   */
  async deleteMachineIdBackup(backupId: string): Promise<{
    success: boolean
    message: string
  }> {
    console.log('🔄 [MachineIdService] 删除机器ID备份:', backupId)

    const response = await this.delete<{
      success: boolean
      message: string
    }>(`machine-id/backups/${backupId}`)
    
    console.log('📡 [MachineIdService] 删除备份响应:', response)
    return response.data
  }

  /**
   * 获取机器ID状态
   */
  async getMachineIdStatus(): Promise<{
    current: string
    status: 'original' | 'fake' | 'restored'
    lastModified: string
    backupAvailable: boolean
    validationStatus: 'valid' | 'invalid' | 'unknown'
  }> {
    console.log('🔄 [MachineIdService] 获取机器ID状态')

    const response = await this.get<{
      current: string
      status: 'original' | 'fake' | 'restored'
      lastModified: string
      backupAvailable: boolean
      validationStatus: 'valid' | 'invalid' | 'unknown'
    }>('machine-id/status')
    
    console.log('📡 [MachineIdService] 机器ID状态响应:', response)
    return response.data
  }
}

// 创建机器ID服务实例
export const machineIdService = new MachineIdService()

// 添加默认拦截器
machineIdService.addRequestInterceptor(async (config) => {
  // 为机器ID操作添加特殊标识
  config.headers = {
    ...config.headers,
    'X-Service': 'MachineIdService',
    'X-Sensitive-Operation': 'true'
  }
  return config
})

machineIdService.addResponseInterceptor(async (response) => {
  // 记录敏感操作
  console.log(`🔐 [MachineIdService] 敏感操作完成: ${Date.now()}`)
  return response
})

machineIdService.addErrorInterceptor(async (error) => {
  // 机器ID操作的错误需要特别处理
  console.error('❌ [MachineIdService] 机器ID操作错误:', {
    type: error.type,
    message: error.message,
    status: error.status
  })
  return error
})
