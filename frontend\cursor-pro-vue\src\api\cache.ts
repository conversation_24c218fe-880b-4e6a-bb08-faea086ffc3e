/**
 * API缓存系统
 * 提供请求缓存、去重、并发控制等优化功能
 */

import { ApiResponse } from './base'

// 缓存配置
export interface CacheConfig {
  ttl: number // 缓存时间（毫秒）
  maxSize: number // 最大缓存条目数
  strategy: 'lru' | 'fifo' | 'ttl' // 缓存策略
}

// 缓存条目
interface CacheEntry<T = any> {
  data: ApiResponse<T>
  timestamp: number
  ttl: number
  accessCount: number
  lastAccessed: number
}

// 请求去重管理器
class RequestDeduplicator {
  private pendingRequests = new Map<string, Promise<any>>()

  /**
   * 获取或创建请求
   */
  async getOrCreate<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
    // 如果已有相同请求在进行中，返回该请求
    if (this.pendingRequests.has(key)) {
      return this.pendingRequests.get(key)!
    }

    // 创建新请求
    const promise = requestFn().finally(() => {
      // 请求完成后清理
      this.pendingRequests.delete(key)
    })

    this.pendingRequests.set(key, promise)
    return promise
  }

  /**
   * 清理所有待处理请求
   */
  clear(): void {
    this.pendingRequests.clear()
  }

  /**
   * 获取待处理请求数量
   */
  getPendingCount(): number {
    return this.pendingRequests.size
  }
}

// 并发控制器
class ConcurrencyController {
  private runningRequests = 0
  private maxConcurrency: number
  private queue: Array<() => void> = []

  constructor(maxConcurrency: number = 10) {
    this.maxConcurrency = maxConcurrency
  }

  /**
   * 执行请求（带并发控制）
   */
  async execute<T>(requestFn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      const executeRequest = async () => {
        this.runningRequests++
        try {
          const result = await requestFn()
          resolve(result)
        } catch (error) {
          reject(error)
        } finally {
          this.runningRequests--
          this.processQueue()
        }
      }

      if (this.runningRequests < this.maxConcurrency) {
        executeRequest()
      } else {
        this.queue.push(executeRequest)
      }
    })
  }

  /**
   * 处理队列
   */
  private processQueue(): void {
    if (this.queue.length > 0 && this.runningRequests < this.maxConcurrency) {
      const nextRequest = this.queue.shift()!
      nextRequest()
    }
  }

  /**
   * 获取当前状态
   */
  getStatus(): {
    running: number
    queued: number
    maxConcurrency: number
  } {
    return {
      running: this.runningRequests,
      queued: this.queue.length,
      maxConcurrency: this.maxConcurrency
    }
  }

  /**
   * 设置最大并发数
   */
  setMaxConcurrency(max: number): void {
    this.maxConcurrency = max
    this.processQueue()
  }
}

/**
 * API缓存管理器
 */
export class ApiCache {
  private cache = new Map<string, CacheEntry>()
  private config: CacheConfig
  private deduplicator = new RequestDeduplicator()
  private concurrencyController = new ConcurrencyController()

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      ttl: 5 * 60 * 1000, // 默认5分钟
      maxSize: 100, // 默认100条
      strategy: 'lru', // 默认LRU策略
      ...config
    }

    // 定期清理过期缓存
    setInterval(() => this.cleanup(), 60 * 1000) // 每分钟清理一次
  }

  /**
   * 生成缓存键
   */
  private generateKey(url: string, params?: any): string {
    const paramStr = params ? JSON.stringify(params) : ''
    return `${url}:${paramStr}`
  }

  /**
   * 获取缓存
   */
  get<T>(url: string, params?: any): ApiResponse<T> | null {
    const key = this.generateKey(url, params)
    const entry = this.cache.get(key)

    if (!entry) {
      return null
    }

    // 检查是否过期
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      return null
    }

    // 更新访问信息
    entry.accessCount++
    entry.lastAccessed = Date.now()

    return entry.data
  }

  /**
   * 设置缓存
   */
  set<T>(url: string, params: any, data: ApiResponse<T>, ttl?: number): void {
    const key = this.generateKey(url, params)
    const now = Date.now()

    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      ttl: ttl || this.config.ttl,
      accessCount: 1,
      lastAccessed: now
    }

    // 检查缓存大小限制
    if (this.cache.size >= this.config.maxSize) {
      this.evict()
    }

    this.cache.set(key, entry)
  }

  /**
   * 删除缓存
   */
  delete(url: string, params?: any): boolean {
    const key = this.generateKey(url, params)
    return this.cache.delete(key)
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear()
  }

  /**
   * 缓存淘汰
   */
  private evict(): void {
    if (this.cache.size === 0) return

    let keyToEvict: string

    switch (this.config.strategy) {
      case 'lru':
        // 最近最少使用
        keyToEvict = this.findLRUKey()
        break
      case 'fifo':
        // 先进先出
        keyToEvict = this.cache.keys().next().value
        break
      case 'ttl':
        // 最早过期
        keyToEvict = this.findEarliestExpiryKey()
        break
      default:
        keyToEvict = this.cache.keys().next().value
    }

    this.cache.delete(keyToEvict)
  }

  /**
   * 查找LRU键
   */
  private findLRUKey(): string {
    let lruKey = ''
    let oldestAccess = Date.now()

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < oldestAccess) {
        oldestAccess = entry.lastAccessed
        lruKey = key
      }
    }

    return lruKey
  }

  /**
   * 查找最早过期的键
   */
  private findEarliestExpiryKey(): string {
    let earliestKey = ''
    let earliestExpiry = Date.now() + this.config.ttl

    for (const [key, entry] of this.cache.entries()) {
      const expiry = entry.timestamp + entry.ttl
      if (expiry < earliestExpiry) {
        earliestExpiry = expiry
        earliestKey = key
      }
    }

    return earliestKey
  }

  /**
   * 清理过期缓存
   */
  private cleanup(): void {
    const now = Date.now()
    const keysToDelete: string[] = []

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        keysToDelete.push(key)
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key))

    if (keysToDelete.length > 0) {
      console.log(`🧹 [ApiCache] 清理了 ${keysToDelete.length} 个过期缓存条目`)
    }
  }

  /**
   * 带缓存的请求执行
   */
  async executeWithCache<T>(
    url: string,
    params: any,
    requestFn: () => Promise<ApiResponse<T>>,
    options?: {
      ttl?: number
      skipCache?: boolean
      skipDeduplication?: boolean
    }
  ): Promise<ApiResponse<T>> {
    const { ttl, skipCache = false, skipDeduplication = false } = options || {}

    // 检查缓存
    if (!skipCache) {
      const cached = this.get<T>(url, params)
      if (cached) {
        console.log(`💾 [ApiCache] 缓存命中: ${url}`)
        return cached
      }
    }

    // 请求去重
    const requestKey = this.generateKey(url, params)
    const executeRequest = () => this.concurrencyController.execute(requestFn)

    const response = skipDeduplication
      ? await executeRequest()
      : await this.deduplicator.getOrCreate(requestKey, executeRequest)

    // 缓存响应
    if (!skipCache && response.success) {
      this.set(url, params, response, ttl)
    }

    return response
  }

  /**
   * 获取缓存统计
   */
  getStats(): {
    size: number
    maxSize: number
    hitRate: number
    concurrency: {
      running: number
      queued: number
      maxConcurrency: number
    }
  } {
    const totalAccess = Array.from(this.cache.values())
      .reduce((sum, entry) => sum + entry.accessCount, 0)
    
    const hitRate = totalAccess > 0 ? (this.cache.size / totalAccess) * 100 : 0

    return {
      size: this.cache.size,
      maxSize: this.config.maxSize,
      hitRate: Math.round(hitRate * 100) / 100,
      concurrency: this.concurrencyController.getStatus()
    }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<CacheConfig>): void {
    this.config = { ...this.config, ...newConfig }
    
    // 如果最大并发数改变，更新并发控制器
    if (newConfig.maxSize && newConfig.maxSize < this.cache.size) {
      // 如果新的最大大小更小，需要清理缓存
      while (this.cache.size > newConfig.maxSize) {
        this.evict()
      }
    }
  }

  /**
   * 预热缓存
   */
  async warmup<T>(
    requests: Array<{
      url: string
      params?: any
      requestFn: () => Promise<ApiResponse<T>>
      ttl?: number
    }>
  ): Promise<void> {
    console.log(`🔥 [ApiCache] 开始预热缓存，共 ${requests.length} 个请求`)

    const promises = requests.map(({ url, params, requestFn, ttl }) =>
      this.executeWithCache(url, params, requestFn, { ttl })
        .catch(error => {
          console.warn(`⚠️ [ApiCache] 预热失败: ${url}`, error)
          return null
        })
    )

    await Promise.all(promises)
    console.log(`✅ [ApiCache] 缓存预热完成`)
  }
}

// 创建全局缓存实例
export const apiCache = new ApiCache({
  ttl: 5 * 60 * 1000, // 5分钟
  maxSize: 200, // 200条缓存
  strategy: 'lru'
})

// 导出便捷函数
export const withCache = <T>(
  url: string,
  params: any,
  requestFn: () => Promise<ApiResponse<T>>,
  options?: Parameters<ApiCache['executeWithCache']>[3]
) => apiCache.executeWithCache(url, params, requestFn, options)
