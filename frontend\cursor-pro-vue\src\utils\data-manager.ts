/**
 * 统一数据管理器
 * 管理所有项目数据的存储、读取、验证、备份
 */

import { ElMessage } from 'element-plus'

// API基础配置
const API_BASE_URL = 'http://localhost:8080'

// 数据类型定义
export interface AccountData {
  email: string
  password: string
  token: string
  usageLimit: string
  machineIds: {
    devDeviceId: string
    macMachineId: string
    machineId: string
    sqmId: string
    serviceMachineId: string
  }
  createdAt: string
  lastUsed: string
}

export interface ConfigData {
  general: {
    browserPath: string
    driverPath: string
    showBrowser: boolean
    timeout: number
    retryCount: number
  }
  tempmail: {
    enabled: boolean
    email: string
    epin: string
  }
  oauth: {
    showSelectionAlert: boolean
    timeout: number
    maxAttempts: number
  }
  language: {
    current: string
    fallback: string
    autoUpdate: boolean
  }
}

export interface RegisterConfig {
  email: string
  tempmailDomain: string
  tempmailEpin: string
  firstName: string
  lastName: string
  password: string
  region: string
  registerMode: string
  showBrowser: boolean
  timeout: number
  retryCount: number
}

export interface BackupInfo {
  id: string
  type: 'settings' | 'session' | 'accounts' | 'full'
  name: string
  createdAt: string
  size: number
  checksum: string
  description?: string
}

// 数据存储位置枚举
export enum DataLocation {
  LOCAL_STORAGE = 'localStorage',
  API_SERVER = 'apiServer',
  FILE_SYSTEM = 'fileSystem',
  SQLITE_DB = 'sqliteDB'
}

/**
 * 统一数据管理器类
 */
export class DataManager {
  private static instance: DataManager
  private cache = new Map<string, any>()
  private cacheTimestamps = new Map<string, number>()
  private readonly CACHE_TTL = 5 * 60 * 1000 // 5分钟缓存

  private constructor() {}

  static getInstance(): DataManager {
    if (!DataManager.instance) {
      DataManager.instance = new DataManager()
    }
    return DataManager.instance
  }

  /**
   * 统一数据读取接口
   */
  async getData<T>(
    key: string, 
    location: DataLocation = DataLocation.API_SERVER,
    defaultValue?: T
  ): Promise<T | null> {
    try {
      // 检查缓存
      const cached = this.getFromCache<T>(key)
      if (cached !== null) {
        return cached
      }

      let data: T | null = null

      switch (location) {
        case DataLocation.LOCAL_STORAGE:
          data = this.getFromLocalStorage<T>(key, defaultValue)
          break
        case DataLocation.API_SERVER:
          data = await this.getFromAPI<T>(key)
          break
        case DataLocation.FILE_SYSTEM:
          data = await this.getFromFileSystem<T>(key)
          break
        case DataLocation.SQLITE_DB:
          data = await this.getFromSQLite<T>(key)
          break
      }

      // 缓存数据
      if (data !== null) {
        this.setToCache(key, data)
      }

      return data || defaultValue || null
    } catch (error) {
      console.error(`❌ 读取数据失败 [${key}]:`, error)
      return defaultValue || null
    }
  }

  /**
   * 统一数据写入接口
   */
  async setData<T>(
    key: string,
    value: T,
    location: DataLocation = DataLocation.API_SERVER,
    options?: {
      backup?: boolean
      validate?: boolean
      encrypt?: boolean
    }
  ): Promise<boolean> {
    try {
      const { backup = false, validate = true, encrypt = false } = options || {}

      // 数据验证
      if (validate && !this.validateData(key, value)) {
        throw new Error('数据验证失败')
      }

      // 创建备份
      if (backup) {
        await this.createBackup(key, location)
      }

      // 数据加密
      const dataToStore = encrypt ? this.encryptData(value) : value

      let success = false

      switch (location) {
        case DataLocation.LOCAL_STORAGE:
          success = this.setToLocalStorage(key, dataToStore)
          break
        case DataLocation.API_SERVER:
          success = await this.setToAPI(key, dataToStore)
          break
        case DataLocation.FILE_SYSTEM:
          success = await this.setToFileSystem(key, dataToStore)
          break
        case DataLocation.SQLITE_DB:
          success = await this.setToSQLite(key, dataToStore)
          break
      }

      // 更新缓存
      if (success) {
        this.setToCache(key, value)
      }

      return success
    } catch (error) {
      console.error(`❌ 写入数据失败 [${key}]:`, error)
      return false
    }
  }

  /**
   * 从localStorage读取
   */
  private getFromLocalStorage<T>(key: string, defaultValue?: T): T | null {
    try {
      const item = localStorage.getItem(key)
      if (item === null) return defaultValue || null
      return JSON.parse(item)
    } catch (error) {
      console.error(`❌ localStorage读取失败 [${key}]:`, error)
      return defaultValue || null
    }
  }

  /**
   * 写入localStorage
   */
  private setToLocalStorage<T>(key: string, value: T): boolean {
    try {
      localStorage.setItem(key, JSON.stringify(value))
      return true
    } catch (error) {
      console.error(`❌ localStorage写入失败 [${key}]:`, error)
      return false
    }
  }

  /**
   * 从API服务器读取
   */
  private async getFromAPI<T>(key: string): Promise<T | null> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/data/${key}`)
      if (!response.ok) throw new Error(`HTTP ${response.status}`)
      const result = await response.json()
      return result.data
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error)
      // 如果是网络连接错误，使用更温和的日志级别
      if (errorMsg.includes('Failed to fetch') || errorMsg.includes('fetch')) {
        console.log(`⚠️ API服务器不可用，跳过读取 [${key}]`)
      } else {
        console.error(`❌ API读取失败 [${key}]:`, error)
      }
      return null
    }
  }

  /**
   * 写入API服务器
   */
  private async setToAPI<T>(key: string, value: T): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/data/${key}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ data: value })
      })
      return response.ok
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error)
      // 如果是网络连接错误，使用更温和的日志级别
      if (errorMsg.includes('Failed to fetch') || errorMsg.includes('fetch')) {
        console.log(`⚠️ API服务器不可用，跳过写入 [${key}]`)
      } else {
        console.error(`❌ API写入失败 [${key}]:`, error)
      }
      return false
    }
  }

  /**
   * 从文件系统读取
   */
  private async getFromFileSystem<T>(key: string): Promise<T | null> {
    // 通过API调用后端文件操作
    return this.getFromAPI<T>(`file/${key}`)
  }

  /**
   * 写入文件系统
   */
  private async setToFileSystem<T>(key: string, value: T): Promise<boolean> {
    // 通过API调用后端文件操作
    return this.setToAPI(`file/${key}`, value)
  }

  /**
   * 从SQLite读取
   */
  private async getFromSQLite<T>(key: string): Promise<T | null> {
    // 通过API调用后端SQLite操作
    return this.getFromAPI<T>(`sqlite/${key}`)
  }

  /**
   * 写入SQLite
   */
  private async setToSQLite<T>(key: string, value: T): Promise<boolean> {
    // 通过API调用后端SQLite操作
    return this.setToAPI(`sqlite/${key}`, value)
  }

  /**
   * 缓存操作
   */
  private getFromCache<T>(key: string): T | null {
    const timestamp = this.cacheTimestamps.get(key)
    if (!timestamp || Date.now() - timestamp > this.CACHE_TTL) {
      this.cache.delete(key)
      this.cacheTimestamps.delete(key)
      return null
    }
    return this.cache.get(key) || null
  }

  private setToCache<T>(key: string, value: T): void {
    this.cache.set(key, value)
    this.cacheTimestamps.set(key, Date.now())
  }

  /**
   * 数据验证
   */
  private validateData(key: string, value: any): boolean {
    try {
      // 根据key类型进行不同的验证
      if (key.includes('email')) {
        return this.validateEmail(value)
      }
      if (key.includes('config')) {
        return this.validateConfig(value)
      }
      if (key.includes('account')) {
        return this.validateAccount(value)
      }
      return true
    } catch (error) {
      console.error(`❌ 数据验证失败 [${key}]:`, error)
      return false
    }
  }

  private validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  private validateConfig(config: any): boolean {
    // 配置数据验证逻辑
    return typeof config === 'object' && config !== null
  }

  private validateAccount(account: any): boolean {
    // 账户数据验证逻辑
    return account && account.email && account.password
  }

  /**
   * 数据加密
   */
  private encryptData<T>(data: T): string {
    // 简单的Base64编码（实际项目中应使用更强的加密）
    return btoa(JSON.stringify(data))
  }

  /**
   * 数据解密
   */
  private decryptData<T>(encryptedData: string): T {
    return JSON.parse(atob(encryptedData))
  }

  /**
   * 创建备份
   */
  private async createBackup(key: string, location: DataLocation): Promise<string> {
    try {
      const currentData = await this.getData(key, location)
      const backupId = `backup_${key}_${Date.now()}`
      
      const backupInfo: BackupInfo = {
        id: backupId,
        type: this.getBackupType(key),
        name: `${key} 备份`,
        createdAt: new Date().toISOString(),
        size: JSON.stringify(currentData).length,
        checksum: this.calculateChecksum(currentData),
        description: `自动备份 - ${key}`
      }

      await this.setData(`backup/${backupId}`, {
        info: backupInfo,
        data: currentData
      }, DataLocation.FILE_SYSTEM)

      return backupId
    } catch (error) {
      console.error(`❌ 创建备份失败 [${key}]:`, error)
      return ''
    }
  }

  private getBackupType(key: string): BackupInfo['type'] {
    if (key.includes('account')) return 'accounts'
    if (key.includes('config')) return 'settings'
    if (key.includes('session')) return 'session'
    return 'settings'
  }

  private calculateChecksum(data: any): string {
    // 简单的校验和计算
    const str = JSON.stringify(data)
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return hash.toString(16)
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    this.cache.clear()
    this.cacheTimestamps.clear()
    console.log('🧹 缓存已清理')
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): {
    size: number
    keys: string[]
    totalMemory: number
  } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
      totalMemory: JSON.stringify(Array.from(this.cache.entries())).length
    }
  }
}

// 导出单例实例
export const dataManager = DataManager.getInstance()

// 便捷函数
export const getData = <T>(key: string, location?: DataLocation, defaultValue?: T) =>
  dataManager.getData<T>(key, location, defaultValue)

export const setData = <T>(key: string, value: T, location?: DataLocation, options?: any) =>
  dataManager.setData<T>(key, value, location, options)
