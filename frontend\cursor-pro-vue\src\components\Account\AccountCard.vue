<template>
  <div class="account-item" :data-index="account.originalIndex" :data-account-index="index">
    <!-- 账户头部信息 -->
    <div class="account-header">
      <div class="account-number">#{{ index + 1 }}</div>
      
      <!-- 账户信息组 -->
      <div class="account-info-group">
        <div class="account-info-title">✉️ 账户信息</div>
        <div class="account-info-content account-email">{{ emailDisplay }}</div>
        <div class="account-info-content register-time">
          注册时间: {{ account.registeredAt || '未知' }}
        </div>
        <button 
          class="copy-btn" 
          @click="copyText(account.email || '', $event)"
          title="复制邮箱"
        >
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
          </svg>
          复制
        </button>
      </div>

      <!-- 密码信息组 -->
      <div class="account-info-group">
        <div class="account-info-title">🔐 登录密码</div>
        <div class="account-info-content account-password">{{ account.password || '未知密码' }}</div>
        <div class="account-status" :class="{ active: account.isActive, inactive: !account.isActive }">
          {{ account.isActive ? 'Active' : 'Inactive' }}
        </div>
        <button 
          class="copy-btn" 
          @click="copyText(account.password || '', $event)"
          title="复制密码"
        >
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
          </svg>
          复制
        </button>
      </div>

      <!-- 用户信息组 -->
      <div class="account-info-group">
        <div class="account-info-title">👨‍💻 用户信息</div>
        <div class="account-info-content">{{ userDisplayName }}</div>
        <div class="account-info-content trial-info">
          试用剩余: {{ account.trialDays || '14' }} 天
        </div>
      </div>
    </div>

    <!-- 账户详情 -->
    <div class="account-details">
      <div class="account-detail-item">
        <div class="account-detail-label">🌍 验证邮箱</div>
        <div class="account-detail-value">{{ account.masterEmail || account.email || '未知' }}</div>
      </div>
      <div class="account-detail-item">
        <div class="account-detail-label">🔑 Token状态</div>
        <div class="account-detail-value">{{ getTokenStatus(account.token) }}</div>
      </div>
      <div class="account-detail-item">
        <div class="account-detail-label">📅 注册日期</div>
        <div class="account-detail-value">{{ formatDate(account.registeredAt) }}</div>
      </div>
      <div class="account-detail-item">
        <div class="account-detail-label">🎯 账户状态</div>
        <div class="account-detail-value" :class="statusClass">{{ statusText }}</div>
      </div>
    </div>

    <!-- 账户使用情况 - Kawaii滑块 -->
    <div class="account-usage">
      <div class="usage-title">📊 使用情况</div>
      <div class="kawaii-sliders">
        <!-- 剩余天数 -->
        <div class="slider-item">
          <div class="slider-label">
            <span class="slider-icon">🔴</span>
            <span class="slider-text">剩余天数</span>
            <span class="slider-value">{{ trialDaysValue }}</span>
          </div>
          <input
            type="range"
            class="kawaii"
            :value="trialDaysValue"
            max="30"
            min="0"
            readonly
            style="--base: #fe8ce4;"
          />
        </div>

        <!-- 高级用量 -->
        <div class="slider-item">
          <div class="slider-label">
            <span class="slider-icon">🔵</span>
            <span class="slider-text">高级用量</span>
            <span class="slider-value">{{ premiumUsageDisplay }}</span>
          </div>
          <input
            type="range"
            class="kawaii"
            :value="premiumUsagePercent"
            max="100"
            min="0"
            readonly
            style="--base: #8cc8e4;"
          />
        </div>

        <!-- 基础功能 -->
        <div class="slider-item">
          <div class="slider-label">
            <span class="slider-icon">🟢</span>
            <span class="slider-text">基础功能</span>
            <span class="slider-value">{{ basicUsageDisplay }}</span>
          </div>
          <input
            type="range"
            class="kawaii"
            :value="basicUsageValue"
            max="100"
            min="0"
            readonly
            style="--base: #6cc484;"
          />
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="account-actions">
      <button class="action-button copy-button" @click="handleCopyAccount">
        <svg viewBox="0 0 448 512" class="svgIcon">
          <path d="M208 0H332.1c12.7 0 24.9 5.1 33.9 14.1l67.9 67.9c9 9 14.1 21.2 14.1 33.9V336c0 26.5-21.5 48-48 48H208c-26.5 0-48-21.5-48-48V48c0-26.5 21.5-48 48-48zM48 128h80v64H64V448H256V416h64v48c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V176c0-26.5 21.5-48 48-48z"></path>
        </svg>
      </button>

      <button
        v-if="!account.isActive"
        class="action-button switch-button"
        @click="handleSwitchAccount"
      >
        <svg viewBox="0 0 448 512" class="svgIcon">
          <path d="M438.6 278.6c12.5-12.5 12.5-32.8 0-45.3l-160-160c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L338.8 224 32 224c-17.7 0-32 14.3-32 32s14.3 32 32 32l306.7 0L233.4 393.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l160-160z"></path>
        </svg>
      </button>

      <button class="action-button delete-button" @click="handleDeleteAccount">
        <svg viewBox="0 0 448 512" class="svgIcon">
          <path d="M135.2 17.7L128 32H32C14.3 32 0 46.3 0 64S14.3 96 32 96H416c17.7 0 32-14.3 32-32s-14.3-32-32-32H320l-7.2-14.3C307.4 6.8 296.3 0 284.2 0H163.8c-12.1 0-23.2 6.8-28.6 17.7zM416 128H32L53.2 467c1.6 25.3 22.6 45 47.9 45H346.9c25.3 0 46.3-19.7 47.9-45L416 128z"></path>
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface RegisteredAccount {
  email: string
  password: string
  firstName?: string
  lastName?: string
  registeredAt?: string
  isActive?: boolean
  trialDays?: number
  masterEmail?: string
  token?: string
  originalIndex: number
}

interface AccountCardProps {
  account: RegisteredAccount
  index: number
}

interface AccountCardEvents {
  'select': [account: RegisteredAccount]
  'copy': [account: RegisteredAccount]
  'delete': [account: RegisteredAccount]
}

const props = defineProps<AccountCardProps>()
const emit = defineEmits<AccountCardEvents>()

// 计算属性
const emailDisplay = computed(() => {
  const email = props.account.email || ''
  if (email.includes('@')) {
    const [username, domain] = email.split('@')
    return `${username}@<span class="email-domain">${domain}</span>`
  }
  return email
})

const userDisplayName = computed(() => {
  const firstName = props.account.firstName || ''
  const lastName = props.account.lastName || ''
  return `${firstName} ${lastName}`.trim() || '未知用户'
})

const statusClass = computed(() => {
  return props.account.isActive ? 'status-active' : 'status-inactive'
})

const statusText = computed(() => {
  return props.account.isActive ? '当前账户' : '备用账户'
})

// 滑块相关计算属性
const trialDaysValue = computed(() => {
  const days = props.account.trialDays || 0
  return Math.min(Math.max(days, 0), 30) // 限制在0-30天范围内
})

const premiumUsagePercent = computed(() => {
  // 模拟高级用量百分比，实际应该从API获取
  const used = Math.floor(Math.random() * 500) // 模拟已使用量
  const total = 999
  return Math.floor((used / total) * 100)
})

const premiumUsageDisplay = computed(() => {
  const used = Math.floor((premiumUsagePercent.value / 100) * 999)
  return `${used}/999`
})

const basicUsageValue = computed(() => {
  // 基础功能通常是无限制的，这里显示为满值
  return props.account.isActive ? 100 : 60
})

const basicUsageDisplay = computed(() => {
  return props.account.isActive ? '∞' : '有限'
})

// 方法
const copyText = async (text: string, event: Event) => {
  try {
    await navigator.clipboard.writeText(text)
    const btn = event.target as HTMLElement
    const originalText = btn.textContent
    btn.textContent = '✅ 已复制'
    btn.classList.add('copied')
    
    setTimeout(() => {
      btn.textContent = originalText
      btn.classList.remove('copied')
    }, 2000)
  } catch (error) {
    console.error('复制失败:', error)
  }
}

const formatDate = (dateStr?: string) => {
  if (!dateStr) return '未知'
  try {
    return new Date(dateStr).toLocaleDateString('zh-CN')
  } catch {
    return dateStr
  }
}

const handleCopyAccount = () => {
  emit('copy', props.account)
}

const handleSwitchAccount = () => {
  emit('select', props.account)
}

const handleDeleteAccount = () => {
  emit('delete', props.account)
}

// 获取Token真实状态 - 不再欺骗用户
const getTokenStatus = (token: string | undefined) => {
  if (!token) {
    return '未配置'
  }

  // 检查是否是无效的占位符Token
  const invalidTokens = ['已设置', '未能获取到有效Token', '需要手动获取Token', '需要手动获取密码']
  if (invalidTokens.includes(token)) {
    return '无效Token'
  }

  // 检查是否是真实的JWT Token
  if (token.startsWith('eyJ') && token.includes('.')) {
    return '已配置'
  }

  // 其他情况视为可疑
  return '状态未知'
}
</script>

<style scoped>
/* 账户项样式 - 保持与原版完全一致 */
.account-item {
  background: #23272e;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  border: 1px solid #2d323a;
  transition: all 0.3s ease;
  position: relative;
}

.account-item:hover {
  border-color: #3d424a;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.account-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #2d323a;
  position: relative;
}

.account-number {
  position: absolute;
  top: -12px;
  right: 0;
  background: #6b7280;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 700;
  font-family: 'Fira Mono', 'Consolas', monospace;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.account-info-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 10px 12px;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.2s ease;
  position: relative;
}

.account-info-group:hover {
  background: rgba(255, 255, 255, 0.04);
  border-color: rgba(255, 255, 255, 0.1);
}

.account-info-group:hover .copy-btn {
  opacity: 1;
}

.account-info-title {
  color: #94a3b8;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
  font-family: 'Fira Mono', 'Consolas', monospace;
}

.account-info-content {
  color: #e5e5e5;
  font-family: 'Fira Mono', 'Consolas', monospace;
  font-size: 0.95rem;
  font-weight: 600;
  word-break: break-all;
}

.account-email {
  color: #facc15 !important;
  font-weight: 700;
}

.email-domain {
  color: #6b7280 !important;
  font-weight: 800;
}

.account-password {
  color: #b5f4a5 !important;
  font-weight: 700;
}

.register-time,
.trial-info {
  font-size: 0.85rem !important;
  color: #94a3b8 !important;
}

.account-status {
  font-size: 0.8rem;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 4px;
  text-align: center;
}

.account-status.active {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.account-status.inactive {
  background: rgba(107, 114, 128, 0.2);
  color: #6b7280;
}

.copy-btn {
  position: absolute;
  top: 6px;
  right: 6px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  padding: 3px 5px;
  color: #94a3b8;
  font-size: 0.65rem;
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 3px;
  font-family: 'Fira Mono', 'Consolas', monospace;
}

.copy-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  color: #e5e5e5;
  transform: translateY(-1px);
}

.copy-btn svg {
  width: 10px;
  height: 10px;
}

.copy-btn.copied {
  background: rgba(16, 185, 129, 0.2);
  border-color: rgba(16, 185, 129, 0.4);
  color: #10b981;
}

.account-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px 0;
  border-top: 1px solid #2d323a;
}

.account-detail-item {
  background: rgba(255, 255, 255, 0.02);
  padding: 10px 12px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.2s ease;
}

.account-detail-item:hover {
  background: rgba(255, 255, 255, 0.04);
  border-color: rgba(255, 255, 255, 0.1);
}

.account-detail-label {
  color: #94a3b8;
  font-size: 0.7rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
  font-family: 'Fira Mono', 'Consolas', monospace;
}

.account-detail-value {
  color: #e5e5e5;
  font-family: 'Fira Mono', 'Consolas', monospace;
  font-size: 0.85rem;
  font-weight: 600;
  word-break: break-all;
}

.status-active {
  color: #10b981 !important;
}

.status-inactive {
  color: #6b7280 !important;
}

.account-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #2d323a;
}

/* 新的动画按钮样式 */
.action-button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgb(20, 20, 20);
  border: none;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.164);
  cursor: pointer;
  transition-duration: .3s;
  overflow: hidden;
  position: relative;
}

.svgIcon {
  width: 12px;
  transition-duration: .3s;
}

.svgIcon path {
  fill: white;
}

.svgIcon polyline,
.svgIcon rect,
.svgIcon line {
  stroke: white;
  fill: none;
}

/* 复制按钮 */
.copy-button:hover {
  width: 140px;
  border-radius: 50px;
  transition-duration: .3s;
  background-color: rgb(34, 197, 94);
  align-items: center;
}

.copy-button:hover .svgIcon {
  width: 50px;
  transition-duration: .3s;
  transform: translateY(60%);
}

.copy-button::before {
  position: absolute;
  top: -20px;
  left: 50%;
  content: "Copy";
  color: white;
  transition-duration: .3s;
  font-size: 2px;
  transform: translateX(-50%);
}

.copy-button:hover::before {
  font-size: 13px;
  opacity: 1;
  transform: translateX(-50%) translateY(30px);
  transition-duration: .3s;
}

/* 切换按钮 */
.switch-button:hover {
  width: 140px;
  border-radius: 50px;
  transition-duration: .3s;
  background-color: rgb(59, 130, 246);
  align-items: center;
}

.switch-button:hover .svgIcon {
  width: 50px;
  transition-duration: .3s;
  transform: translateY(60%);
}

.switch-button::before {
  position: absolute;
  top: -20px;
  left: 50%;
  content: "Switch";
  color: white;
  transition-duration: .3s;
  font-size: 2px;
  transform: translateX(-50%);
}

.switch-button:hover::before {
  font-size: 13px;
  opacity: 1;
  transform: translateX(-50%) translateY(30px);
  transition-duration: .3s;
}

/* 删除按钮 */
.delete-button:hover {
  width: 140px;
  border-radius: 50px;
  transition-duration: .3s;
  background-color: rgb(255, 69, 69);
  align-items: center;
}

.delete-button:hover .svgIcon {
  width: 50px;
  transition-duration: .3s;
  transform: translateY(60%);
}

.delete-button::before {
  position: absolute;
  top: -20px;
  left: 50%;
  content: "Delete";
  color: white;
  transition-duration: .3s;
  font-size: 2px;
  transform: translateX(-50%);
}

.delete-button:hover::before {
  font-size: 13px;
  opacity: 1;
  transform: translateX(-50%) translateY(30px);
  transition-duration: .3s;
}

/* 账户使用情况样式 */
.account-usage {
  margin-top: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  border: 1px solid #2d323a;
}

.usage-title {
  color: #e5e7eb;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 16px;
  text-align: center;
}

.kawaii-sliders {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.slider-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.slider-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #e5e7eb;
  font-size: 0.8rem;
}

.slider-icon {
  font-size: 0.9rem;
}

.slider-text {
  flex: 1;
  margin-left: 8px;
}

.slider-value {
  font-weight: 600;
  color: #10b981;
}

/* Kawaii滑块样式 */
[type="range"].kawaii {
  --base: #fe8ce4;
  --light: color-mix(in sRGB, var(--base) 60%, #fff);
  --lighter: color-mix(in sRGB, var(--base) 30%, #fff);
  --dark: color-mix(in sRGB, var(--base) 95%, #000);
  --transparent: color-mix(in sRGB, var(--base) 0%, #0000);
  appearance: none;
  font-size: 1em;
  width: 100%;
  height: 2em;
  border: 0.5em solid #fff;
  border-radius: 2em;
  box-shadow:
    0 0 1em #0001,
    0 0.25em 0.5em #0001;
  overflow: hidden;
  cursor: default;
}

[type="range"].kawaii::-webkit-slider-runnable-track {
  background:
    radial-gradient(
      circle at 0.75em 0.6em,
      var(--light) calc(0.2em - 1px),
      #0000 0.2em
    ),
    radial-gradient(
      circle at 1.25em 0.6em,
      var(--light) calc(0.2em - 1px),
      #0000 0.2em
    ),
    radial-gradient(
      circle at 5em 0.6em,
      var(--light) calc(0.2em - 1px),
      #0000 0.2em
    ),
    linear-gradient(var(--light) 0 0) 1.25em 0.4em / 3.75em calc(0.4em - 0.5px)
      no-repeat,
    linear-gradient(90deg, var(--base), var(--transparent) 1em),
    linear-gradient(#0000 70%, var(--dark) 80%),
    var(--base);
  border-radius: 2em;
  height: 100%;
  overflow: hidden;
}

[type="range"].kawaii::-webkit-slider-thumb {
  appearance: none;
  height: 2em;
  width: 2em;
  color: var(--lighter);
  background:
    radial-gradient(
      circle at 0.75em 0.6em,
      var(--light) calc(0.2em - 1px),
      #0000 0.2em
    ),
    linear-gradient(90deg, #0000 0.75em, var(--base) 0) 0 0 / 100% 50% no-repeat;
  border-radius: 50%;
  box-shadow:
    inset -0.5em 0 0.5em -0.25em var(--base),
    1em 0 0 0.25em,
    2em 0 0 0.25em,
    3em 0 0 0.25em,
    4em 0 0 0.25em,
    5em 0 0 0.25em,
    6em 0 0 0.25em,
    7em 0 0 0.25em,
    8em 0 0 0.25em,
    9em 0 0 0.25em,
    10em 0 0 0.25em,
    11em 0 0 0.25em,
    12em 0 0 0.25em,
    12em 0 0 0.25em,
    13em 0 0 0.25em,
    14em 0 0 0.25em,
    15em 0 0 0.25em,
    16em 0 0 0.25em,
    17em 0 0 0.25em,
    18em 0 0 0.25em,
    19em 0 0 0.25em;
}

[type="range"].kawaii::-moz-range-track {
  background:
    radial-gradient(
      circle at 0.75em 30%,
      var(--light) calc(0.2em - 1px),
      #0000 0.2em
    ),
    radial-gradient(
      circle at 1.5em 30%,
      var(--light) calc(0.2em - 1px),
      #0000 0.2em
    ),
    radial-gradient(
      circle at 5.5em 30%,
      var(--light) calc(0.2em - 1px),
      #0000 0.2em
    ),
    linear-gradient(var(--light) 0 0) 1.5em calc(15% + 0.18em) / 4em
      calc(0.4em - 0.5px) no-repeat,
    linear-gradient(90deg, var(--base), var(--transparent) 1em),
    linear-gradient(var(--transparent) 70%, var(--dark) 80%),
    var(--base);
  border-radius: 2em;
  height: 100%;
  overflow: hidden;
}

[type="range"].kawaii::-moz-range-thumb {
  appearance: none;
  height: 2em;
  width: 2em;
  border: 0;
  color: var(--lighter);
  background:
    radial-gradient(
      circle at 0.75em 0.6em,
      var(--light) calc(0.2em - 1px),
      #0000 0.2em
    ),
    linear-gradient(90deg, var(--transparent) 0.75em, var(--base) 0) 0 0 / 100%
      50% no-repeat;
  border-radius: 50% 0 50% 50% 0;
  box-shadow:
    inset -0.5em 0 0.5em -0.25em var(--base),
    1em 0 0 0.25em,
    2em 0 0 0.25em,
    3em 0 0 0.25em,
    4em 0 0 0.25em,
    5em 0 0 0.25em,
    6em 0 0 0.25em,
    7em 0 0 0.25em,
    8em 0 0 0.25em,
    9em 0 0 0.25em,
    10em 0 0 0.25em,
    11em 0 0 0.25em,
    12em 0 0 0.25em,
    12em 0 0 0.25em,
    13em 0 0 0.25em,
    14em 0 0 0.25em,
    15em 0 0 0.25em,
    16em 0 0 0.25em,
    17em 0 0 0.25em,
    18em 0 0 0.25em,
    19em 0 0 0.25em;
}
</style>
