[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "cursor-pro"
version = "1.1.0"
description = "专业的Cursor账户管理工具"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Cursor Pro Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "Cursor Pro Team", email = "<EMAIL>"}
]
keywords = ["cursor", "account", "management", "automation"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Utilities",
]
requires-python = ">=3.8"
dependencies = [
    "requests>=2.25.0",
    "flask>=2.0.0",
    "sqlite3",
    "cryptography>=3.0.0",
    "pyyaml>=5.4.0",
    "click>=8.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "pytest-cov>=2.0",
    "black>=21.0",
    "flake8>=3.8",
    "mypy>=0.800",
    "pre-commit>=2.15.0",
]
test = [
    "pytest>=6.0",
    "pytest-cov>=2.0",
    "pytest-mock>=3.6.0",
]

[project.urls]
Homepage = "https://github.com/cursor-pro/cursor-pro"
Documentation = "https://cursor-pro.readthedocs.io/"
Repository = "https://github.com/cursor-pro/cursor-pro.git"
"Bug Tracker" = "https://github.com/cursor-pro/cursor-pro/issues"

[project.scripts]
cursor-pro = "cursor_pro.core.main:main"
cursor-pro-api = "cursor_pro.core.api_server:main"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
"cursor_pro.database" = ["*.sql"]
"cursor_pro" = ["*.json", "*.txt"]

# Black配置
[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# pytest配置
[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

# mypy配置
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
