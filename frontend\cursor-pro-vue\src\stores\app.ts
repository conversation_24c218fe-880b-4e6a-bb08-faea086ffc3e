/**
 * 应用状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

export const useAppStore = defineStore('app', () => {
  // 状态
  const loading = ref(false)
  const currentView = ref('dashboard') // dashboard, features, settings, about
  const sidebarCollapsed = ref(false)
  const theme = ref<'light' | 'dark'>('dark')
  const version = ref('Vue 3.5.17')
  
  // 模态框状态
  const modals = ref({
    settings: false,
    confirm: false,
    about: false
  })

  // 确认对话框状态
  const confirmDialog = ref({
    visible: false,
    title: '',
    message: '',
    onConfirm: null as ((selectedSteps?: string[]) => void) | null,
    onCancel: null as (() => void) | null
  })

  // 计算属性
  const isLoading = computed(() => loading.value)
  const isDarkTheme = computed(() => theme.value === 'dark')

  // 方法
  const setLoading = (state: boolean) => {
    loading.value = state
  }

  const setCurrentView = (view: string) => {
    console.log('🔄 切换视图:', view)
    currentView.value = view
  }

  // 强制切换到dashboard
  const forceToDashboard = () => {
    console.log('🎯 强制切换到Dashboard')
    currentView.value = 'dashboard'
  }

  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }

  const setSidebarCollapsed = (collapsed: boolean) => {
    sidebarCollapsed.value = collapsed
  }

  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
    // 应用主题到document
    document.documentElement.setAttribute('data-theme', theme.value)
  }

  const setTheme = (newTheme: 'light' | 'dark') => {
    theme.value = newTheme
    document.documentElement.setAttribute('data-theme', newTheme)
  }

  // 模态框控制
  const openModal = (modalName: keyof typeof modals.value) => {
    modals.value[modalName] = true
  }

  const closeModal = (modalName: keyof typeof modals.value) => {
    modals.value[modalName] = false
  }

  const closeAllModals = () => {
    Object.keys(modals.value).forEach(key => {
      modals.value[key as keyof typeof modals.value] = false
    })
  }

  // 确认对话框
  const showConfirm = (
    title: string,
    message: string,
    onConfirm?: (selectedSteps?: string[]) => void,
    onCancel?: () => void
  ) => {
    confirmDialog.value = {
      visible: true,
      title,
      message,
      onConfirm: onConfirm || null,
      onCancel: onCancel || null
    }
  }

  const hideConfirm = () => {
    confirmDialog.value.visible = false
    confirmDialog.value.onConfirm = null
    confirmDialog.value.onCancel = null
  }

  const confirmAction = (selectedSteps?: string[]) => {
    if (confirmDialog.value.onConfirm) {
      confirmDialog.value.onConfirm(selectedSteps)
    }
    hideConfirm()
  }

  const cancelAction = () => {
    if (confirmDialog.value.onCancel) {
      confirmDialog.value.onCancel()
    }
    hideConfirm()
  }

  // 复制模态框
  const showCopyModal = (message: string) => {
    // 触发全局事件，让App.vue处理
    if (typeof window !== 'undefined') {
      ;(window as any).showCopyModal?.(message)
    }
  }

  // 初始化
  const init = () => {
    // 设置初始主题
    setTheme(theme.value)
    
    // 从localStorage恢复设置
    const savedTheme = localStorage.getItem('cursor-theme')
    if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {
      setTheme(savedTheme)
    }

    const savedSidebarState = localStorage.getItem('cursor-sidebar-collapsed')
    if (savedSidebarState) {
      setSidebarCollapsed(JSON.parse(savedSidebarState))
    }
  }

  // 保存设置到localStorage
  const saveSettings = () => {
    localStorage.setItem('cursor-theme', theme.value)
    localStorage.setItem('cursor-sidebar-collapsed', JSON.stringify(sidebarCollapsed.value))
  }

  // 监听设置变化并保存
  const watchSettings = () => {
    // 这里可以添加watch逻辑，但在setup语法中通常在组件中处理
  }

  // 显示成功消息
  const showSuccess = (message: string) => {
    console.log('[SUCCESS]:', message)
    ElMessage.success({
      message: message,
      duration: 4000,
      showClose: true,
      center: true
    })
  }

  // 显示错误消息
  const showError = (message: string) => {
    console.error('[ERROR]:', message)
    ElMessage.error({
      message: message,
      duration: 5000,
      showClose: true,
      center: true
    })
  }

  // 显示警告消息
  const showWarning = (message: string) => {
    console.warn('[WARNING]:', message)
    ElMessage.warning({
      message: message,
      duration: 4000,
      showClose: true,
      center: true
    })
  }

  return {
    // 状态
    loading,
    currentView,
    sidebarCollapsed,
    theme,
    version,
    modals,
    confirmDialog,
    
    // 计算属性
    isLoading,
    isDarkTheme,
    
    // 方法
    setLoading,
    setCurrentView,
    forceToDashboard,
    toggleSidebar,
    setSidebarCollapsed,
    toggleTheme,
    setTheme,
    openModal,
    closeModal,
    closeAllModals,
    showConfirm,
    hideConfirm,
    confirmAction,
    cancelAction,
    showCopyModal,
    showSuccess,
    showError,
    showWarning,
    init,
    saveSettings,
    watchSettings
  }
})
