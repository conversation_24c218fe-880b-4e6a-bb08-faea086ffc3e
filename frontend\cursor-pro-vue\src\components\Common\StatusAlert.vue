<template>
  <div
    role="alert"
    :class="alertClasses"
    class="status-alert"
  >
    <svg
      stroke="currentColor"
      viewBox="0 0 24 24"
      fill="none"
      :class="iconClasses"
      class="status-icon"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        v-if="type === 'success'"
        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
        stroke-width="2"
        stroke-linejoin="round"
        stroke-linecap="round"
      ></path>
      <path
        v-else-if="type === 'error'"
        d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
        stroke-width="2"
        stroke-linejoin="round"
        stroke-linecap="round"
      ></path>
      <path
        v-else-if="type === 'warning'"
        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
        stroke-width="2"
        stroke-linejoin="round"
        stroke-linecap="round"
      ></path>
      <path
        v-else
        d="M13 16h-1v-4h1m0-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
        stroke-width="2"
        stroke-linejoin="round"
        stroke-linecap="round"
      ></path>
    </svg>
    <div class="status-content">
      <p :class="textClasses" class="status-title">
        {{ title }}
      </p>
      <p v-if="message" :class="messageClasses" class="status-message">
        {{ message }}
      </p>
    </div>
    <button
      v-if="closable"
      @click="$emit('close')"
      :class="closeButtonClasses"
      class="status-close"
    >
      <svg class="close-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
    </button>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  type?: 'success' | 'info' | 'warning' | 'error'
  title: string
  message?: string
  closable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'info',
  closable: false
})

defineEmits<{
  close: []
}>()

const alertClasses = computed(() => {
  switch (props.type) {
    case 'success':
      return 'status-alert--success'
    case 'error':
      return 'status-alert--error'
    case 'warning':
      return 'status-alert--warning'
    default:
      return 'status-alert--info'
  }
})

const iconClasses = computed(() => {
  switch (props.type) {
    case 'success':
      return 'icon--success'
    case 'error':
      return 'icon--error'
    case 'warning':
      return 'icon--warning'
    default:
      return 'icon--info'
  }
})

const textClasses = computed(() => {
  switch (props.type) {
    case 'success':
      return 'text--success'
    case 'error':
      return 'text--error'
    case 'warning':
      return 'text--warning'
    default:
      return 'text--info'
  }
})

const messageClasses = computed(() => {
  switch (props.type) {
    case 'success':
      return 'message--success'
    case 'error':
      return 'message--error'
    case 'warning':
      return 'message--warning'
    default:
      return 'message--info'
  }
})

const closeButtonClasses = computed(() => {
  switch (props.type) {
    case 'success':
      return 'close--success'
    case 'error':
      return 'close--error'
    case 'warning':
      return 'close--warning'
    default:
      return 'close--info'
  }
})
</script>

<style scoped>
.status-alert {
  border-left: 4px solid;
  padding: 12px 16px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  transition: all 0.3s ease-in-out;
  transform: scale(1);
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-alert:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* Success styles */
.status-alert--success {
  background-color: #f0f9ff;
  border-color: #10b981;
  color: #065f46;
}

.status-alert--success:hover {
  background-color: #ecfdf5;
}

/* Error styles */
.status-alert--error {
  background-color: #fef2f2;
  border-color: #ef4444;
  color: #991b1b;
}

.status-alert--error:hover {
  background-color: #fee2e2;
}

/* Warning styles */
.status-alert--warning {
  background-color: #fffbeb;
  border-color: #f59e0b;
  color: #92400e;
}

.status-alert--warning:hover {
  background-color: #fef3c7;
}

/* Info styles */
.status-alert--info {
  background-color: #eff6ff;
  border-color: #3b82f6;
  color: #1e40af;
}

.status-alert--info:hover {
  background-color: #dbeafe;
}

/* Icon styles */
.status-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  margin-right: 12px;
}

.icon--success {
  color: #10b981;
}

.icon--error {
  color: #ef4444;
}

.icon--warning {
  color: #f59e0b;
}

.icon--info {
  color: #3b82f6;
}

/* Content styles */
.status-content {
  flex: 1;
}

.status-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0;
  line-height: 1.4;
}

.status-message {
  font-size: 12px;
  margin: 4px 0 0 0;
  opacity: 0.8;
  line-height: 1.3;
}

/* Close button styles */
.status-close {
  margin-left: 12px;
  padding: 4px;
  border-radius: 50%;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  width: 16px;
  height: 16px;
}

.close--success {
  color: #10b981;
}

.close--success:hover {
  background-color: rgba(16, 185, 129, 0.1);
}

.close--error {
  color: #ef4444;
}

.close--error:hover {
  background-color: rgba(239, 68, 68, 0.1);
}

.close--warning {
  color: #f59e0b;
}

.close--warning:hover {
  background-color: rgba(245, 158, 11, 0.1);
}

.close--info {
  color: #3b82f6;
}

.close--info:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .status-alert--success {
    background-color: #064e3b;
    color: #a7f3d0;
  }

  .status-alert--error {
    background-color: #7f1d1d;
    color: #fca5a5;
  }

  .status-alert--warning {
    background-color: #78350f;
    color: #fcd34d;
  }

  .status-alert--info {
    background-color: #1e3a8a;
    color: #93c5fd;
  }
}
</style>
