/**
 * 全局类型定义
 */

// 基础类型
export interface BaseResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// 账户相关类型
export interface AccountInfo {
  email: string
  plan: string
  trial_days: number
  pro_used: number
  pro_total: number
  pro_percent: number
  basic_total: string
}

export interface RegisteredAccount {
  email: string
  password?: string
  status?: string
  usage?: string
  used?: number
  total?: number
  firstName?: string
  lastName?: string
  registeredAt?: string
  isActive?: boolean
  trialDays?: number
  masterEmail?: string
  token?: string
  originalIndex?: number
  hasToken?: boolean
  hasBackup?: boolean
  machine_ids?: string
  proUsed?: number
  proTotal?: number
  accountType?: string
}

// 日志相关类型
export interface LogEntry {
  id: number
  timestamp: number
  level: 'info' | 'success' | 'warning' | 'error'
  content: string
  source?: string
}

export interface LogFilters {
  levels: {
    info: boolean
    success: boolean
    warning: boolean
    error: boolean
  }
  source: string
  content: string
}

// 应用状态类型
export interface AppState {
  loading: boolean
  currentView: string
  sidebarCollapsed: boolean
  theme: 'light' | 'dark'
  version: string
  modals: {
    settings: boolean
    confirm: boolean
    about: boolean
  }
  confirmDialog: {
    visible: boolean
    title: string
    message: string
    onConfirm: (() => void) | null
    onCancel: (() => void) | null
  }
}

// 操作按钮类型
export interface OperationButtonProps {
  type?: 'primary' | 'secondary' | 'warning' | 'danger' | 'success'
  icon?: string
  text: string
  description?: string
  loading?: boolean
  disabled?: boolean
  block?: boolean
}

export interface ActionItem {
  id: string
  text: string
  icon?: string
  description?: string
  disabled?: boolean
}

// 模态框类型
export interface BaseModalProps {
  visible: boolean
  title?: string
  width?: string
  maxWidth?: string
  closable?: boolean
  maskClosable?: boolean
}

export interface ConfirmModalProps {
  visible: boolean
  title: string
  message: string
  confirmText?: string
  cancelText?: string
  closable?: boolean
}

export interface InputModalProps {
  visible: boolean
  title: string
  message?: string
  placeholder?: string
  defaultValue?: string
  confirmText?: string
  cancelText?: string
}

// API相关类型
export interface ApiConfig {
  timeout: number
  retryCount: number
  retryDelay: number
}

export interface ApiError {
  code: string
  message: string
  details?: any
}

// 设置相关类型
export interface UserSettings {
  theme: 'light' | 'dark'
  sidebarCollapsed: boolean
  autoScroll: boolean
  maxLogs: number
  language: string
}

export interface RegisterConfig {
  email: string
  domain: string
  firstName: string
  lastName: string
  password: string
  region: string
  mode: string
}

export interface AdvancedOptions {
  showBrowser: boolean
  timeout: number
  retryCount: number
}

// 组件事件类型
export interface ComponentEvents {
  'update:modelValue': [value: any]
  'change': [value: any]
  'click': []
  'confirm': []
  'cancel': []
  'close': []
  'open': []
  'refresh': []
  'save': []
  'delete': []
  'copy': [text: string]
  'select': [item: any]
}

// 表单相关类型
export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'checkbox' | 'textarea'
  value: any
  placeholder?: string
  required?: boolean
  disabled?: boolean
  options?: Array<{ label: string; value: any }>
  validation?: {
    pattern?: RegExp
    min?: number
    max?: number
    message?: string
  }
}

export interface FormData {
  [key: string]: any
}

export interface FormErrors {
  [key: string]: string
}

// 文件相关类型
export interface FileInfo {
  name: string
  size: number
  type: string
  lastModified: number
  content?: string
}

// 主题相关类型
export interface ThemeConfig {
  colors: {
    primary: string
    success: string
    warning: string
    error: string
    background: string
    surface: string
    border: string
    textPrimary: string
    textSecondary: string
    textMuted: string
  }
  fonts: {
    mono: string
    sans: string
  }
  breakpoints: {
    sm: string
    md: string
    lg: string
    xl: string
  }
}

// 动画相关类型
export interface AnimationConfig {
  duration: {
    fast: string
    normal: string
    slow: string
  }
  easing: {
    ease: string
    easeIn: string
    easeOut: string
    easeInOut: string
  }
}

// 路由相关类型
export interface RouteInfo {
  name: string
  path: string
  component: any
  meta?: {
    title?: string
    icon?: string
    requiresAuth?: boolean
  }
}

// 错误相关类型
export interface ErrorInfo {
  code: string
  message: string
  stack?: string
  timestamp: number
  context?: any
}

// 性能相关类型
export interface PerformanceMetrics {
  loadTime: number
  renderTime: number
  memoryUsage: number
  apiResponseTime: number
}

// 设备相关类型
export interface DeviceInfo {
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  screenSize: 'small' | 'medium' | 'large'
  userAgent: string
}

// 工具函数类型
export type SafeJsonParse = <T = any>(str: string, defaultValue: T) => T
export type SafeJsonStringify = (obj: any, defaultValue?: string) => string
export type Debounce = <T extends (...args: any[]) => any>(func: T, wait: number) => (...args: Parameters<T>) => void
export type Throttle = <T extends (...args: any[]) => any>(func: T, limit: number) => (...args: Parameters<T>) => void

// 存储相关类型
export interface StorageAdapter {
  get<T = any>(key: string, defaultValue: T): T
  set(key: string, value: any): boolean
  remove(key: string): boolean
  clear(): boolean
}

// 插件相关类型
export interface PluginOptions {
  name: string
  version: string
  enabled: boolean
  config?: any
}

// Electron API类型定义
export interface ElectronAPI {
  // 窗口控制
  minimizeWindow: () => Promise<void>
  maximizeWindow: () => Promise<void>
  closeWindow: () => Promise<void>
  isMaximized: () => Promise<boolean>

  // 窗口状态监听
  onWindowStateChanged?: (callback: (data: { maximized: boolean }) => void) => void
  removeWindowStateListener?: () => void

  // 应用信息
  isElectron?: boolean

  // 平台信息
  platform: string

  // 版本信息
  versions: {
    node: string
    chrome: string
    electron: string
  }
}

// 全局窗口类型扩展
declare global {
  interface Window {
    // Electron API
    electronAPI?: ElectronAPI

    // PyWebView API (保持兼容性)
    pywebview?: {
      api: {
        get_account_info: () => Promise<any>
        get_registered_accounts: () => Promise<any>
        get_log: () => Promise<string>
        bypass_version_check: () => Promise<string>
        bypass_token_limit: () => Promise<string>
        get_machine_id: () => Promise<string>
        reset_machine_id: () => Promise<string>
        fake_machine_id: () => Promise<string>
        restore_machine_id: () => Promise<string>
        show_config: () => Promise<string>
        get_contributors: () => Promise<string>
        get_vue_info: () => Promise<any>
        [key: string]: any
      }
    }

    // 模态框方法
    showCopyModal?: (message: string) => void
    showInputModal?: (config: any) => void
    showConfigModal?: () => void
    showOAuthModal?: () => void
    showAdvancedModal?: () => void
    showLanguageModal?: () => void
  }
}

// 导出所有类型
// export * from './account'
// export * from './log'
// export * from './common'
