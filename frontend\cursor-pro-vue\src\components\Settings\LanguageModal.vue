<template>
  <BaseModal
    :visible="visible"
    title="语言设置"
    width="400px"
    @close="handleClose"
  >
    <!-- 语言选择界面 -->
    <div class="language-content">
      <div class="language-title">选择界面语言</div>
      
      <div class="language-options">
        <div 
          v-for="lang in languages"
          :key="lang.code"
          class="language-option"
          :class="{ 'active': currentLanguage === lang.code }"
          @click="selectLanguage(lang.code)"
        >
          <div class="language-flag">{{ lang.flag }}</div>
          <div class="language-info">
            <div class="language-name">{{ lang.name }}</div>
            <div class="language-native">{{ lang.native }}</div>
          </div>
          <div v-if="currentLanguage === lang.code" class="language-check">✓</div>
        </div>
      </div>

      <div class="language-note">
        <div class="note-icon">💡</div>
        <div class="note-text">
          语言更改将在下次启动时生效
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <button
        class="card__button card__button--cancel"
        @click="handleClose"
      >
        取消
      </button>
      <button
        class="card__button card__button--confirm"
        @click="handleSave"
        :disabled="!hasChanges"
      >
        保存设置
      </button>
    </template>
  </BaseModal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import BaseModal from '../Modal/BaseModal.vue'
import { useLogStore } from '@/stores/logs'

interface LanguageModalProps {
  visible: boolean
}

interface LanguageModalEvents {
  'close': []
  'update:visible': [visible: boolean]
}

interface Language {
  code: string
  name: string
  native: string
  flag: string
}

const props = defineProps<LanguageModalProps>()
const emit = defineEmits<LanguageModalEvents>()

const logStore = useLogStore()

// 支持的语言列表 - 仅中文
const languages: Language[] = [
  {
    code: 'zh-CN',
    name: '简体中文',
    native: '简体中文',
    flag: '🇨🇳'
  },
  {
    code: 'zh-TW',
    name: '繁體中文',
    native: '繁體中文',
    flag: '🇹🇼'
  }
]

// 状态
const currentLanguage = ref('zh-CN') // 默认简体中文
const selectedLanguage = ref('zh-CN')

// 计算属性
const hasChanges = computed(() => {
  return currentLanguage.value !== selectedLanguage.value
})

// 方法
const selectLanguage = (code: string) => {
  selectedLanguage.value = code
}

const handleSave = () => {
  if (!hasChanges.value) return
  
  currentLanguage.value = selectedLanguage.value
  
  // 保存到本地存储
  localStorage.setItem('cursor-language', selectedLanguage.value)
  
  const selectedLang = languages.find(lang => lang.code === selectedLanguage.value)
  logStore.addSuccess(`语言已设置为: ${selectedLang?.name}`, 'Language')
  
  handleClose()
}

const handleClose = () => {
  // 重置选择
  selectedLanguage.value = currentLanguage.value
  
  emit('close')
  emit('update:visible', false)
}

// 初始化
const initLanguage = () => {
  const savedLanguage = localStorage.getItem('cursor-language')
  if (savedLanguage && languages.some(lang => lang.code === savedLanguage)) {
    currentLanguage.value = savedLanguage
    selectedLanguage.value = savedLanguage
  }
}

// 组件挂载时初始化
initLanguage()
</script>

<style scoped>
.language-content {
  padding: 20px 0;
}

.language-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #e5e5e5;
  margin-bottom: 20px;
  text-align: center;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.language-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 24px;
}

.language-option {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #1a1e23;
  border: 1px solid #374151;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  gap: 12px;
}

.language-option:hover {
  background: #252a31;
  border-color: #4b5563;
}

.language-option.active {
  background: #1e40af;
  border-color: #3b82f6;
}

.language-flag {
  font-size: 1.5rem;
  min-width: 32px;
  text-align: center;
}

.language-info {
  flex: 1;
}

.language-name {
  font-size: 0.95rem;
  font-weight: 600;
  color: #e5e5e5;
  margin-bottom: 2px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.language-native {
  font-size: 0.8rem;
  color: #94a3b8;
}

.language-check {
  color: #10b981;
  font-size: 1.2rem;
  font-weight: bold;
}

.language-note {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 6px;
}

.note-icon {
  font-size: 1.2rem;
}

.note-text {
  font-size: 0.85rem;
  color: #94a3b8;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.modal-btn {
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  min-width: 80px;
}

.modal-btn {
  background: #374151;
  border-color: #4b5563;
  color: #e5e5e5;
}

.modal-btn:hover:not(:disabled) {
  background: #4b5563;
  border-color: #6b7280;
}

.modal-btn.primary {
  background: #10b981;
  border-color: #10b981;
  color: #fff;
}

.modal-btn.primary:hover:not(:disabled) {
  background: #059669;
  border-color: #059669;
}

/* 按钮样式 */
.card__button {
  border: 3px solid #000;
  background: #000;
  color: #fff;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: bold;
  text-transform: uppercase;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s;
  min-width: 100px;
}

.card__button--cancel {
  background: #dc2626;
  border-color: #dc2626;
}

.card__button--cancel::before {
  content: "Sure?";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 105%;
  background-color: #f87171;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.card__button--cancel:hover::before {
  transform: translateY(0);
}

.card__button--confirm::before {
  content: "Sure?";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 105%;
  background-color: #5ad641;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.card__button--confirm:hover::before {
  transform: translateY(0);
}

.card__button:active {
  transform: scale(0.95);
}

.card__button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.card__button:disabled::before {
  display: none;
}
</style>
