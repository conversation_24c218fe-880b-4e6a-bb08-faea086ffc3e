/**
 * 系统相关API服务
 * 处理版本绕过、配置管理、系统操作等功能
 */

import { BaseAPI } from '../base'
import type {
  SystemInfo,
  HealthCheckResponse,
  AppConfig
} from '../types'

// 系统操作相关类型
export interface BypassResult {
  success: boolean
  message: string
  version?: string
  method: string
  timestamp: string
}

export interface ConfigInfo {
  version: string
  environment: 'development' | 'production' | 'test'
  features: Record<string, boolean>
  settings: Record<string, any>
  lastModified: string
}

export interface SystemOperation {
  id: string
  type: 'reset' | 'update' | 'bypass' | 'config'
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: number
  message: string
  startTime: string
  endTime?: string
  result?: any
}

/**
 * 系统服务类
 * 继承BaseAPI，提供系统相关的API调用
 */
export class SystemService extends BaseAPI {
  /**
   * 版本绕过检查
   */
  async bypassVersionCheck(): Promise<BypassResult> {
    console.log('🔄 [SystemService] 执行版本绕过检查')

    const response = await this.post<BypassResult>('system/bypass/version')
    console.log('📡 [SystemService] 版本绕过响应:', response)
    
    return response.data
  }

  /**
   * Token限制绕过
   */
  async bypassTokenLimit(): Promise<BypassResult> {
    console.log('🔄 [SystemService] 执行Token限制绕过')

    const response = await this.post<BypassResult>('system/bypass/token-limit')
    console.log('📡 [SystemService] Token限制绕过响应:', response)
    
    return response.data
  }

  /**
   * 获取配置信息
   */
  async getConfigInfo(): Promise<ConfigInfo> {
    console.log('🔄 [SystemService] 获取配置信息')

    const response = await this.get<ConfigInfo>('system/config/info')
    console.log('📡 [SystemService] 配置信息响应:', response)
    
    return response.data
  }

  /**
   * 显示配置
   */
  async showConfig(): Promise<{
    config: Record<string, any>
    metadata: {
      source: string
      lastModified: string
      size: number
    }
  }> {
    console.log('🔄 [SystemService] 显示配置')

    const response = await this.get<{
      config: Record<string, any>
      metadata: {
        source: string
        lastModified: string
        size: number
      }
    }>('system/config/show')
    
    console.log('📡 [SystemService] 显示配置响应:', response)
    return response.data
  }

  /**
   * 重置配置
   */
  async resetConfig(options?: {
    keepUserData?: boolean
    backupFirst?: boolean
    resetToDefaults?: boolean
  }): Promise<{
    success: boolean
    message: string
    backupId?: string
  }> {
    console.log('🔄 [SystemService] 重置配置:', options)

    const response = await this.post<{
      success: boolean
      message: string
      backupId?: string
    }>('system/config/reset', options)
    
    console.log('📡 [SystemService] 重置配置响应:', response)
    return response.data
  }

  /**
   * 完全重置Cursor
   */
  async totallyResetCursor(): Promise<{
    success: boolean
    message: string
    operations: string[]
    warnings?: string[]
  }> {
    console.log('🔄 [SystemService] 完全重置Cursor')

    const response = await this.post<{
      success: boolean
      message: string
      operations: string[]
      warnings?: string[]
    }>('system/reset/total')
    
    console.log('📡 [SystemService] 完全重置响应:', response)
    return response.data
  }

  /**
   * 切换语言
   */
  async changeLanguage(language: string): Promise<{
    success: boolean
    message: string
    currentLanguage: string
  }> {
    console.log('🔄 [SystemService] 切换语言:', language)

    const response = await this.post<{
      success: boolean
      message: string
      currentLanguage: string
    }>('system/language/change', { language })
    
    console.log('📡 [SystemService] 语言切换响应:', response)
    return response.data
  }



  /**
   * 切换自动更新
   */
  async toggleAutoUpdate(enabled?: boolean): Promise<{
    success: boolean
    message: string
    autoUpdateEnabled: boolean
  }> {
    console.log('🔄 [SystemService] 切换自动更新:', enabled)

    const response = await this.post<{
      success: boolean
      message: string
      autoUpdateEnabled: boolean
    }>('system/auto-update/toggle', { enabled })
    
    console.log('📡 [SystemService] 自动更新切换响应:', response)
    return response.data
  }

  /**
   * 打开URL
   */
  async openUrl(url: string, options?: {
    newWindow?: boolean
    incognito?: boolean
  }): Promise<{
    success: boolean
    message: string
  }> {
    console.log('🔄 [SystemService] 打开URL:', url, options)

    const response = await this.post<{
      success: boolean
      message: string
    }>('system/url/open', { url, ...options })
    
    console.log('📡 [SystemService] 打开URL响应:', response)
    return response.data
  }

  /**
   * 显示消息
   */
  async showMessage(message: string, options?: {
    type?: 'info' | 'warning' | 'error' | 'success'
    duration?: number
    title?: string
  }): Promise<{
    success: boolean
    messageId: string
  }> {
    console.log('🔄 [SystemService] 显示消息:', message, options)

    const response = await this.post<{
      success: boolean
      messageId: string
    }>('system/message/show', { message, ...options })
    
    console.log('📡 [SystemService] 显示消息响应:', response)
    return response.data
  }

  /**
   * 获取系统信息
   */
  async getSystemInfo(): Promise<SystemInfo> {
    console.log('🔄 [SystemService] 获取系统信息')

    const response = await this.get<SystemInfo>('system/info')
    console.log('📡 [SystemService] 系统信息响应:', response)
    
    return response.data
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<HealthCheckResponse> {
    console.log('🔄 [SystemService] 执行健康检查')

    const response = await this.get<HealthCheckResponse>('system/health')
    console.log('📡 [SystemService] 健康检查响应:', response)
    
    return response.data
  }

  /**
   * 获取贡献者信息
   */
  async getContributors(): Promise<{
    contributors: Array<{
      name: string
      email?: string
      avatar?: string
      contributions: number
      role: string
      joinDate: string
    }>
    total: number
  }> {
    console.log('🔄 [SystemService] 获取贡献者信息')

    const response = await this.get<{
      contributors: Array<{
        name: string
        email?: string
        avatar?: string
        contributions: number
        role: string
        joinDate: string
      }>
      total: number
    }>('system/contributors')
    
    console.log('📡 [SystemService] 贡献者信息响应:', response)
    return response.data
  }

  /**
   * 获取系统操作历史
   */
  async getOperationHistory(limit: number = 50): Promise<{
    operations: SystemOperation[]
    total: number
  }> {
    console.log('🔄 [SystemService] 获取系统操作历史')

    const response = await this.get<{
      operations: SystemOperation[]
      total: number
    }>('system/operations/history', { limit })
    
    console.log('📡 [SystemService] 操作历史响应:', response)
    return response.data
  }

  /**
   * 获取系统日志
   */
  async getSystemLogs(options?: {
    level?: 'debug' | 'info' | 'warn' | 'error'
    startTime?: string
    endTime?: string
    limit?: number
  }): Promise<{
    logs: Array<{
      timestamp: string
      level: string
      message: string
      source: string
      data?: any
    }>
    total: number
  }> {
    console.log('🔄 [SystemService] 获取系统日志:', options)

    const response = await this.get<{
      logs: Array<{
        timestamp: string
        level: string
        message: string
        source: string
        data?: any
      }>
      total: number
    }>('system/logs', options)
    
    console.log('📡 [SystemService] 系统日志响应:', response)
    return response.data
  }

  /**
   * 清理系统缓存
   */
  async clearCache(types?: string[]): Promise<{
    success: boolean
    message: string
    clearedTypes: string[]
    freedSpace: number
  }> {
    console.log('🔄 [SystemService] 清理系统缓存:', types)

    const response = await this.post<{
      success: boolean
      message: string
      clearedTypes: string[]
      freedSpace: number
    }>('system/cache/clear', { types })
    
    console.log('📡 [SystemService] 清理缓存响应:', response)
    return response.data
  }
}

// 创建系统服务实例
export const systemService = new SystemService()

// 添加默认拦截器
systemService.addRequestInterceptor(async (config) => {
  // 为系统操作添加特殊标识
  config.headers = {
    ...config.headers,
    'X-Service': 'SystemService',
    'X-System-Operation': 'true'
  }
  return config
})

systemService.addResponseInterceptor(async (response) => {
  // 记录系统操作
  console.log(`⚙️ [SystemService] 系统操作完成: ${Date.now()}`)
  return response
})

systemService.addErrorInterceptor(async (error) => {
  // 系统操作的错误处理
  console.error('❌ [SystemService] 系统操作错误:', {
    type: error.type,
    message: error.message,
    status: error.status
  })
  return error
})
