#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor Pro - Electron 前端打包脚本
构建 Vue.js + Electron 桌面应用
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_node_npm():
    """检查 Node.js 和 npm 是否已安装"""
    try:
        # 检查 Node.js
        result = subprocess.run(['node', '--version'], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            print(f"✅ Node.js 已安装: {result.stdout.strip()}")
        else:
            print("❌ Node.js 未安装")
            return False

        # 检查 npm
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            print(f"✅ npm 已安装: {result.stdout.strip()}")
            return True
        else:
            print("❌ npm 未安装")
            return False

    except Exception as e:
        print(f"❌ 检查 Node.js/npm 时出错: {e}")
        print("请先安装 Node.js: https://nodejs.org/")
        return False

def install_dependencies():
    """安装前端依赖"""
    print("📦 安装前端依赖...")
    
    frontend_dir = Path('cursor-pro-vue')
    if not frontend_dir.exists():
        print("❌ 前端目录不存在: cursor-pro-vue")
        return False
    
    try:
        # 切换到前端目录
        os.chdir(frontend_dir)
        
        # 安装依赖
        subprocess.check_call(['npm', 'install'], shell=True)
        print("✅ 前端依赖安装成功")
        
        # 返回根目录
        os.chdir('..')
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        os.chdir('..')
        return False

def build_vue_app():
    """构建 Vue.js 应用"""
    print("🏗️ 构建 Vue.js 应用...")
    
    frontend_dir = Path('cursor-pro-vue')
    
    try:
        os.chdir(frontend_dir)
        
        # 构建 Vue 应用
        subprocess.check_call(['npm', 'run', 'build-only'], shell=True)
        print("✅ Vue.js 应用构建成功")
        
        os.chdir('..')
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Vue.js 构建失败: {e}")
        os.chdir('..')
        return False

def build_electron_app():
    """构建 Electron 应用"""
    print("📱 构建 Electron 桌面应用...")
    
    frontend_dir = Path('cursor-pro-vue')
    
    try:
        os.chdir(frontend_dir)
        
        # 构建 Electron 应用
        subprocess.check_call(['npm', 'run', 'dist:win'], shell=True)
        print("✅ Electron 应用构建成功")
        
        os.chdir('..')
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Electron 构建失败: {e}")
        os.chdir('..')
        return False

def copy_electron_build():
    """复制 Electron 构建结果到根目录"""
    print("📁 复制构建结果...")
    
    source_dir = Path('cursor-pro-vue/dist-electron')
    target_dir = Path('dist-frontend')
    
    if source_dir.exists():
        if target_dir.exists():
            shutil.rmtree(target_dir)
        
        shutil.copytree(source_dir, target_dir)
        print(f"✅ 构建结果已复制到: {target_dir}")
        return True
    else:
        print("❌ 未找到 Electron 构建结果")
        return False

def create_frontend_launcher():
    """创建前端启动脚本"""
    launcher_content = '''@echo off
title Cursor Pro - Desktop Application
color 0B

echo ========================================
echo  Cursor Pro - Desktop Application
echo ========================================
echo.

echo Starting Cursor Pro Desktop App...
echo.

REM 启动桌面应用
start "" "Cursor Pro Setup.exe"

echo.
echo Desktop application launched!
echo You can close this window now.
echo.
pause
'''
    
    with open('dist-frontend/launch-frontend.cmd', 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("✅ 创建前端启动脚本: launch-frontend.cmd")

def build_frontend():
    """构建前端的主函数"""
    print("🚀 开始构建 Electron 前端...")
    
    # 检查环境
    if not check_node_npm():
        return False
    
    # 安装依赖
    if not install_dependencies():
        return False
    
    # 构建 Vue 应用
    if not build_vue_app():
        return False
    
    # 构建 Electron 应用
    if not build_electron_app():
        return False
    
    # 复制构建结果
    if not copy_electron_build():
        return False
    
    # 创建启动脚本
    create_frontend_launcher()
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("🖥️  Cursor Pro - Electron 前端打包工具")
    print("=" * 50)
    
    if build_frontend():
        print("\n🎉 前端打包完成!")
        print("📦 打包文件位于 'dist-frontend' 目录")
        print("🚀 可以运行安装程序安装桌面应用")
    else:
        print("\n❌ 前端打包失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
