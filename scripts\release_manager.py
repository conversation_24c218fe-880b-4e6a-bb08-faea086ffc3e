#!/usr/bin/env python3
"""
版本发布管理器
开发者用于发布新版本和控制用户更新的工具
"""

import requests
import json
import sys
from datetime import datetime

class ReleaseManager:
    def __init__(self):
        self.api_base = "https://cursorpro-api.vercel.app"
        self.admin_key = "cursor-admin-2025"  # 管理员密钥
        
    def get_current_config(self):
        """获取当前版本配置"""
        try:
            response = requests.get(
                f"{self.api_base}/api/admin",
                headers={'X-Admin-Key': self.admin_key},
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()['data']
            else:
                print(f"❌ 获取配置失败: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return None
    
    def update_config(self, config_update):
        """更新版本配置"""
        try:
            response = requests.post(
                f"{self.api_base}/api/admin",
                headers={
                    'X-Admin-Key': self.admin_key,
                    'Content-Type': 'application/json'
                },
                json=config_update,
                timeout=10
            )
            
            if response.status_code == 200:
                print("✅ 配置更新成功")
                return True
            else:
                print(f"❌ 配置更新失败: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return False
    
    def release_new_version(self, version, description="", force_update=False, min_version=None):
        """发布新版本"""
        print(f"🚀 发布新版本: {version}")
        
        config_update = {
            "version_control": {
                "latest_version": version,
                "min_supported_version": min_version or version,
                "force_update": force_update,
                "update_message": f"发现新版本 {version}！{description}",
                "download_message": "请到QQ群/微信群下载最新版本",
                "download_url": "https://github.com/tul345/cursor-pro-main/releases"
            },
            "add_version_history": {
                "version": version,
                "description": description,
                "critical": force_update
            },
            "admin_notes": f"版本 {version} 于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} 发布"
        }
        
        return self.update_config(config_update)
    
    def force_update_all(self, min_version):
        """强制所有低于指定版本的用户更新"""
        print(f"⚠️ 强制更新: 所有低于 {min_version} 的版本将被要求更新")
        
        config_update = {
            "version_control": {
                "min_supported_version": min_version,
                "force_update": True,
                "update_message": f"检测到关键更新，必须升级到 {min_version} 或更高版本才能继续使用",
                "download_message": "请立即到QQ群/微信群下载最新版本"
            }
        }
        
        return self.update_config(config_update)
    
    def disable_old_versions(self, versions_to_block):
        """禁用特定版本"""
        print(f"🚫 禁用版本: {', '.join(versions_to_block)}")
        
        config_update = {
            "blocked_versions": versions_to_block,
            "version_control": {
                "update_message": "您使用的版本已停止支持，请更新到最新版本",
                "force_update": True
            }
        }
        
        return self.update_config(config_update)
    
    def enable_maintenance_mode(self, message="系统维护中，请稍后再试"):
        """启用维护模式"""
        print("🔧 启用维护模式")
        
        config_update = {
            "version_control": {
                "maintenance_mode": True,
                "maintenance_message": message
            }
        }
        
        return self.update_config(config_update)
    
    def disable_maintenance_mode(self):
        """禁用维护模式"""
        print("✅ 禁用维护模式")
        
        config_update = {
            "version_control": {
                "maintenance_mode": False
            }
        }
        
        return self.update_config(config_update)
    
    def show_status(self):
        """显示当前状态"""
        config = self.get_current_config()
        if not config:
            return
        
        vc = config.get('version_control', {})
        
        print("\n" + "="*50)
        print("📊 当前版本控制状态")
        print("="*50)
        print(f"最新版本: {vc.get('latest_version', 'N/A')}")
        print(f"最低支持版本: {vc.get('min_supported_version', 'N/A')}")
        print(f"强制更新: {'是' if vc.get('force_update') else '否'}")
        print(f"维护模式: {'是' if vc.get('maintenance_mode') else '否'}")
        print(f"更新消息: {vc.get('update_message', 'N/A')}")
        
        if config.get('blocked_versions'):
            print(f"被禁用版本: {', '.join(config['blocked_versions'])}")
        
        print("\n📋 版本历史:")
        for version in config.get('version_history', [])[:5]:  # 显示最近5个版本
            print(f"  - {version.get('version')} ({version.get('release_date')}) - {version.get('description')}")
        
        print("="*50)

def main():
    """主函数 - 交互式版本管理"""
    manager = ReleaseManager()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "status":
            manager.show_status()
        elif command == "release" and len(sys.argv) >= 3:
            version = sys.argv[2]
            description = sys.argv[3] if len(sys.argv) > 3 else ""
            force = "--force" in sys.argv
            manager.release_new_version(version, description, force)
        elif command == "force-update" and len(sys.argv) >= 3:
            min_version = sys.argv[2]
            manager.force_update_all(min_version)
        elif command == "maintenance":
            if len(sys.argv) > 2 and sys.argv[2] == "off":
                manager.disable_maintenance_mode()
            else:
                message = sys.argv[2] if len(sys.argv) > 2 else "系统维护中，请稍后再试"
                manager.enable_maintenance_mode(message)
        else:
            print("❌ 未知命令")
    else:
        # 交互式模式
        print("🎮 版本发布管理器")
        print("1. 查看状态")
        print("2. 发布新版本")
        print("3. 强制更新")
        print("4. 维护模式")
        print("0. 退出")
        
        choice = input("\n请选择操作: ")
        
        if choice == "1":
            manager.show_status()
        elif choice == "2":
            version = input("新版本号: ")
            description = input("版本描述: ")
            force = input("是否强制更新? (y/N): ").lower() == 'y'
            manager.release_new_version(version, description, force)
        elif choice == "3":
            min_version = input("最低支持版本: ")
            manager.force_update_all(min_version)
        elif choice == "4":
            action = input("启用(on)还是禁用(off)维护模式? ")
            if action == "on":
                message = input("维护消息: ")
                manager.enable_maintenance_mode(message)
            else:
                manager.disable_maintenance_mode()

if __name__ == "__main__":
    main()
