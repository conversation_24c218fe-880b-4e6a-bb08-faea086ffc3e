<template>
  <div class="window-controls">
    <!-- 最小化按钮 -->
    <div
      class="cartoon-droplet cartoon-green"
      @click="handleMinimize"
      title="最小化"
    >
    </div>

    <!-- 最大化/还原按钮 -->
    <div
      class="cartoon-droplet cartoon-yellow"
      @click="handleMaximize"
      :title="isMaximized ? '还原窗口' : '最大化窗口'"
    >
    </div>

    <!-- 关闭按钮 -->
    <div
      class="cartoon-droplet cartoon-red"
      @click="handleClose"
      title="关闭"
    >
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const isMaximized = ref(false)

// 组件挂载时检查API状态和初始化窗口状态
onMounted(async () => {
  console.log('🖥️ WindowControls 组件已挂载')
  console.log('🔍 检查 Electron API 状态:')
  console.log('  - window.electronAPI:', !!window.electronAPI)
  console.log('  - isElectron:', window.electronAPI?.isElectron)
  console.log('  - platform:', window.electronAPI?.platform)

  if (window.electronAPI) {
    console.log('  - minimizeWindow:', !!window.electronAPI.minimizeWindow)
    console.log('  - maximizeWindow:', !!window.electronAPI.maximizeWindow)
    console.log('  - closeWindow:', !!window.electronAPI.closeWindow)
    console.log('  - isMaximized:', !!window.electronAPI.isMaximized)

    // 初始化窗口最大化状态
    if (window.electronAPI.isMaximized) {
      try {
        isMaximized.value = await window.electronAPI.isMaximized()
        console.log('📏 初始窗口状态:', isMaximized.value ? '最大化' : '正常')
      } catch (error) {
        console.error('❌ 获取窗口状态失败:', error)
      }
    }

    // 监听窗口状态变化
    if (window.electronAPI.onWindowStateChanged) {
      window.electronAPI.onWindowStateChanged((data: { maximized: boolean }) => {
        console.log('🔄 窗口状态变化:', data.maximized ? '最大化' : '正常')
        isMaximized.value = data.maximized
      })
    }
  } else {
    console.warn('⚠️ Electron API 未找到，窗口控制功能将不可用')
  }
})

// 组件卸载时清理监听器
onUnmounted(() => {
  if (window.electronAPI?.removeWindowStateListener) {
    window.electronAPI.removeWindowStateListener()
    console.log('🧹 已清理窗口状态监听器')
  }
})

// 最小化窗口
const handleMinimize = async () => {
  console.log('🔽 最小化窗口')
  console.log('electronAPI available:', !!window.electronAPI)
  console.log('minimizeWindow available:', !!window.electronAPI?.minimizeWindow)

  if (window.electronAPI?.minimizeWindow) {
    try {
      await window.electronAPI.minimizeWindow()
      console.log('✅ 最小化成功')
    } catch (error) {
      console.error('❌ 最小化失败:', error)
    }
  } else {
    console.warn('⚠️ Electron API 不可用，无法最小化窗口')
  }
}

// 最大化/还原窗口
const handleMaximize = async () => {
  console.log('🔼 最大化/还原窗口')
  console.log('electronAPI available:', !!window.electronAPI)
  console.log('maximizeWindow available:', !!window.electronAPI?.maximizeWindow)

  if (window.electronAPI?.maximizeWindow) {
    try {
      // 执行最大化/还原操作
      const result = await window.electronAPI.maximizeWindow()
      console.log('✅ 最大化/还原成功', result)

      // 立即更新状态
      if (window.electronAPI?.isMaximized) {
        const newState = await window.electronAPI.isMaximized()
        isMaximized.value = newState
        console.log('📏 窗口状态:', newState ? '最大化' : '正常')
      }
    } catch (error) {
      console.error('❌ 最大化/还原失败:', error)
    }
  } else {
    console.warn('⚠️ Electron API 不可用，无法最大化/还原窗口')
  }
}

// 关闭窗口
const handleClose = async () => {
  console.log('❌ 关闭窗口')
  console.log('electronAPI available:', !!window.electronAPI)
  console.log('closeWindow available:', !!window.electronAPI?.closeWindow)

  if (window.electronAPI?.closeWindow) {
    try {
      await window.electronAPI.closeWindow()
      console.log('✅ 关闭成功')
    } catch (error) {
      console.error('❌ 关闭失败:', error)
    }
  } else {
    console.warn('⚠️ Electron API 不可用，尝试关闭浏览器标签页')
    // 如果不是Electron环境，关闭当前标签页
    window.close()
  }
}
</script>

<style scoped>
.window-controls {
  display: flex;
  gap: 12px;
  align-items: center;
  padding: 4px;
  flex-shrink: 0;
  -webkit-app-region: no-drag; /* 确保窗口控制按钮不会被拖拽功能影响 */
}

.cartoon-droplet {
  width: 40px;
  height: 20px;
  border-radius: 10px;
  border: 2px solid #2f3542;
  box-shadow: 3px 3px 0px #2f3542;
  position: relative;
  cursor: pointer;
  transition: transform 0.2s cubic-bezier(.4,1.4,.6,1), box-shadow 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
}

/* 水滴高光效果 */
.cartoon-droplet::before {
  content: '';
  position: absolute;
  top: 3px;
  left: 8px;
  width: 12px;
  height: 6px;
  border-radius: 6px;
  background-color: rgba(255,255,255,0.7);
  transform: rotate(15deg);
  pointer-events: none;
}

/* 颜色主题 */
.cartoon-green { 
  background-color: #4cd137;
}

.cartoon-yellow { 
  background-color: #fbc531;
}

.cartoon-red { 
  background-color: #e84118;
}

/* 弹出动画 */
.cartoon-droplet:hover {
  transform: scale(1.15);
  z-index: 2;
}

.cartoon-droplet:active {
  transform: scale(1.05);
  box-shadow: 1px 1px 0px #2f3542;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .window-controls {
    gap: 8px;
  }

  .cartoon-droplet {
    width: 32px;
    height: 16px;
    border-radius: 8px;
  }

  .cartoon-droplet::before {
    top: 2px;
    left: 6px;
    width: 8px;
    height: 4px;
    border-radius: 4px;
  }

  .control-icon {
    width: 10px;
    height: 10px;
  }
}
</style>
