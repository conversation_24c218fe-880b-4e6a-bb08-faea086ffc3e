<template>
  <FormCard title="高级选项">
    <div class="form-fields">
      <div class="form-field">
        <label class="field-label">
          <input 
            type="checkbox" 
            :checked="showBrowser" 
            @change="updateShowBrowser"
            class="field-checkbox"
          />
          显示浏览器窗口
        </label>
      </div>
      
      <div class="form-field">
        <label class="field-label">超时时间 (秒)</label>
        <input 
          class="field-input" 
          type="number" 
          :value="timeout" 
          @input="updateTimeout"
          placeholder="300" 
          min="30"
          max="3600"
        />
      </div>
      
      <div class="form-field">
        <label class="field-label">重试次数</label>
        <input 
          class="field-input" 
          type="number" 
          :value="retryCount" 
          @input="updateRetryCount"
          placeholder="3" 
          min="1"
          max="10"
        />
      </div>
    </div>
  </FormCard>
</template>

<script setup lang="ts">
import FormCard from '../Common/FormCard.vue'

// Props定义
interface Props {
  showBrowser: boolean
  timeout: number
  retryCount: number
}

// Emits定义
interface Emits {
  (e: 'update:showBrowser', value: boolean): void
  (e: 'update:timeout', value: number): void
  (e: 'update:retryCount', value: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 更新方法
const updateShowBrowser = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('update:showBrowser', target.checked)
}

const updateTimeout = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('update:timeout', parseInt(target.value) || 300)
}

const updateRetryCount = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('update:retryCount', parseInt(target.value) || 3)
}
</script>

<style scoped>
.form-fields {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field-label {
  color: #006aaaff;
  font-weight: bold;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  font-size: 1.15em;
  text-shadow: 0em 0.1em 0em var(--color2);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.field-checkbox {
  width: 1.2em;
  height: 1.2em;
  accent-color: #006aaaff;
  cursor: pointer;
}

.field-input {
  background-color: white;
  border: none;
  border-right: 5px solid #ff7c90ff;
  border-bottom: 5px solid #ff7c90ff;
  border-bottom-right-radius: 25em;
  height: 2em;
  width: 90%;
  transition: all 0.5s ease;
  color: #ff7c90ff;
  font-weight: bold;
  padding-left: 0.5em;
  font-size: 1.05em;
  box-shadow: 1em 0em 0em;
  outline: none;
}

.field-input:hover {
  width: 100%;
  background-color: white;
  box-shadow: 0em 0em 0em;
}

.field-input:focus {
  width: 100%;
  background-color: white;
  box-shadow: 0em 0em 0em;
  border-color: #006aaaff;
}
</style>
