#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor Pro - 主入口文件
包含翻译系统和主程序逻辑
"""

import os
import sys
import json
import locale
from pathlib import Path
from colorama import Fore, Style, init

# 初始化colorama
init()

class Translator:
    """翻译器类 - 支持多语言国际化"""
    
    def __init__(self, language=None):
        self.current_language = language or self._detect_system_language()
        self.fallback_language = 'zh_cn'  # 改为中文作为备用语言
        self.translations = {}
        self.locales_dir = Path(__file__).parent.parent.parent.parent / 'locales'

        # 加载翻译文件
        self._load_translations()
    
    def _detect_system_language(self):
        """检测系统语言 - 简化版，只支持中文"""
        try:
            # 获取系统语言
            system_locale = locale.getdefaultlocale()[0]
            if system_locale:
                # 只检测中文变体
                if system_locale.startswith('zh_TW') or system_locale.startswith('zh-TW'):
                    return 'zh_tw'
                elif system_locale.startswith('zh'):
                    return 'zh_cn'
        except:
            pass

        # 默认返回简体中文
        return 'zh_cn'
    
    def _load_translations(self):
        """加载翻译文件"""
        try:
            # 加载当前语言
            current_file = self.locales_dir / f'{self.current_language}.json'
            if current_file.exists():
                with open(current_file, 'r', encoding='utf-8') as f:
                    self.translations[self.current_language] = json.load(f)
            
            # 加载备用语言
            fallback_file = self.locales_dir / f'{self.fallback_language}.json'
            if fallback_file.exists() and self.fallback_language != self.current_language:
                with open(fallback_file, 'r', encoding='utf-8') as f:
                    self.translations[self.fallback_language] = json.load(f)
                    
        except Exception as e:
            print(f"Warning: Failed to load translations: {e}")
            self.translations = {}
    
    def get(self, key, **kwargs):
        """获取翻译文本"""
        try:
            # 尝试从当前语言获取
            if self.current_language in self.translations:
                text = self._get_nested_value(self.translations[self.current_language], key)
                if text:
                    return self._format_text(text, **kwargs)
            
            # 尝试从备用语言获取
            if self.fallback_language in self.translations:
                text = self._get_nested_value(self.translations[self.fallback_language], key)
                if text:
                    return self._format_text(text, **kwargs)
            
            # 如果都没找到，返回key本身
            return self._format_text(key, **kwargs)
            
        except Exception:
            # 出错时返回key
            return key
    
    def _get_nested_value(self, data, key):
        """获取嵌套字典中的值"""
        try:
            keys = key.split('.')
            value = data
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return None
            return value
        except:
            return None
    
    def _format_text(self, text, **kwargs):
        """格式化文本，替换占位符"""
        try:
            if kwargs and isinstance(text, str):
                return text.format(**kwargs)
            return text
        except:
            return text
    
    def set_language(self, language):
        """设置语言"""
        if language != self.current_language:
            self.current_language = language
            self._load_translations()
    
    def get_available_languages(self):
        """获取可用语言列表"""
        languages = []
        if self.locales_dir.exists():
            for file in self.locales_dir.glob('*.json'):
                lang_code = file.stem
                languages.append(lang_code)
        return sorted(languages)

# 创建全局翻译器实例
translator = Translator()

def main():
    """主函数 - 程序入口点"""
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}🚀 Cursor Pro - 主程序{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print()
    
    print(f"{Fore.GREEN}✅ 翻译系统已初始化{Style.RESET_ALL}")
    print(f"{Fore.CYAN}📍 当前语言: {translator.current_language}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}📁 可用语言: {', '.join(translator.get_available_languages())}{Style.RESET_ALL}")
    print()
    
    # 测试翻译功能
    test_key = "menu.title"
    translated = translator.get(test_key)
    print(f"{Fore.YELLOW}🧪 翻译测试: {test_key} -> {translated}{Style.RESET_ALL}")
    print()
    
    print(f"{Fore.GREEN}✅ main.py 已成功加载！{Style.RESET_ALL}")
    print(f"{Fore.CYAN}💡 现在其他模块可以正常导入 translator 了{Style.RESET_ALL}")
    print()
    
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}🎯 请使用 start.bat 启动完整应用{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
