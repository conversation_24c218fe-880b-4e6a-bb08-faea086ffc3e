<template>
  <div class="app-container">
    <Sidebar
      :collapsed="sidebarCollapsed"
      :current-view="currentView"
      @view-change="handleViewChange"
      @toggle-collapse="toggleSidebar"
    />
    <MainContent
      :current-view="currentView"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAppStore } from '@/stores/app'
import Sidebar from './Sidebar.vue'
import MainContent from './MainContent.vue'

const appStore = useAppStore()

// 计算属性
const currentView = computed(() => appStore.currentView)
const sidebarCollapsed = computed(() => appStore.sidebarCollapsed)

// 方法
const handleViewChange = (view: string) => {
  appStore.setCurrentView(view)
}

const toggleSidebar = () => {
  appStore.toggleSidebar()
}
</script>

<style scoped>
.app-container {
  display: flex;
  height: 100vh;
  width: 100vw;
  background: #181c20;
  overflow: hidden;
}
</style>
