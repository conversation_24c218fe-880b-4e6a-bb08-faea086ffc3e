<template>
  <div class="backup-manager">
    <!-- 备份管理标题 -->
    <div class="backup-header">
      <h2 class="backup-title">
        <svg class="backup-icon" viewBox="0 0 24 24">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
          <polyline points="14,2 14,8 20,8"></polyline>
          <line x1="16" y1="13" x2="8" y2="13"></line>
          <line x1="16" y1="17" x2="8" y2="17"></line>
          <polyline points="10,9 9,9 8,9"></polyline>
        </svg>
        备份管理
      </h2>
      <div class="backup-actions">
        <button class="action-btn create-btn" @click="showCreateBackupDialog = true">
          <svg viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="8" x2="12" y2="16"></line>
            <line x1="8" y1="12" x2="16" y2="12"></line>
          </svg>
          创建备份
        </button>
        <button class="action-btn refresh-btn" @click="refreshBackups" :disabled="loading">
          <svg viewBox="0 0 24 24" :class="{ spinning: loading }">
            <polyline points="23 4 23 10 17 10"></polyline>
            <polyline points="1 20 1 14 7 14"></polyline>
            <path d="m3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
          </svg>
          刷新
        </button>
      </div>
    </div>

    <!-- 备份类型标签 -->
    <div class="backup-tabs">
      <button 
        v-for="tab in tabs" 
        :key="tab.type"
        class="tab-btn"
        :class="{ active: activeTab === tab.type }"
        @click="activeTab = tab.type"
      >
        <component :is="tab.icon" class="tab-icon" />
        {{ tab.label }}
        <span class="tab-count">{{ getBackupCount(tab.type) }}</span>
      </button>
    </div>

    <!-- 备份列表 -->
    <div class="backup-content">
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>正在加载备份列表...</p>
      </div>

      <div v-else-if="filteredBackups.length === 0" class="empty-state">
        <svg class="empty-icon" viewBox="0 0 24 24">
          <circle cx="12" cy="12" r="10"></circle>
          <path d="m9 9 6 6"></path>
          <path d="m15 9-6 6"></path>
        </svg>
        <h3>暂无{{ getTabLabel(activeTab) }}备份</h3>
        <p>点击"创建备份"按钮开始备份您的数据</p>
      </div>

      <div v-else class="backup-list">
        <div 
          v-for="backup in filteredBackups" 
          :key="backup.id"
          class="backup-item"
        >
          <div class="backup-info">
            <div class="backup-header-info">
              <h4 class="backup-name">{{ backup.name }}</h4>
              <span class="backup-type-badge" :class="backup.type">
                {{ getTypeLabel(backup.type) }}
              </span>
            </div>
            <p class="backup-description">{{ backup.description || '无描述' }}</p>
            <div class="backup-meta">
              <span class="backup-date">{{ formatDate(backup.createdAt) }}</span>
              <span class="backup-size">{{ formatSize(backup.size) }}</span>
            </div>
          </div>
          <div class="backup-actions">
            <button 
              class="action-btn restore-btn" 
              @click="restoreBackup(backup)"
              title="恢复备份"
            >
              <svg viewBox="0 0 24 24">
                <polyline points="1 4 1 10 7 10"></polyline>
                <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"></path>
              </svg>
              恢复
            </button>
            <button 
              class="action-btn delete-btn" 
              @click="deleteBackup(backup)"
              title="删除备份"
            >
              <svg viewBox="0 0 24 24">
                <polyline points="3,6 5,6 21,6"></polyline>
                <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
              </svg>
              删除
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建备份对话框 -->
    <BaseModal
      :visible="showCreateBackupDialog"
      title="创建新备份"
      width="500px"
      @close="showCreateBackupDialog = false"
    >
      <div class="create-backup-form">
        <div class="form-group">
          <label>备份类型</label>
          <select v-model="newBackup.type" class="form-select">
            <option value="settings">设置备份</option>
            <option value="session">会话备份</option>
            <option value="full">完整备份</option>
          </select>
        </div>
        <div class="form-group">
          <label>备份名称</label>
          <input
            v-model="newBackup.name"
            type="text"
            class="form-input"
            placeholder="输入备份名称"
          />
        </div>
        <div class="form-group">
          <label>描述（可选）</label>
          <textarea
            v-model="newBackup.description"
            class="form-textarea"
            placeholder="输入备份描述"
            rows="3"
          ></textarea>
        </div>
      </div>

      <template #footer>
        <button class="card__button card__button--cancel" @click="showCreateBackupDialog = false">取消</button>
        <button class="card__button card__button--confirm" @click="createBackup" :disabled="!newBackup.name">
          创建备份
        </button>
      </template>
    </BaseModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { realAPI } from '@/api/real-api'
import { useLogStore } from '@/stores/logs'
import { useAppStore } from '@/stores/app'
import BaseModal from '@/components/Modal/BaseModal.vue'

// 图标组件
const SettingsIcon = () => h('svg', { viewBox: '0 0 24 24', stroke: 'currentColor', fill: 'none', 'stroke-width': '2' }, [
  h('circle', { cx: '12', cy: '12', r: '3' }),
  h('path', { d: 'M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z' })
])

const SessionIcon = () => h('svg', { viewBox: '0 0 24 24', stroke: 'currentColor', fill: 'none', 'stroke-width': '2' }, [
  h('path', { d: 'M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z' })
])

const MachineIdIcon = () => h('svg', { viewBox: '0 0 24 24', stroke: 'currentColor', fill: 'none', 'stroke-width': '2' }, [
  h('rect', { x: '2', y: '3', width: '20', height: '14', rx: '2', ry: '2' }),
  h('line', { x1: '8', y1: '21', x2: '16', y2: '21' }),
  h('line', { x1: '12', y1: '17', x2: '12', y2: '21' }),
  h('circle', { cx: '12', cy: '10', r: '2' })
])

const AllIcon = () => h('svg', { viewBox: '0 0 24 24', stroke: 'currentColor', fill: 'none', 'stroke-width': '2' }, [
  h('path', { d: 'M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z' }),
  h('polyline', { points: '14,2 14,8 20,8' })
])

const logStore = useLogStore()
const appStore = useAppStore()

// 响应式数据
const loading = ref(false)
const activeTab = ref<'all' | 'settings' | 'session' | 'machine_id'>('all')
const backups = ref<any[]>([])
const showCreateBackupDialog = ref(false)

// 新备份表单
const newBackup = ref({
  type: 'settings',
  name: '',
  description: ''
})

// 标签配置
const tabs = [
  { type: 'all', label: '全部', icon: AllIcon },
  { type: 'settings', label: '设置', icon: SettingsIcon },
  { type: 'session', label: '会话', icon: SessionIcon },
  { type: 'machine_id', label: '机器ID', icon: MachineIdIcon }
]

// 计算属性
const filteredBackups = computed(() => {
  if (activeTab.value === 'all') {
    return backups.value
  }
  return backups.value.filter(backup => backup.type === activeTab.value)
})

// 方法
const getBackupCount = (type: string) => {
  if (type === 'all') return backups.value.length
  return backups.value.filter(backup => backup.type === type).length
}

const getTabLabel = (type: string) => {
  const tab = tabs.find(t => t.type === type)
  return tab ? tab.label : '未知'
}

const getTypeLabel = (type: string) => {
  const labels = {
    settings: '设置',
    session: '会话',
    machine_id: '机器ID',
    full: '完整'
  }
  return labels[type] || type
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const formatSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// API 方法
const refreshBackups = async () => {
  loading.value = true
  try {
    // 获取真实的备份列表
    const settingsBackups = await realAPI.listSettingsBackups()
    const sessionBackups = await realAPI.listSessionBackups()
    const machineIdBackups = await realAPI.listMachineIdBackups()

    // 合并并排序备份列表
    backups.value = [
      ...settingsBackups.map(b => ({ ...b, type: 'settings' })),
      ...sessionBackups.map(b => ({ ...b, type: 'session' })),
      ...machineIdBackups.map(b => ({ ...b, type: 'machine_id' }))
    ].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())

    logStore.addSuccess(`刷新备份列表成功，共 ${backups.value.length} 个备份`, 'BackupManager')
  } catch (error) {
    logStore.addError(`刷新备份列表失败: ${(error as Error).message}`, 'BackupManager')
    appStore.showError('刷新备份列表失败')
  } finally {
    loading.value = false
  }
}

const createBackup = async () => {
  try {
    let result
    if (newBackup.value.type === 'settings') {
      result = await realAPI.backupSettings(newBackup.value.name)
    } else if (newBackup.value.type === 'session') {
      result = await realAPI.backupSession(newBackup.value.name)
    } else if (newBackup.value.type === 'machine_id') {
      // 机器ID备份通过重置时自动创建，这里可以提示用户
      appStore.showInfo('机器ID备份会在重置机器ID时自动创建')
      showCreateBackupDialog.value = false
      return
    }

    appStore.showSuccess('备份创建成功')
    logStore.addSuccess(`创建${getTypeLabel(newBackup.value.type)}备份成功: ${newBackup.value.name}`, 'BackupManager')

    // 重置表单
    newBackup.value = { type: 'settings', name: '', description: '' }
    showCreateBackupDialog.value = false

    // 刷新列表
    await refreshBackups()
  } catch (error) {
    logStore.addError(`创建备份失败: ${(error as Error).message}`, 'BackupManager')
    appStore.showError('创建备份失败')
  }
}

const restoreBackup = async (backup: any) => {
  appStore.showConfirm(
    '恢复备份',
    `确定要恢复备份"${backup.name}"吗？\n\n⚠️ 这将覆盖当前的${getTypeLabel(backup.type)}数据`,
    async () => {
      try {
        let result
        if (backup.type === 'settings') {
          result = await realAPI.restoreSettings(backup.name)
        } else if (backup.type === 'session') {
          result = await realAPI.restoreSession(backup.name)
        } else if (backup.type === 'machine_id') {
          // 机器ID备份使用id字段（文件名）而不是name字段（显示名称）
          result = await realAPI.restoreMachineId(backup.id)
        }

        appStore.showSuccess('备份恢复成功')
        logStore.addSuccess(`恢复${getTypeLabel(backup.type)}备份成功: ${backup.name}`, 'BackupManager')
      } catch (error) {
        logStore.addError(`恢复备份失败: ${(error as Error).message}`, 'BackupManager')
        appStore.showError('恢复备份失败')
      }
    }
  )
}

const deleteBackup = async (backup: any) => {
  appStore.showConfirm(
    '删除备份',
    `确定要删除备份"${backup.name}"吗？\n\n⚠️ 此操作不可撤销`,
    async () => {
      try {
        // 根据备份类型调用不同的删除API
        if (backup.type === 'settings') {
          await realAPI.deleteSettingsBackup(backup.name)
        } else if (backup.type === 'session') {
          await realAPI.deleteSessionBackup(backup.name)
        } else if (backup.type === 'machine_id') {
          // 机器ID备份使用id字段（文件名）而不是name字段（显示名称）
          await realAPI.deleteMachineIdBackup(backup.id)
        } else {
          // 通用删除方法
          await realAPI.deleteBackup(backup.name)
        }

        appStore.showSuccess('备份删除成功')
        logStore.addSuccess(`删除${getTypeLabel(backup.type)}备份成功: ${backup.name}`, 'BackupManager')
        await refreshBackups()
      } catch (error) {
        logStore.addError(`删除备份失败: ${(error as Error).message}`, 'BackupManager')
        appStore.showError('删除备份失败')
      }
    }
  )
}

// 组件挂载时加载数据
onMounted(() => {
  refreshBackups()
})
</script>

<style scoped>
.backup-manager {
  padding: 24px;
  background: #1a1a2e;
  border-radius: 12px;
  color: #e2e8f0;
  height: 600px;
  max-height: 600px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.backup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.backup-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.5rem;
  font-weight: 600;
  color: #60a5fa;
  margin: 0;
}

.backup-icon {
  width: 24px;
  height: 24px;
  fill: currentColor;
}

.backup-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn svg {
  width: 16px;
  height: 16px;
  stroke: currentColor;
  fill: none;
  stroke-width: 2;
}

.create-btn {
  background: #10b981;
  color: white;
}

.create-btn:hover {
  background: #059669;
  transform: translateY(-1px);
}

.refresh-btn {
  background: #3b82f6;
  color: white;
}

.refresh-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.backup-tabs {
  display: flex;
  gap: 4px;
  margin-bottom: 24px;
  background: #16213e;
  padding: 4px;
  border-radius: 8px;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  background: transparent;
  color: #94a3b8;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  justify-content: center;
}

.tab-btn.active {
  background: #60a5fa;
  color: white;
}

.tab-btn:hover:not(.active) {
  background: #1e293b;
  color: #e2e8f0;
}

.tab-icon {
  width: 16px;
  height: 16px;
  stroke: currentColor;
  fill: none;
  stroke-width: 2;
}

.tab-count {
  background: rgba(255, 255, 255, 0.2);
  color: currentColor;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

.backup-content {
  flex: 1;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #4a5568 #2d3748;
}

.backup-content::-webkit-scrollbar {
  width: 8px;
}

.backup-content::-webkit-scrollbar-track {
  background: #2d3748;
  border-radius: 4px;
}

.backup-content::-webkit-scrollbar-thumb {
  background: #4a5568;
  border-radius: 4px;
}

.backup-content::-webkit-scrollbar-thumb:hover {
  background: #718096;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  gap: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #374151;
  border-top: 3px solid #60a5fa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  gap: 16px;
  color: #94a3b8;
}

.empty-icon {
  width: 64px;
  height: 64px;
  stroke: currentColor;
  fill: none;
  stroke-width: 1.5;
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

.backup-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.backup-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #16213e;
  border-radius: 12px;
  border: 1px solid #374151;
  transition: all 0.2s ease;
}

.backup-item:hover {
  border-color: #60a5fa;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(96, 165, 250, 0.1);
}

.backup-info {
  flex: 1;
}

.backup-header-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.backup-name {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #e2e8f0;
}

.backup-type-badge {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.backup-type-badge.settings {
  background: #059669;
  color: white;
}

.backup-type-badge.session {
  background: #7c3aed;
  color: white;
}

.backup-type-badge.full {
  background: #dc2626;
  color: white;
}

.backup-description {
  margin: 0 0 8px 0;
  color: #94a3b8;
  font-size: 14px;
}

.backup-meta {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: #6b7280;
}

.backup-actions {
  display: flex;
  gap: 8px;
}

.restore-btn {
  background: #059669;
  color: white;
}

.restore-btn:hover {
  background: #047857;
}

.delete-btn {
  background: #dc2626;
  color: white;
}

.delete-btn:hover {
  background: #b91c1c;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #1a1a2e;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  border: 1px solid #374151;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #374151;
}

.modal-header h3 {
  margin: 0;
  color: #e2e8f0;
  font-size: 1.2rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: #94a3b8;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #374151;
  color: #e2e8f0;
}

.close-btn svg {
  width: 20px;
  height: 20px;
  stroke: currentColor;
  stroke-width: 2;
}

.modal-body {
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #e2e8f0;
  font-weight: 500;
  font-size: 14px;
}

.form-select,
.form-input,
.form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #374151;
  border-radius: 8px;
  background: #16213e;
  color: #e2e8f0;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-select:focus,
.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #60a5fa;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #374151;
}

.btn-cancel,
.btn-confirm {
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-cancel {
  background: #374151;
  color: #e2e8f0;
}

.btn-cancel:hover {
  background: #4b5563;
}

.btn-confirm {
  background: #10b981;
  color: white;
}

.btn-confirm:hover:not(:disabled) {
  background: #059669;
}

.btn-confirm:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 创建备份表单样式 */
.create-backup-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.create-backup-form .form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.create-backup-form label {
  font-weight: 600;
  color: #333;
  font-size: 0.9em;
}

.create-backup-form .form-select,
.create-backup-form .form-input,
.create-backup-form .form-textarea {
  padding: 8px 12px;
  border: 2px solid #e0e0e0;
  border-radius: 6px;
  font-size: 0.9em;
  transition: border-color 0.2s ease;
}

.create-backup-form .form-select:focus,
.create-backup-form .form-input:focus,
.create-backup-form .form-textarea:focus {
  outline: none;
  border-color: #007bff;
}

.create-backup-form .form-textarea {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}
</style>
