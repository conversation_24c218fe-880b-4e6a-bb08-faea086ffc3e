<template>
  <div class="features">
    <!-- 操作面板 -->
    <OperationPanel @operation="handleOperation" />

    <!-- 工作流程进度弹窗 -->
    <WorkflowProgress
      ref="workflowProgressRef"
      :visible="showWorkflowProgress"
      :can-cancel="workflowCanCancel"
      :selected-steps="currentSelectedSteps"
      @cancel="cancelWorkflow"
      @close="closeWorkflowProgress"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useLogStore } from '@/stores/logs'
import { useAppStore } from '@/stores/app'
import { realAPI } from '@/api/real-api'
import OperationPanel from '@/components/Operation/OperationPanel.vue'
import WorkflowProgress from '@/components/Progress/WorkflowProgress.vue'

// 定义组件名称（用于keep-alive缓存）
defineOptions({
  name: 'Features'
})

const logStore = useLogStore()
const appStore = useAppStore()

// 工作流程进度相关
const showWorkflowProgress = ref(false)
const workflowCanCancel = ref(true)
const workflowProgressRef = ref<InstanceType<typeof WorkflowProgress> | null>(null)
const workflowCancelled = ref(false)
const workflowAbortController = ref<AbortController | null>(null)
const currentSelectedSteps = ref<string[]>([])

// 监听弹窗状态变化，用于调试
watch(showWorkflowProgress, (newValue, oldValue) => {
  console.log(`🔍 弹窗状态变化: ${oldValue} -> ${newValue}`)
  if (newValue === false && oldValue === true) {
    console.trace('🚨 弹窗被关闭，调用栈:')
  }
})

// 处理操作
const handleOperation = async (type: string) => {
  logStore.addInfo(`执行操作: ${type}`, 'Features')

  try {
    switch (type) {
      case 'auto-workflow':
        await handleAutoWorkflow()
        break
      case 'auto-register':
        await handleAutoRegister()
        break
      case 'get-random-config':
        await handleGetRandomConfig()
        break
      case 'get-verification-code':
        await handleGetVerificationCode()
        break

      case 'show-config':
        await handleShowConfig()
        break
      case 'init-cursor':
        await handleInitCursor()
        break
      case 'reset-machine-code':
        await handleResetMachineCode()
        break
      case 'bypass-version':
        await handleBypassVersion()
        break



      case 'auto-update':
        await handleAutoUpdate()
        break

      default:
        logStore.addWarning(`未知操作类型: ${type}`, 'Features')
    }
  } catch (error) {
    logStore.addError(`操作失败: ${(error as Error).message}`, 'Features')
  }
}

// 一键完整流程
const handleAutoWorkflow = async () => {
  appStore.showConfirm(
    '🚀 一键完整流程',
    '确定要执行完整的自动化流程吗？\n\n📋 执行步骤：\n• 保存原账户信息\n• 备份设置和会话\n• 重置机器码\n• 自动注册新账户\n• 初始化Cursor配置\n• 恢复设置和会话\n• 重启Cursor\n\n⚠️ 此操作将重置当前配置\n✅ 所有功能均为真实执行',
    async (selectedSteps?: string[]) => {
      // 保存选择的步骤
      currentSelectedSteps.value = selectedSteps || []
      console.log('🔍 用户选择的步骤:', selectedSteps)
      console.log('🔍 保存到currentSelectedSteps:', currentSelectedSteps.value)

      // 显示进度弹窗
      console.log('🚀 显示工作流程进度弹窗')
      showWorkflowProgress.value = true
      workflowCanCancel.value = true
      workflowCancelled.value = false
      workflowAbortController.value = new AbortController()

      // 等待Vue响应式更新完成，确保WorkflowProgress组件接收到最新的selectedSteps
      await new Promise(resolve => setTimeout(resolve, 0))
      console.log('🔍 调用reset前，currentSelectedSteps:', currentSelectedSteps.value)
      workflowProgressRef.value?.reset()

      try {
        await executeWorkflowWithProgress(selectedSteps)

        // 流程完成后，显示完成状态
        workflowCanCancel.value = false
        workflowProgressRef.value?.addLog('success', '🎉 一键完整流程执行完成！')
        logStore.addSuccess('🎉 一键完整流程执行完成！', 'AutoWorkflow')

        // 显示成功通知
        appStore.showSuccess('🎉 一键完整流程已成功执行完成！')

      } catch (error) {
        const errorMsg = (error as Error).message
        logStore.addError(`一键完整流程失败: ${errorMsg}`, 'AutoWorkflow')
        workflowProgressRef.value?.addLog('error', `流程失败: ${errorMsg}`)

        // 即使失败也保持弹窗显示，让用户手动关闭
        workflowCanCancel.value = false
        workflowProgressRef.value?.addLog('warning', '请手动关闭弹窗或查看错误详情')
      }
    }
  )
}

// 执行带进度的工作流程
const executeWorkflowWithProgress = async (selectedSteps?: string[]) => {
  const allSteps = [
    { name: 'saveAccount', title: '📦 保存原账户', api: () => realAPI.saveAccount() },
    { name: 'backupSettings', title: '⚙️ 备份设置', api: () => realAPI.backupSettings() },
    { name: 'backupSession', title: '💬 备份会话', api: () => realAPI.backupSession() },
    { name: 'resetMachine', title: '🔄 重置机器码', api: () => realAPI.resetMachineId() },
    { name: 'autoRegister', title: '📝 自动注册', api: () => realAPI.autoRegister() },
    { name: 'initCursor', title: '🧹 初始化Cursor', api: () => realAPI.initCursor() },
    { name: 'restoreSettings', title: '⚙️ 恢复设置', api: () => realAPI.restoreSettings() },
    { name: 'restoreSession', title: '💬 恢复会话', api: () => realAPI.restoreSession() },
    { name: 'restartCursor', title: '🔄 重启Cursor', api: () => realAPI.restartCursor() }
  ]

  // 如果提供了选中的步骤，只执行选中的步骤
  const steps = selectedSteps && selectedSteps.length > 0
    ? allSteps.filter(step => selectedSteps.includes(step.name))
    : allSteps

  logStore.addInfo(`开始执行工作流程，共 ${steps.length} 个步骤`, 'AutoWorkflow')

  for (let i = 0; i < steps.length; i++) {
    // 检查是否被取消
    if (workflowCancelled.value) {
      console.log('🛑 工作流程被用户取消')
      workflowProgressRef.value?.addLog('warning', '工作流程已被用户取消')
      break
    }

    const step = steps[i]

    try {
      // 开始步骤
      workflowProgressRef.value?.startStep(i, `正在${step.title}...`)
      logStore.addInfo(`步骤${i + 1}: ${step.title}...`, 'AutoWorkflow')
      console.log(`🚀 开始执行步骤 ${i + 1}: ${step.title}`)

      // 模拟进度更新（某些步骤可能需要时间）
      if (step.name === 'autoRegister') {
        // 注册步骤显示详细进度
        workflowProgressRef.value?.updateStepProgress(i, 20, '生成临时邮箱...')
        await new Promise(resolve => setTimeout(resolve, 1000))

        workflowProgressRef.value?.updateStepProgress(i, 40, '提交注册信息...')
        await new Promise(resolve => setTimeout(resolve, 1000))

        workflowProgressRef.value?.updateStepProgress(i, 60, '等待验证邮件...')
        await new Promise(resolve => setTimeout(resolve, 1000))

        workflowProgressRef.value?.updateStepProgress(i, 80, '验证邮箱...')
      }

      // 再次检查是否被取消（在API调用前）
      if (workflowCancelled.value) {
        console.log('🛑 API调用前检测到取消信号')
        throw new Error('工作流程已被用户取消')
      }

      // 执行API调用
      console.log(`📡 调用API: ${step.name}`)
      const result = await step.api()
      console.log(`✅ API调用成功: ${step.name}`, result)

      // API调用后再次检查是否被取消
      if (workflowCancelled.value) {
        console.log('🛑 API调用后检测到取消信号')
        throw new Error('工作流程已被用户取消')
      }

      // 完成步骤
      workflowProgressRef.value?.completeStep(i, `${step.title}完成`)
      logStore.addSuccess(`步骤${i + 1}完成: ${result}`, 'AutoWorkflow')

      // 让用户看到"已完成"状态1.5秒
      await new Promise(resolve => setTimeout(resolve, 1500))

    } catch (error) {
      const errorMsg = (error as Error).message
      console.error(`❌ 步骤${i + 1}失败:`, error)
      workflowProgressRef.value?.errorStep(i, errorMsg)
      logStore.addError(`步骤${i + 1}失败: ${errorMsg}`, 'AutoWorkflow')

      // 检查是否是网络连接问题
      if (errorMsg.includes('fetch') || errorMsg.includes('Failed to fetch') || errorMsg.includes('NetworkError')) {
        workflowProgressRef.value?.addLog('error', '❌ 网络连接失败，请检查后端服务是否启动 (http://localhost:8080)')
      }

      // 判断是否是关键步骤，关键步骤失败则停止整个流程
      const criticalSteps = ['autoRegister', 'resetMachine'] // 关键步骤列表
      if (criticalSteps.includes(step.name)) {
        workflowProgressRef.value?.addLog('error', `❌ 关键步骤"${step.title}"失败，停止执行后续步骤`)
        logStore.addError('🛑 工作流程因关键步骤失败而终止', 'AutoWorkflow')
        break // 停止执行后续步骤
      } else {
        // 非关键步骤失败，继续执行
        workflowProgressRef.value?.addLog('warning', `⚠️ 步骤"${step.title}"失败，但继续执行后续步骤...`)
      }
    }
  }

  // 所有步骤执行完成（无论成功失败）
  console.log('🏁 工作流程执行完成')
}

// 取消工作流程
const cancelWorkflow = () => {
  console.log('🚨 用户取消工作流程')

  // 设置取消标志
  workflowCancelled.value = true

  // 中止正在进行的API请求
  if (workflowAbortController.value) {
    workflowAbortController.value.abort()
    workflowAbortController.value = null
  }

  workflowCanCancel.value = false
  showWorkflowProgress.value = false
  workflowProgressRef.value?.addLog('warning', '用户取消了工作流程')
  logStore.addWarning('用户取消了一键完整流程', 'AutoWorkflow')
}

// 关闭进度弹窗
const closeWorkflowProgress = () => {
  console.log('🚨 关闭进度弹窗')
  showWorkflowProgress.value = false
}


// 自动注册
const handleAutoRegister = async () => {
  appStore.showConfirm(
    '📝 一键自动注册',
    '确定要使用临时邮箱进行自动注册吗？\n\n🔧 注册流程：\n• 生成临时邮箱地址\n• 自动填写注册信息\n• 提交注册申请\n• 等待邮箱验证\n\n💡 请确保已在设置中配置好注册参数',
    async () => {
      try {
        logStore.addInfo('开始自动注册...', 'AutoRegister')
        const result = await realAPI.autoRegister()

        // 检查返回结果
        if (typeof result === 'object' && result !== null) {
          if (result.success === true) {
            // 如果响应中包含日志信息，先记录这些日志
            if (result.logs && Array.isArray(result.logs)) {
              result.logs.forEach((logMsg: string) => {
                if (logMsg.includes('❌')) {
                  logStore.addError(logMsg, 'AutoRegister')
                } else if (logMsg.includes('✅') || logMsg.includes('🎉')) {
                  logStore.addSuccess(logMsg, 'AutoRegister')
                } else if (logMsg.includes('⚠️')) {
                  logStore.addWarning(logMsg, 'AutoRegister')
                } else {
                  logStore.addInfo(logMsg, 'AutoRegister')
                }
              })
            } else {
              // 兼容旧版本
              logStore.addSuccess(`自动注册完成: ${result.message || '注册成功'}`, 'AutoRegister')
            }

            let successMsg = `🎉 自动注册成功！\n\n✅ 新账户已创建并激活`
            if (result.email) {
              successMsg += `\n📧 邮箱: ${result.email}`
            }
            if (result.save_warning) {
              successMsg += `\n⚠️ ${result.message}`
            }
            successMsg += `\n💡 可以开始使用Cursor了`

            appStore.showSuccess(successMsg)
          } else {
            // 如果失败响应中包含日志信息，先记录这些日志
            if (result.logs && Array.isArray(result.logs)) {
              result.logs.forEach((logMsg: string) => {
                if (logMsg.includes('❌')) {
                  logStore.addError(logMsg, 'AutoRegister')
                } else if (logMsg.includes('✅')) {
                  logStore.addSuccess(logMsg, 'AutoRegister')
                } else if (logMsg.includes('⚠️')) {
                  logStore.addWarning(logMsg, 'AutoRegister')
                } else {
                  logStore.addInfo(logMsg, 'AutoRegister')
                }
              })
            }
            throw new Error(result.error || '注册失败，未知错误')
          }
        } else {
          // 如果返回的是字符串，按原来的方式处理
          logStore.addSuccess(`自动注册完成: ${result}`, 'AutoRegister')
          appStore.showSuccess(`🎉 自动注册成功！\n\n✅ 新账户已创建并激活\n💡 可以开始使用Cursor了`)
        }
      } catch (error) {
        logStore.addError(`自动注册失败: ${(error as Error).message}`, 'AutoRegister')
        appStore.showError(`❌ 自动注册失败\n\n${(error as Error).message}`)
      }
    }
  )
}

// 获取随机配置
const handleGetRandomConfig = async () => {
  try {
    logStore.addInfo('正在生成随机配置...', 'RandomConfig')
    const result = await realAPI.getRandomConfig()

    // 检查返回结果
    if (typeof result === 'object' && result !== null) {
      if (result.success === true) {
        // 如果响应中包含日志信息，先记录这些日志
        if (result.logs && Array.isArray(result.logs)) {
          result.logs.forEach((logMsg: string) => {
            if (logMsg.includes('❌')) {
              logStore.addError(logMsg, 'RandomConfig')
            } else if (logMsg.includes('✅')) {
              logStore.addSuccess(logMsg, 'RandomConfig')
            } else {
              logStore.addInfo(logMsg, 'RandomConfig')
            }
          })
        } else {
          // 兼容旧版本，记录详细的配置信息到日志
          logStore.addSuccess('随机配置生成成功', 'RandomConfig')
          if (result.email) {
            logStore.addInfo(`📧 生成邮箱: ${result.email}`, 'RandomConfig')
          }
          if (result.firstName) {
            logStore.addInfo(`👤 生成名字: ${result.firstName}`, 'RandomConfig')
          }
          if (result.lastName) {
            logStore.addInfo(`👤 生成姓氏: ${result.lastName}`, 'RandomConfig')
          }
          if (result.password) {
            logStore.addInfo(`🔐 生成密码: ${result.password}`, 'RandomConfig')
          }
          if (result.tempDomain) {
            logStore.addInfo(`🌐 临时域名: ${result.tempDomain}`, 'RandomConfig')
          }
          if (result.pinCode) {
            logStore.addInfo(`📌 生成PIN码: ${result.pinCode}`, 'RandomConfig')
          }
        }

        // 构建显示内容
        let configContent = '🎲 随机配置信息\n\n'
        if (result.email) configContent += `📧 邮箱: ${result.email}\n`
        if (result.firstName) configContent += `👤 名字: ${result.firstName}\n`
        if (result.lastName) configContent += `👤 姓氏: ${result.lastName}\n`
        if (result.password) configContent += `🔐 密码: ${result.password}\n`
        if (result.tempDomain) configContent += `🌐 临时域名: ${result.tempDomain}\n`
        if (result.pinCode) configContent += `📌 PIN码: ${result.pinCode}\n`

        configContent += '\n💡 点击确定复制到剪贴板'

        // 显示信息弹窗，每个字段可单独复制
        if (window.showInfoModal) {
          window.showInfoModal('🎲 随机配置信息', result)
        } else {
          appStore.showSuccess(configContent)
        }
      } else {
        // 如果失败响应中包含日志信息，先记录这些日志
        if (result.logs && Array.isArray(result.logs)) {
          result.logs.forEach((logMsg: string) => {
            if (logMsg.includes('❌')) {
              logStore.addError(logMsg, 'RandomConfig')
            } else if (logMsg.includes('✅')) {
              logStore.addSuccess(logMsg, 'RandomConfig')
            } else {
              logStore.addInfo(logMsg, 'RandomConfig')
            }
          })
        }
        throw new Error(result.error || '生成随机配置失败')
      }
    } else {
      logStore.addSuccess(`随机配置生成完成: ${result}`, 'RandomConfig')
      appStore.showSuccess(`🎲 随机配置生成成功！\n\n${result}`)
    }
  } catch (error) {
    logStore.addError(`生成随机配置失败: ${(error as Error).message}`, 'RandomConfig')
    appStore.showError(`❌ 生成随机配置失败\n\n${(error as Error).message}`)
  }
}

// 获取验证码
const handleGetVerificationCode = async () => {
  try {
    logStore.addInfo('正在获取验证码...', 'VerificationCode')
    const result = await realAPI.getVerificationCode()

    // 检查返回结果
    if (typeof result === 'object' && result !== null) {
      if (result.success === true) {
        // 如果响应中包含日志信息，先记录这些日志
        if (result.logs && Array.isArray(result.logs)) {
          result.logs.forEach((logMsg: string) => {
            if (logMsg.includes('❌')) {
              logStore.addError(logMsg, 'VerificationCode')
            } else if (logMsg.includes('✅')) {
              logStore.addSuccess(logMsg, 'VerificationCode')
            } else {
              logStore.addInfo(logMsg, 'VerificationCode')
            }
          })
        } else {
          // 兼容旧版本，记录详细的验证码信息到日志
          logStore.addSuccess('验证码获取成功', 'VerificationCode')
          if (result.code) {
            logStore.addInfo(`🔢 获取验证码: ${result.code}`, 'VerificationCode')
          }
          if (result.email) {
            logStore.addInfo(`📧 来源邮箱: ${result.email}`, 'VerificationCode')
          }
          if (result.subject) {
            logStore.addInfo(`📝 邮件主题: ${result.subject}`, 'VerificationCode')
          }
          if (result.time) {
            logStore.addInfo(`⏰ 接收时间: ${result.time}`, 'VerificationCode')
          }
        }

        // 构建显示内容
        let codeContent = '📨 最新验证码\n\n'
        if (result.code) {
          codeContent += `🔢 验证码: ${result.code}\n`
        }
        if (result.email) {
          codeContent += `📧 邮箱: ${result.email}\n`
        }
        if (result.subject) {
          codeContent += `📝 主题: ${result.subject}\n`
        }
        if (result.time) {
          codeContent += `⏰ 时间: ${result.time}\n`
        }

        codeContent += '\n💡 点击确定复制验证码到剪贴板'

        // 显示确认弹窗，用户点击确定后复制验证码
        appStore.showConfirm(
          '📨 最新验证码',
          codeContent + '\n\n💡 点击确定复制验证码到剪贴板',
          async () => {
            // 用户点击确定后复制验证码到剪贴板
            const copyText = result.code || ''

            try {
              await navigator.clipboard.writeText(copyText)
              appStore.showSuccess('✅ 验证码已复制到剪贴板！')
            } catch (clipboardError) {
              appStore.showError('❌ 复制失败，请手动复制')
            }
          }
        )
      } else {
        // 如果失败响应中包含日志信息，先记录这些日志
        if (result.logs && Array.isArray(result.logs)) {
          result.logs.forEach((logMsg: string) => {
            if (logMsg.includes('❌')) {
              logStore.addError(logMsg, 'VerificationCode')
            } else if (logMsg.includes('✅')) {
              logStore.addSuccess(logMsg, 'VerificationCode')
            } else {
              logStore.addInfo(logMsg, 'VerificationCode')
            }
          })
        }
        throw new Error(result.error || '获取验证码失败')
      }
    } else {
      logStore.addSuccess(`验证码获取完成: ${result}`, 'VerificationCode')
      appStore.showSuccess(`📨 验证码获取成功！\n\n${result}`)
    }
  } catch (error) {
    logStore.addError(`获取验证码失败: ${(error as Error).message}`, 'VerificationCode')
    appStore.showError(`❌ 获取验证码失败\n\n${(error as Error).message}`)
  }
}

// 显示配置
const handleShowConfig = () => {
  logStore.addInfo('打开配置显示窗口...', 'ShowConfig')

  // 使用全局配置模态框
  if (window.showConfigModal) {
    window.showConfigModal()
  } else {
    logStore.addError('配置显示功能不可用', 'ShowConfig')
  }
}

// 初始化Cursor
const handleInitCursor = async () => {
  appStore.showConfirm(
    '🚀 初始化Cursor',
    '确定要初始化Cursor吗？\n\n🔧 初始化内容：\n• 重新配置认证信息\n• 清理缓存数据\n• 重置用户状态\n• 准备登录环境\n\n⚠️ 这将清除当前登录状态',
    async () => {
      logStore.addInfo('🚀 开始初始化Cursor...', 'InitCursor')
      try {
        const result = await realAPI.initCursor()

        // 检查返回结果
        if (typeof result === 'object' && result !== null) {
          if (result.success === true) {
            // 如果响应中包含日志信息，先记录这些日志
            if (result.logs && Array.isArray(result.logs)) {
              result.logs.forEach((logMsg: string) => {
                if (logMsg.includes('❌')) {
                  logStore.addError(logMsg, 'InitCursor')
                } else if (logMsg.includes('✅') || logMsg.includes('🎉')) {
                  logStore.addSuccess(logMsg, 'InitCursor')
                } else if (logMsg.includes('⚠️') || logMsg.includes('ℹ️')) {
                  logStore.addWarning(logMsg, 'InitCursor')
                } else {
                  logStore.addInfo(logMsg, 'InitCursor')
                }
              })
            } else {
              // 兼容旧版本
              logStore.addSuccess(`✅ Cursor初始化完成: ${result.message}`, 'InitCursor')
            }

            let successMsg = '🎉 Cursor初始化成功！'
            if (result.details && Array.isArray(result.details)) {
              successMsg += '\n\n📋 操作详情：\n' + result.details.join('\n')
            }
            appStore.showSuccess(successMsg)
          } else {
            // 如果失败响应中包含日志信息，先记录这些日志
            if (result.logs && Array.isArray(result.logs)) {
              result.logs.forEach((logMsg: string) => {
                if (logMsg.includes('❌')) {
                  logStore.addError(logMsg, 'InitCursor')
                } else {
                  logStore.addInfo(logMsg, 'InitCursor')
                }
              })
            }
            throw new Error(result.error || '初始化失败')
          }
        } else {
          // 如果返回的是字符串，按原来的方式处理
          logStore.addSuccess(`✅ Cursor初始化完成: ${result}`, 'InitCursor')
          appStore.showSuccess('Cursor初始化成功！')
        }
      } catch (error) {
        logStore.addError(`❌ 初始化失败: ${(error as Error).message}`, 'InitCursor')
        appStore.showError(`初始化失败: ${(error as Error).message}`)
      }
    }
  )
}

// 重置机器码
const handleResetMachineCode = async () => {
  appStore.showConfirm(
    '🔄 重置机器码',
    '确定要重置机器码吗？\n\n⚠️ 重要提醒：\n• 将生成新的机器标识符\n• 当前授权状态会被清除\n• 需要重新登录验证\n• 可能影响试用期计算\n\n💡 通常用于解决授权问题',
    async () => {
      try {
        logStore.addInfo('重置机器码...', 'ResetMachineCode')
        const result = await realAPI.resetMachineId()
        logStore.addSuccess(`机器码重置完成: ${result}`, 'ResetMachineCode')
        appStore.showSuccess(`🎉 机器码重置成功！\n\n✅ 新的设备标识已生成\n💡 现在可以重新注册账户了`)
      } catch (error) {
        logStore.addError(`重置机器码失败: ${(error as Error).message}`, 'ResetMachineCode')
        appStore.showError(`❌ 重置机器码失败\n\n${(error as Error).message}`)
      }
    }
  )
}



// 绕过版本检查
const handleBypassVersion = async () => {
  appStore.showConfirm(
    '绕过版本检查',
    '确定要绕过Cursor版本检查吗？这将修改产品配置文件。',
    async () => {
      try {
        logStore.addInfo('绕过版本检查...', 'BypassVersion')
        const result = await realAPI.bypassVersionCheck()
        logStore.addSuccess(`版本检查绕过完成: ${result}`, 'BypassVersion')

        // 显示成功通知
        appStore.showSuccess('✅ 版本检查绕过完成！')
      } catch (error) {
        const errorMsg = (error as Error).message
        logStore.addError(`绕过版本检查失败: ${errorMsg}`, 'BypassVersion')
        appStore.showError(`❌ 绕过版本检查失败: ${errorMsg}`)
      }
    }
  )
}















// 禁用自动更新
const handleAutoUpdate = async () => {
  appStore.showConfirm(
    '🛑 禁用自动更新',
    '确定要禁用Cursor的自动更新功能吗？\n\n🔒 此操作将：\n• 结束Cursor进程\n• 删除更新程序目录\n• 清空更新配置文件\n• 创建阻止文件\n• 移除更新URL\n\n💡 禁用后Cursor将不会自动更新\n⚠️ 需要管理员权限',
    async () => {
      try {
        logStore.addInfo('禁用自动更新...', 'AutoUpdate')
        const result = await realAPI.toggleAutoUpdate()
        logStore.addSuccess('自动更新已禁用', 'AutoUpdate')
        appStore.showSuccess(result)
      } catch (error) {
        logStore.addError(`禁用自动更新失败: ${(error as Error).message}`, 'AutoUpdate')
        appStore.showError(`❌ 禁用自动更新失败\n\n${(error as Error).message}`)
      }
    }
  )
}


</script>

<style scoped>
.features {
  padding: 20px;
}
</style>
