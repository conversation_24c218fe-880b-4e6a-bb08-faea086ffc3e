name: 💡 Feature Request
description: Suggest an idea for this project
title: "[Feature]: "
labels: ["enhancement"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to suggest a new feature!
        Please make sure to fill out all the sections below.
  - type: textarea
    id: feature-description
    attributes:
      label: Feature Description
      description: A clear and concise description of what you want to happen.
      placeholder: "I would like to..."
    validations:
      required: true
  - type: textarea
    id: problem-solution
    attributes:
      label: Problem & Solution
      description: Describe the problem you're trying to solve and how this feature would help.
      placeholder: "Currently, when I try to..."
    validations:
      required: true
  - type: textarea
    id: alternatives
    attributes:
      label: Alternatives Considered
      description: Describe any alternative solutions or features you've considered.
      placeholder: "I've considered..."
  - type: dropdown
    id: priority
    attributes:
      label: Priority
      description: How important is this feature to you?
      options:
        - High (Critical for my workflow)
        - Medium (Would be very helpful)
        - Low (Nice to have)
    validations:
      required: true
  - type: dropdown
    id: impact
    attributes:
      label: Impact
      description: How many users would benefit from this feature?
      options:
        - All users
        - Most users
        - Some users
        - Just me
    validations:
      required: true
  - type: textarea
    id: technical-details
    attributes:
      label: Technical Details
      description: Any technical considerations or implementation details you'd like to share.
      placeholder: "The feature could be implemented by..."
  - type: textarea
    id: screenshots
    attributes:
      label: Screenshots
      description: If applicable, add screenshots to help explain your feature.
      placeholder: "You can drag and drop screenshots here..."
  - type: checkboxes
    id: terms
    attributes:
      label: Code of Conduct
      description: By submitting this issue, you agree to follow this project's Code of Conduct
      options:
        - label: I agree to follow this project's Code of Conduct
          required: true
