<template>
  <div class="account-card">
    <!-- 顶部装饰条 -->
    <div class="top-decoration"></div>

    <!-- 装饰元素 -->
    <div class="dots-decoration">
      <div class="dot"></div>
      <div class="dot"></div>
    </div>

    <!-- 卡片头部 -->
    <div class="card-header-new">
      <div class="email-new">{{ displayEmail }}</div>
      <div class="plan-badge-new" :class="planClass">{{ displayPlan }}</div>
    </div>

    <!-- 底部指标 -->
    <div class="bottom-horizontal-new">
      <div class="metric-row-new">
        <div class="metric-label-new">
          <span class="progress-icon" :class="statusIconClass"></span>
          <span>状态</span>
        </div>
        <div class="metric-value-new">{{ statusText }}</div>
      </div>

      <div class="metric-row-new">
        <div class="metric-label-new">
          <span class="progress-icon icon-blue"></span>
          <span>试用剩余</span>
        </div>
        <div class="metric-value-new">{{ trialDaysText }}</div>
      </div>

      <div class="metric-row-new">
        <div class="metric-label-new">
          <span class="progress-icon icon-green"></span>
          <span>Pro使用量</span>
        </div>
        <div class="metric-value-new">{{ usageText }}</div>
      </div>

      <div class="metric-row-new">
        <div class="metric-label-new">
          <span class="progress-icon icon-purple"></span>
          <span>Basic总量</span>
        </div>
        <div class="metric-value-new">{{ basicTotalText }}</div>
      </div>
    </div>

    <!-- 刷新按钮 -->
    <button 
      class="refresh-btn" 
      @click="handleRefresh"
      :disabled="loading"
      title="刷新账户信息"
    >
      <svg 
        width="16" 
        height="16" 
        viewBox="0 0 24 24" 
        fill="none" 
        stroke="currentColor" 
        stroke-width="2"
        :class="{ 'rotating': loading }"
      >
        <polyline points="23 4 23 10 17 10"></polyline>
        <polyline points="1 20 1 14 7 14"></polyline>
        <path d="m21 4-7 7-7-7"></path>
        <path d="m3 20 7-7 7 7"></path>
      </svg>
    </button>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
// 定义账户信息类型
interface AccountInfo {
  email: string
  plan: string
  trial_days: number
  pro_used: number
  pro_total: number
  pro_percent: number
  basic_total: string
}

interface AccountInfoProps {
  account: AccountInfo
  loading?: boolean
}

interface AccountInfoEvents {
  'refresh': []
  'copy': [text: string]
}

const props = withDefaults(defineProps<AccountInfoProps>(), {
  loading: false
})

const emit = defineEmits<AccountInfoEvents>()

// 计算属性
const displayEmail = computed(() => {
  if (props.loading) return '加载中...'
  return props.account.email || '未登录'
})

const displayPlan = computed(() => {
  if (props.loading) return '-'
  return props.account.plan || '-'
})

const planClass = computed(() => {
  const plan = props.account.plan
  if (plan === 'Pro') return 'plan-pro'
  if (plan === 'Free') return 'plan-free'
  return 'plan-unknown'
})

const statusIconClass = computed(() => {
  if (props.loading) return 'icon-blue'
  const email = props.account.email
  if (email && email !== '未登录') return 'icon-green'
  return 'icon-red'
})

const statusText = computed(() => {
  if (props.loading) return '加载中'
  const email = props.account.email
  if (email && email !== '未登录') return '已登录'
  return '未登录'
})

const trialDaysText = computed(() => {
  if (props.loading) return '-'
  const days = props.account.trial_days
  if (days !== undefined && days !== null) {
    return `${days} 天`
  }
  return '14 天'
})

const usageText = computed(() => {
  if (props.loading) return '-'
  const used = props.account.pro_used || 0
  const total = props.account.pro_total || 999
  const percent = props.account.pro_percent || 0
  return `${used}/${total} (${percent}%)`
})

const basicTotalText = computed(() => {
  if (props.loading) return '-'
  return props.account.basic_total || 'No Limit'
})

// 方法
const handleRefresh = () => {
  if (!props.loading) {
    emit('refresh')
  }
}
</script>

<style scoped>
/* 账号信息卡片 - 增强版紫色风格 */
.account-card {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%);
  border: none;
  border-radius: 24px;
  padding: 24px;
  margin-bottom: 32px;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(139, 92, 246, 0.3);
  overflow: hidden;
}

.account-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(139, 92, 246, 0.4);
}

/* 顶部装饰条 */
.top-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #FFD700 0%, #FFA500 50%, #FFD700 100%);
  border-radius: 24px 24px 0 0;
}

/* 装饰点 */
.dots-decoration {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  gap: 8px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
}

/* 卡片头部 */
.card-header-new {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-top: 8px;
}

.email-new {
  color: #ffffff;
  font-size: 1.4rem;
  font-weight: 700;
  font-family: 'Fira Mono', 'Consolas', monospace;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  max-width: 60%;
  word-break: break-all;
}

.plan-badge-new {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  font-family: 'Fira Mono', 'Consolas', monospace;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.plan-badge-new.plan-pro {
  background: rgba(16, 185, 129, 0.3);
  border-color: rgba(16, 185, 129, 0.5);
}

.plan-badge-new.plan-free {
  background: rgba(107, 114, 128, 0.3);
  border-color: rgba(107, 114, 128, 0.5);
}

/* 底部指标 */
.bottom-horizontal-new {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.metric-row-new {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.metric-label-new {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.85rem;
  font-weight: 500;
  font-family: 'Fira Mono', 'Consolas', monospace;
}

.metric-value-new {
  color: #ffffff;
  font-size: 0.9rem;
  font-weight: 600;
  font-family: 'Fira Mono', 'Consolas', monospace;
  text-align: right;
}

/* 进度图标 */
.progress-icon {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  flex-shrink: 0;
}

.icon-blue { background: #3b82f6; }
.icon-green { background: #10b981; }
.icon-red { background: #ef4444; }
.icon-purple { background: #8b5cf6; }

/* 刷新按钮 */
.refresh-btn {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.refresh-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.1);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
