#!/usr/bin/env python3
"""
更新导入路径脚本
将旧的导入路径更新为新的包结构
"""

import os
import re
from pathlib import Path

# 导入映射表
IMPORT_MAPPING = {
    # 核心模块
    'from main import': 'from cursor_pro.core.main import',
    'import main': 'import cursor_pro.core.main as main',
    'from vue_with_api_optimized import': 'from cursor_pro.core.api_server import',
    'import vue_with_api_optimized': 'import cursor_pro.core.api_server as vue_with_api_optimized',
    'from cursor_acc_info import': 'from cursor_pro.core.account_info import',
    'import cursor_acc_info': 'import cursor_pro.core.account_info as cursor_acc_info',
    
    # 认证模块
    'from cursor_auth import': 'from cursor_pro.auth.cursor_auth import',
    'import cursor_auth': 'import cursor_pro.auth.cursor_auth as cursor_auth',
    'from oauth_auth import': 'from cursor_pro.auth.oauth_auth import',
    'import oauth_auth': 'import cursor_pro.auth.oauth_auth as oauth_auth',
    'from get_user_token import': 'from cursor_pro.auth.token_manager import',
    'import get_user_token': 'import cursor_pro.auth.token_manager as get_user_token',
    'from new_signup import': 'from cursor_pro.auth.signup import',
    'import new_signup': 'import cursor_pro.auth.signup as new_signup',
    'from cursor_register_manual import': 'from cursor_pro.auth.register import',
    'import cursor_register_manual': 'import cursor_pro.auth.register as cursor_register_manual',
    
    # 备份模块
    'from backup_settings import': 'from cursor_pro.backup.settings import',
    'import backup_settings': 'import cursor_pro.backup.settings as backup_settings',
    'from backup_session import': 'from cursor_pro.backup.session import',
    'import backup_session': 'import cursor_pro.backup.session as backup_session',
    
    # 反检测模块
    'from advanced_anti_detection import': 'from cursor_pro.anti_detection.advanced import',
    'import advanced_anti_detection': 'import cursor_pro.anti_detection.advanced as advanced_anti_detection',
    'from anti_detection_integration import': 'from cursor_pro.anti_detection.integration import',
    'import anti_detection_integration': 'import cursor_pro.anti_detection.integration as anti_detection_integration',
    'from cursor_pro_anti_detection import': 'from cursor_pro.anti_detection.core import',
    'import cursor_pro_anti_detection': 'import cursor_pro.anti_detection.core as cursor_pro_anti_detection',
    'from bypass_token_limit import': 'from cursor_pro.anti_detection.bypass_token import',
    'import bypass_token_limit': 'import cursor_pro.anti_detection.bypass_token as bypass_token_limit',
    'from bypass_version import': 'from cursor_pro.anti_detection.bypass_version import',
    'import bypass_version': 'import cursor_pro.anti_detection.bypass_version as bypass_version',
    'from reset_machine_manual import': 'from cursor_pro.anti_detection.machine_reset import',
    'import reset_machine_manual': 'import cursor_pro.anti_detection.machine_reset as reset_machine_manual',
    
    # 管理模块
    'from account_manager import': 'from cursor_pro.management.account_manager import',
    'import account_manager': 'import cursor_pro.management.account_manager as account_manager',
    'from admin_panel import': 'from cursor_pro.management.admin_panel import',
    'import admin_panel': 'import cursor_pro.management.admin_panel as admin_panel',
    'from config import': 'from cursor_pro.management.config import',
    'import config': 'import cursor_pro.management.config as config',
    'from data_path_manager import': 'from cursor_pro.management.data_path import',
    'import data_path_manager': 'import cursor_pro.management.data_path as data_path_manager',
    'from legacy_path_adapter import': 'from cursor_pro.management.legacy_adapter import',
    'import legacy_path_adapter': 'import cursor_pro.management.legacy_adapter as legacy_path_adapter',
    
    # 工具模块
    'from utils import': 'from cursor_pro.utils.common import',
    'import utils': 'import cursor_pro.utils.common as utils',
    'from app_version import': 'from cursor_pro.utils.app_version import',
    'import app_version': 'import cursor_pro.utils.app_version as app_version',
    'from version_control import': 'from cursor_pro.utils.version_control import',
    'import version_control': 'import cursor_pro.utils.version_control as version_control',
    'from parameter_tuning import': 'from cursor_pro.utils.tuning import',
    'import parameter_tuning': 'import cursor_pro.utils.tuning as parameter_tuning',
    'from disable_auto_update import': 'from cursor_pro.utils.updater import',
    'import disable_auto_update': 'import cursor_pro.utils.updater as disable_auto_update',
    'from check_user_authorized import': 'from cursor_pro.utils.auth_check import',
    'import check_user_authorized': 'import cursor_pro.utils.auth_check as check_user_authorized',
    
    # 数据库模块
    'from database.config import': 'from cursor_pro.database.config import',
    'from database.sqlite_config import': 'from cursor_pro.database.sqlite_config import',
}

def update_file_imports(file_path):
    """更新单个文件的导入语句"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 应用导入映射
        for old_import, new_import in IMPORT_MAPPING.items():
            content = content.replace(old_import, new_import)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 更新: {file_path}")
            return True
        else:
            print(f"⏭️ 跳过: {file_path} (无需更新)")
            return False
            
    except Exception as e:
        print(f"❌ 错误: {file_path} - {str(e)}")
        return False

def main():
    """主函数"""
    print("🔄 开始更新导入路径...")
    
    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    src_dir = project_root / "src" / "cursor_pro"
    
    updated_count = 0
    total_count = 0
    
    # 遍历所有Python文件
    for py_file in src_dir.rglob("*.py"):
        if py_file.name == "__init__.py":
            continue  # 跳过__init__.py文件
            
        total_count += 1
        if update_file_imports(py_file):
            updated_count += 1
    
    print(f"\n📊 更新完成:")
    print(f"   总文件数: {total_count}")
    print(f"   更新文件数: {updated_count}")
    print(f"   跳过文件数: {total_count - updated_count}")

if __name__ == "__main__":
    main()
