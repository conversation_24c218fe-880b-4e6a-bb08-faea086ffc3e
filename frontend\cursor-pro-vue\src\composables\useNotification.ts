import { ref, reactive } from 'vue'

export interface NotificationItem {
  id: string
  type: 'success' | 'info' | 'warning' | 'error'
  title: string
  message?: string
  duration?: number
  closable?: boolean
}

const notifications = ref<NotificationItem[]>([])

let notificationId = 0

export function useNotification() {
  const addNotification = (notification: Omit<NotificationItem, 'id'>) => {
    const id = `notification-${++notificationId}`
    const item: NotificationItem = {
      id,
      duration: 4000,
      closable: true,
      ...notification
    }
    
    notifications.value.push(item)
    
    // 自动移除通知
    if (item.duration && item.duration > 0) {
      setTimeout(() => {
        removeNotification(id)
      }, item.duration)
    }
    
    return id
  }
  
  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }
  
  const clearAll = () => {
    notifications.value = []
  }
  
  // 便捷方法
  const success = (title: string, message?: string, options?: Partial<NotificationItem>) => {
    return addNotification({ type: 'success', title, message, ...options })
  }
  
  const info = (title: string, message?: string, options?: Partial<NotificationItem>) => {
    return addNotification({ type: 'info', title, message, ...options })
  }
  
  const warning = (title: string, message?: string, options?: Partial<NotificationItem>) => {
    return addNotification({ type: 'warning', title, message, ...options })
  }
  
  const error = (title: string, message?: string, options?: Partial<NotificationItem>) => {
    return addNotification({ type: 'error', title, message, ...options })
  }
  
  return {
    notifications,
    addNotification,
    removeNotification,
    clearAll,
    success,
    info,
    warning,
    error
  }
}

// 全局实例
export const notification = useNotification()
