<template>
  <!-- 模态框遮罩 -->
  <div
    v-if="visible"
    class="modal-mask"
    @click="handleMaskClick"
  >
    <!-- 使用新的卡片样式 -->
    <div class="card confirm-card" @click.stop>
      <span class="card__title">{{ title }}</span>

      <!-- 结构化确认弹窗布局 -->
      <div v-if="isStructuredConfirm" class="structured-content">
        <div class="structured-description">
          <p>{{ structuredContent?.description }}</p>
        </div>

        <div class="structured-steps">
          <div class="steps-header">
            <h4>📋 {{ confirmType === 'workflow' ? '执行步骤：' : '操作内容：' }}</h4>
            <!-- 只有工作流程才显示选择控制 -->
            <div v-if="confirmType === 'workflow'" class="steps-controls">
              <button
                type="button"
                class="select-all-btn"
                @click="selectAllSteps"
              >
                全选
              </button>
              <button
                type="button"
                class="select-none-btn"
                @click="selectNoneSteps"
              >
                全不选
              </button>
            </div>
          </div>
          <div class="steps-grid">
            <div
              v-for="(step, index) in structuredContent?.steps"
              :key="index"
              class="step-item"
              :class="{
                'step-selected': confirmType === 'workflow' && workflowSteps[index]?.selected,
                'step-disabled': confirmType === 'workflow' && !workflowSteps[index]?.enabled,
                'step-readonly': confirmType !== 'workflow',
                'step-clickable': confirmType === 'workflow' && workflowSteps[index]?.enabled
              }"
              @click="confirmType === 'workflow' && workflowSteps[index]?.enabled && toggleStep(index)"
            >
              <!-- 只有工作流程才显示复选框 -->
              <label v-if="confirmType === 'workflow'" class="custom-checkbox" @click.stop="workflowSteps[index]?.enabled && toggleStep(index)">
                <input
                  type="checkbox"
                  :checked="workflowSteps[index]?.selected"
                  :disabled="!workflowSteps[index]?.enabled"
                  @change="workflowSteps[index]?.enabled && toggleStep(index)"
                />
                <span class="checkmark"></span>
              </label>
              <div class="step-number">{{ index + 1 }}</div>
              <div class="step-content">
                <div class="step-icon">{{ step.icon }}</div>
                <div class="step-text">{{ step.text }}</div>
              </div>
            </div>
          </div>
          <!-- 只有工作流程才显示选择计数 -->
          <div v-if="confirmType === 'workflow'" class="selected-count" :class="{ 'no-selection': selectedStepsCount === 0 }">
            已选择 {{ selectedStepsCount }} / {{ workflowSteps.length }} 个步骤
            <span v-if="selectedStepsCount === 0" class="warning-text">（请至少选择一个步骤）</span>
          </div>
        </div>

        <div class="structured-warnings">
          <div
            v-for="(warning, index) in structuredContent?.warnings"
            :key="index"
            :class="`${warning.type}-item`"
          >
            <span class="warning-icon">{{ warning.icon }}</span>
            <span>{{ warning.text }}</span>
          </div>
        </div>
      </div>

      <!-- 普通确认对话框内容 -->
      <div v-else class="normal-content">
        <p class="card__content">{{ message }}</p>
      </div>

      <div class="card__form">
        <button class="card__button card__button--cancel" @click="handleCancel">
          {{ cancelText }}
        </button>
        <button
          class="card__button card__button--confirm"
          @click="handleConfirm"
          :disabled="confirmType === 'workflow' && selectedStepsCount === 0"
          :class="{ 'disabled': confirmType === 'workflow' && selectedStepsCount === 0 }"
        >
          {{ confirmText }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'

interface ConfirmModalProps {
  visible: boolean
  title: string
  message: string
  confirmText?: string
  cancelText?: string
  closable?: boolean
}

interface ConfirmModalEvents {
  'confirm': [selectedSteps?: string[]]
  'cancel': []
}

const props = withDefaults(defineProps<ConfirmModalProps>(), {
  confirmText: '确定',
  cancelText: '取消',
  closable: true
})

const emit = defineEmits<ConfirmModalEvents>()

// 判断确认弹窗类型
const confirmType = computed(() => {
  if (props.title.includes('一键完整流程')) return 'workflow'
  if (props.title.includes('一键自动注册')) return 'register'
  if (props.title.includes('初始化Cursor')) return 'init'
  if (props.title.includes('重置机器码')) return 'reset'
  if (props.title.includes('禁用自动更新')) return 'update'
  if (props.title.includes('绕过版本检查')) return 'bypass'
  if (props.title.includes('危险操作确认')) return 'danger'
  return 'normal'
})

// 判断是否为结构化弹窗
const isStructuredConfirm = computed(() => {
  return confirmType.value !== 'normal'
})

// 获取结构化内容
const structuredContent = computed(() => {
  switch (confirmType.value) {
    case 'workflow':
      return {
        description: '确定要执行完整的自动化流程吗？',
        steps: [
          { icon: '💾', text: '保存原账户信息', key: 'saveAccount' },
          { icon: '⚙️', text: '备份设置', key: 'backupSettings' },
          { icon: '💬', text: '备份会话', key: 'backupSession' },
          { icon: '🔄', text: '重置机器码', key: 'resetMachine' },
          { icon: '📝', text: '自动注册新账户', key: 'autoRegister' },
          { icon: '🧹', text: '初始化Cursor配置', key: 'initCursor' },
          { icon: '⚙️', text: '恢复设置', key: 'restoreSettings' },
          { icon: '💬', text: '恢复会话', key: 'restoreSession' },
          { icon: '🔄', text: '重启Cursor', key: 'restartCursor' }
        ],
        warnings: [
          { type: 'warning', icon: '⚠️', text: '此操作将重置当前配置' },
          { type: 'success', icon: '✅', text: '所有功能均为真实执行' }
        ]
      }
    case 'register':
      return {
        description: '确定要使用临时邮箱进行自动注册吗？',
        steps: [
          { icon: '📧', text: '生成临时邮箱地址' },
          { icon: '✍️', text: '自动填写注册信息' },
          { icon: '📤', text: '提交注册申请' },
          { icon: '✉️', text: '等待邮箱验证' }
        ],
        warnings: [
          { type: 'info', icon: '💡', text: '请确保已在设置中配置好注册参数' }
        ]
      }
    case 'init':
      return {
        description: '确定要初始化Cursor吗？',
        steps: [
          { icon: '🔧', text: '重新配置认证信息' },
          { icon: '🧹', text: '清理缓存数据' },
          { icon: '🔄', text: '重置用户状态' },
          { icon: '🚀', text: '准备登录环境' }
        ],
        warnings: [
          { type: 'warning', icon: '⚠️', text: '这将清除当前登录状态' }
        ]
      }
    case 'reset':
      return {
        description: '确定要重置机器码吗？',
        steps: [
          { icon: '🆔', text: '生成新的机器标识符' },
          { icon: '🔓', text: '清除当前授权状态' },
          { icon: '🔑', text: '需要重新登录验证' },
          { icon: '⏰', text: '可能影响试用期计算' }
        ],
        warnings: [
          { type: 'info', icon: '💡', text: '通常用于解决授权问题' }
        ]
      }
    case 'update':
      return {
        description: '确定要禁用Cursor的自动更新功能吗？',
        steps: [
          { icon: '⏹️', text: '结束Cursor进程' },
          { icon: '🗂️', text: '删除更新程序目录' },
          { icon: '📄', text: '清空更新配置文件' },
          { icon: '🚫', text: '创建阻止文件' },
          { icon: '🔗', text: '移除更新URL' }
        ],
        warnings: [
          { type: 'info', icon: '💡', text: '禁用后Cursor将不会自动更新' },
          { type: 'warning', icon: '⚠️', text: '需要管理员权限' }
        ]
      }
    case 'bypass':
      return {
        description: '确定要绕过Cursor版本检查吗？',
        steps: [
          { icon: '📝', text: '修改产品配置文件' },
          { icon: '🔧', text: '更新版本检查设置' },
          { icon: '✅', text: '应用配置更改' }
        ],
        warnings: [
          { type: 'info', icon: '💡', text: '这将修改Cursor的配置文件' }
        ]
      }
    case 'danger':
      return {
        description: '确定要执行完全重置吗？',
        steps: [
          { icon: '🗑️', text: '清除所有配置' },
          { icon: '🔄', text: '重置所有设置' },
          { icon: '💾', text: '删除所有数据' }
        ],
        warnings: [
          { type: 'danger', icon: '🚨', text: '此操作不可撤销！' }
        ]
      }
    default:
      return null
  }
})

// 工作流程步骤（与Features.vue中的步骤定义保持一致）
const workflowSteps = ref([
  { icon: '💾', text: '保存原账户信息', selected: true, enabled: true, key: 'saveAccount' },
  { icon: '⚙️', text: '备份设置', selected: true, enabled: true, key: 'backupSettings' },
  { icon: '💬', text: '备份会话', selected: true, enabled: true, key: 'backupSession' },
  { icon: '🔄', text: '重置机器码', selected: true, enabled: true, key: 'resetMachine' },
  { icon: '📝', text: '自动注册新账户', selected: true, enabled: true, key: 'autoRegister' },
  { icon: '🧹', text: '初始化Cursor配置', selected: true, enabled: true, key: 'initCursor' },
  { icon: '⚙️', text: '恢复设置', selected: true, enabled: true, key: 'restoreSettings' },
  { icon: '💬', text: '恢复会话', selected: true, enabled: true, key: 'restoreSession' },
  { icon: '🔄', text: '重启Cursor', selected: true, enabled: true, key: 'restartCursor' }
])

// 选中步骤数量
const selectedStepsCount = computed(() => {
  return workflowSteps.value.filter(step => step.selected).length
})

// 获取选中的步骤
const getSelectedSteps = () => {
  return workflowSteps.value
    .filter(step => step.selected)
    .map(step => step.key)
}

// 切换步骤选择
const toggleStep = (index: number) => {
  console.log(`🔍 ConfirmModal toggleStep 被调用 - 索引: ${index}`)
  if (workflowSteps.value[index] && workflowSteps.value[index].enabled) {
    const oldSelected = workflowSteps.value[index].selected
    workflowSteps.value[index].selected = !workflowSteps.value[index].selected
    console.log(`🔍 ConfirmModal toggleStep - 步骤${index + 1}(${workflowSteps.value[index].key}) 选择状态: ${oldSelected} -> ${workflowSteps.value[index].selected}`)
  } else {
    console.log(`🔍 ConfirmModal toggleStep - 步骤${index + 1} 被禁用或不存在`)
  }
}

// 全选步骤
const selectAllSteps = () => {
  workflowSteps.value.forEach(step => {
    if (step.enabled) {
      step.selected = true
    }
  })
}

// 全不选步骤
const selectNoneSteps = () => {
  workflowSteps.value.forEach(step => {
    if (step.enabled) {
      step.selected = false
    }
  })
}

const handleConfirm = () => {
  if (confirmType.value === 'workflow') {
    // 如果是工作流程确认，传递选中的步骤
    const selectedSteps = getSelectedSteps()
    console.log('🔍 ConfirmModal handleConfirm - workflowSteps:', workflowSteps.value)
    console.log('🔍 ConfirmModal handleConfirm - selectedSteps:', selectedSteps)
    if (selectedSteps.length === 0) {
      // 如果没有选中任何步骤，提示用户
      return
    }
    emit('confirm', selectedSteps)
  } else {
    emit('confirm')
  }
}

const handleCancel = () => {
  emit('cancel')
}

const handleMaskClick = () => {
  if (props.closable) {
    handleCancel()
  }
}
</script>

<style scoped>
/* From Uiverse.io by 0xnihilism */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.card {
  width: 480px;
  max-width: 95vw;
  max-height: 90vh;
  padding: 20px;
  background: #fff;
  border: 6px solid #000;
  box-shadow: 12px 12px 0 #000;
  transition: transform 0.3s, box-shadow 0.3s;
  animation: slideIn 0.3s ease;
  border-radius: 12px;
  overflow-y: auto;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.card:hover {
  transform: translate(-5px, -5px);
  box-shadow: 17px 17px 0 #000;
}

.card__title {
  font-size: 32px;
  font-weight: 900;
  color: #000;
  text-transform: uppercase;
  margin-bottom: 15px;
  display: block;
  position: relative;
  overflow: hidden;
}

.card__title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 90%;
  height: 3px;
  background-color: #000;
  transform: translateX(-100%);
  transition: transform 0.3s;
}

.card:hover .card__title::after {
  transform: translateX(0);
}

.card__content {
  font-size: 16px;
  line-height: 1.4;
  color: #000;
  margin-bottom: 20px;
}

.card__form {
  display: flex;
  flex-direction: row;
  gap: 15px;
  justify-content: center;
}

.card__button {
  border: 3px solid #000;
  background: #000;
  color: #fff;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: bold;
  text-transform: uppercase;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s;
  min-width: 100px;
}

.card__button--cancel {
  background: #dc2626;
  border-color: #dc2626;
}

.card__button--cancel::before {
  content: "Sure?";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 105%;
  background-color: #f87171;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.card__button--cancel:hover::before {
  transform: translateY(0);
}

.card__button--confirm::before {
  content: "Sure?";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 105%;
  background-color: #5ad641;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.card__button--confirm:hover::before {
  transform: translateY(0);
}

.card__button:active {
  transform: scale(0.95);
}

.card__button.disabled,
.card__button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.card__button.disabled::before,
.card__button:disabled::before {
  display: none;
}

@keyframes glitch {
  0% {
    transform: translate(2px, 2px);
  }
  25% {
    transform: translate(-2px, -2px);
  }
  50% {
    transform: translate(-2px, 2px);
  }
  75% {
    transform: translate(2px, -2px);
  }
  100% {
    transform: translate(2px, 2px);
  }
}

.glitch {
  animation: glitch 0.3s infinite;
}

/* 结构化弹窗样式 */
.structured-content {
  margin-bottom: 16px;
}

.structured-description {
  margin-bottom: 16px;
}

.structured-description p {
  font-size: 15px;
  color: #333;
  margin: 0;
  text-align: center;
  font-weight: 500;
}

.structured-steps {
  margin-bottom: 16px;
}

.steps-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.workflow-steps h4 {
  font-size: 18px;
  font-weight: 700;
  color: #000;
  margin: 0;
}

.steps-controls {
  display: flex;
  gap: 8px;
}

.select-all-btn,
.select-none-btn {
  padding: 4px 8px;
  font-size: 12px;
  border: 1px solid #ddd;
  background: #f8f9fa;
  color: #333;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.select-all-btn:hover {
  background: #e3f2fd;
  border-color: #2196f3;
  color: #1976d2;
}

.select-none-btn:hover {
  background: #ffebee;
  border-color: #f44336;
  color: #d32f2f;
}

.steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 10px;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  transition: all 0.3s ease;
  user-select: none;
}

.step-item.step-clickable {
  cursor: pointer;
}

.step-item.step-clickable:hover {
  background: #e3f2fd;
  border-color: #2196f3;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2);
}



.step-item.step-selected {
  background: #f0f8ff;
  border-color: #333;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.step-item.step-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.step-item.step-disabled:hover {
  background: #f8f9fa;
  border-color: #e9ecef;
  transform: none;
}

.step-item.step-readonly {
  background: #f8f9fa;
  border-color: #e9ecef;
  cursor: default;
}

.step-item.step-readonly:hover {
  background: #f0f0f0;
  border-color: #ddd;
  transform: translateY(-1px);
}



.step-item.step-readonly .step-number,
.step-item.step-readonly .step-content {
  cursor: default;
}

/* 自定义复选框样式 */
.custom-checkbox {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  font-size: 16px;
  color: #333;
  transition: color 0.3s;
  margin-right: 4px;
}

.custom-checkbox input[type="checkbox"] {
  display: none;
}

.custom-checkbox .checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid #333;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s, border-color 0.3s, transform 0.3s;
  transform-style: preserve-3d;
}

.custom-checkbox .checkmark::before {
  content: "\2713";
  font-size: 14px;
  color: transparent;
  transition: color 0.3s, transform 0.3s;
}

.custom-checkbox input[type="checkbox"]:checked + .checkmark {
  background-color: #333;
  border-color: #333;
  transform: scale(1.1) rotateZ(360deg) rotateY(360deg);
}

.custom-checkbox input[type="checkbox"]:checked + .checkmark::before {
  color: #fff;
}

.custom-checkbox:hover {
  color: #666;
}

.custom-checkbox:hover .checkmark {
  border-color: #666;
  background-color: #f0f0f0;
  transform: scale(1.05);
}

.custom-checkbox input[type="checkbox"]:focus + .checkmark {
  box-shadow: 0 0 3px 2px rgba(0, 0, 0, 0.2);
  outline: none;
}

.custom-checkbox .checkmark,
.custom-checkbox input[type="checkbox"]:checked + .checkmark {
  transition: background-color 1.3s, border-color 1.3s, color 1.3s, transform 0.3s;
}

/* 禁用状态 */
.step-item.step-disabled .custom-checkbox {
  opacity: 0.5;
  cursor: not-allowed;
}

.step-item.step-disabled .custom-checkbox .checkmark {
  border-color: #ccc;
}

.step-item.step-disabled .custom-checkbox:hover .checkmark {
  border-color: #ccc;
  background-color: transparent;
  transform: none;
}

.step-item.step-disabled .step-number,
.step-item.step-disabled .step-content {
  cursor: not-allowed;
  opacity: 0.6;
}

.step-number {
  width: 22px;
  height: 22px;
  background: #333;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 700;
  flex-shrink: 0;
  transition: all 0.3s ease;

}

.step-item.step-selected .step-number {
  background: #333;
  transform: scale(1.1);
}

.step-content {
  flex: 1;
}

.step-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.step-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.step-text {
  font-size: 13px;
  color: #333;
  font-weight: 500;
  line-height: 1.2;
}

.selected-count {
  text-align: center;
  margin-top: 12px;
  font-size: 14px;
  color: #666;
  font-weight: 500;
  padding: 8px;
  background: #f0f0f0;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.selected-count.no-selection {
  background: #ffebee;
  border: 1px solid #ffcdd2;
  color: #c62828;
}

.warning-text {
  color: #d32f2f;
  font-weight: 600;
}

.structured-warnings {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 16px;
}

.warning-item,
.success-item,
.info-item,
.danger-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
}

.warning-item {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
}

.success-item {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.info-item {
  background: #d1ecf1;
  border: 1px solid #bee5eb;
  color: #0c5460;
}

.danger-item {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.warning-icon,
.success-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.normal-content {
  margin-bottom: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card {
    width: 95vw;
    max-width: 380px;
    padding: 16px;
    max-height: 85vh;
  }

  .card__form {
    flex-direction: column;
    gap: 10px;
  }

  .card__button {
    width: 100%;
  }

  .steps-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .step-item {
    padding: 8px;
    gap: 6px;
  }

  .structured-warnings {
    gap: 4px;
  }

  .structured-description p {
    font-size: 14px;
  }

  .step-text {
    font-size: 12px;
  }
}
</style>
