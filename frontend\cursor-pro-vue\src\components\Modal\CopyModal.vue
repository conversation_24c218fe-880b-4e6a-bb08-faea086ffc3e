<template>
  <!-- 复制成功小弹窗 -->
  <div
    v-if="visible"
    class="modal-mask"
    @click="handleClose"
  >
    <div class="card copy-card" @click.stop>
      <span class="card__title">Success!</span>
      <p class="card__content">{{ message }}</p>
      <form class="card__form">
        <button type="button" class="card__button" @click="handleClose">
          Got it!
        </button>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch } from 'vue'

interface CopyModalProps {
  visible: boolean
  message?: string
  autoClose?: boolean
  autoCloseDelay?: number
}

interface CopyModalEvents {
  'close': []
  'update:visible': [visible: boolean]
}

const props = withDefaults(defineProps<CopyModalProps>(), {
  message: '✔ 已复制到剪贴板！',
  autoClose: true,
  autoCloseDelay: 1800
})

const emit = defineEmits<CopyModalEvents>()

// 自动关闭
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.autoClose) {
    setTimeout(() => {
      handleClose()
    }, props.autoCloseDelay)
  }
})

const handleClose = () => {
  emit('close')
  emit('update:visible', false)
}
</script>

<style scoped>
/* From Uiverse.io by 0xnihilism */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1100;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.card {
  width: 300px;
  padding: 20px;
  background: #fff;
  border: 6px solid #000;
  box-shadow: 12px 12px 0 #000;
  transition: transform 0.3s, box-shadow 0.3s;
  animation: bounceIn 0.4s ease;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.card:hover {
  transform: translate(-5px, -5px);
  box-shadow: 17px 17px 0 #000;
}

.card__title {
  font-size: 32px;
  font-weight: 900;
  color: #000;
  text-transform: uppercase;
  margin-bottom: 15px;
  display: block;
  position: relative;
  overflow: hidden;
}

.card__title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 90%;
  height: 3px;
  background-color: #000;
  transform: translateX(-100%);
  transition: transform 0.3s;
}

.card:hover .card__title::after {
  transform: translateX(0);
}

.card__content {
  font-size: 16px;
  line-height: 1.4;
  color: #000;
  margin-bottom: 20px;
}

.card__form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: center;
}

.card__button {
  border: 3px solid #000;
  background: #000;
  color: #fff;
  padding: 10px 20px;
  font-size: 18px;
  font-weight: bold;
  text-transform: uppercase;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s;
  width: 60%;
}

.card__button::before {
  content: "Sure?";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 105%;
  background-color: #5ad641;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.card__button:hover::before {
  transform: translateY(0);
}

.card__button:active {
  transform: scale(0.95);
}

@keyframes glitch {
  0% {
    transform: translate(2px, 2px);
  }
  25% {
    transform: translate(-2px, -2px);
  }
  50% {
    transform: translate(-2px, 2px);
  }
  75% {
    transform: translate(2px, -2px);
  }
  100% {
    transform: translate(2px, 2px);
  }
}

.glitch {
  animation: glitch 0.3s infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card {
    width: 90vw;
    max-width: 280px;
  }

  .card__button {
    width: 80%;
  }
}
</style>
