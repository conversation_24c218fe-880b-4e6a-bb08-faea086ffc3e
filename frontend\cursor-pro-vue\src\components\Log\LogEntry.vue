<template>
  <div 
    class="log-entry"
    :class="[
      `log-level-${log.level}`,
      { 'log-highlighted': highlighted }
    ]"
  >
    <!-- 时间戳 -->
    <span class="log-timestamp">{{ formattedTime }}</span>
    
    <!-- 来源标识 -->
    <span v-if="log.source" class="log-source">[{{ log.source }}]</span>
    
    <!-- 日志级别图标 -->
    <span class="log-level-icon" :class="`icon-${log.level}`">{{ levelIcon }}</span>
    
    <!-- 日志内容 -->
    <span class="log-content" v-html="formattedContent"></span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { LogEntry } from '@/stores/logs'

interface LogEntryProps {
  log: LogEntry
  highlighted?: boolean
}

const props = withDefaults(defineProps<LogEntryProps>(), {
  highlighted: false
})

// 计算属性
const formattedTime = computed(() => {
  return new Date(props.log.timestamp).toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
})

const levelIcon = computed(() => {
  const icons = {
    info: 'INFO',
    success: 'OK',
    warning: 'WARN',
    error: 'ERR'
  }
  return icons[props.log.level] || 'INFO'
})

const formattedContent = computed(() => {
  let content = props.log.content
  
  // 应用颜色化规则（与原版LogTerminal一致）
  content = content.replace(/\[日志\]/g, '<span class="log-keyword">[日志]</span>')
  content = content.replace(/(失败|错误|Exception|Traceback)/g, '<span class="log-error-text">$1</span>')
  content = content.replace(/(成功|完成)/g, '<span class="log-success-text">$1</span>')
  content = content.replace(/(machineId 路径: .*)/g, '<span class="log-path">$1</span>')
  content = content.replace(/(machineId 内容: .*)/g, '<span class="log-content-text">$1</span>')
  
  // 高亮URL
  content = content.replace(/(https?:\/\/[^\s]+)/g, '<span class="log-url">$1</span>')
  
  // 高亮文件路径
  content = content.replace(/([A-Za-z]:\\[^\s]+|\/[^\s]+)/g, '<span class="log-file-path">$1</span>')
  
  return content
})
</script>

<style scoped>
.log-entry {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 2px 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  font-size: 0.95rem;
  line-height: 1.4;
  word-wrap: break-word;
  transition: background-color 0.2s ease;
}

.log-entry.log-highlighted {
  background-color: rgba(96, 165, 250, 0.1);
  border-radius: 4px;
  padding: 4px 8px;
  margin: 2px 0;
}

.log-timestamp {
  color: #6b7280;
  font-size: 0.85rem;
  font-weight: 500;
  flex-shrink: 0;
  min-width: 70px;
}

.log-source {
  color: #8b5cf6;
  font-size: 0.8rem;
  font-weight: 600;
  flex-shrink: 0;
  background: rgba(139, 92, 246, 0.1);
  padding: 1px 4px;
  border-radius: 3px;
}

.log-level-icon {
  flex-shrink: 0;
  font-size: 0.9rem;
}

.log-content {
  flex: 1;
  word-break: break-word;
}

/* 日志级别样式 */
.log-level-info {
  border-left: 3px solid #60a5fa;
  padding-left: 8px;
}

.log-level-success {
  border-left: 3px solid #10b981;
  padding-left: 8px;
}

.log-level-warning {
  border-left: 3px solid #f59e0b;
  padding-left: 8px;
}

.log-level-error {
  border-left: 3px solid #f87171;
  padding-left: 8px;
  background: rgba(248, 113, 113, 0.05);
}

/* 级别图标颜色 */
.icon-info { color: #60a5fa; }
.icon-success { color: #10b981; }
.icon-warning { color: #f59e0b; }
.icon-error { color: #f87171; }

/* 内容高亮样式 */
:deep(.log-keyword) {
  color: #60a5fa;
  font-weight: bold;
}

:deep(.log-error-text) {
  color: #f87171;
  font-weight: bold;
}

:deep(.log-success-text) {
  color: #10b981;
  font-weight: bold;
}

:deep(.log-path) {
  color: #facc15;
}

:deep(.log-content-text) {
  color: #b5f4a5;
}

:deep(.log-url) {
  color: #60a5fa;
  text-decoration: underline;
  cursor: pointer;
}

:deep(.log-url:hover) {
  color: #3b82f6;
}

:deep(.log-file-path) {
  color: #a78bfa;
  font-style: italic;
}

/* 悬停效果 */
.log-entry:hover {
  background-color: rgba(255, 255, 255, 0.02);
  border-radius: 4px;
  padding: 4px 8px;
  margin: 2px 0;
}

.log-entry:hover .log-timestamp {
  color: #9ca3af;
}
</style>
