// Main API endpoint - handles all requests
module.exports = (req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  // Handle preflight OPTIONS request
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // Log request details
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
  
  try {
    // Default response for any request
    const response = {
      "status": "success",
      "message": "Cursor Pro API is running",
      "version": "1.0.0",
      "timestamp": new Date().toISOString(),
      "endpoint": req.url,
      "method": req.method,
      "bypass": {
        "hasUpdate": false,
        "version": "0.42.4",
        "forceUpdate": false
      }
    };

    res.setHeader('Content-Type', 'application/json');
    res.status(200).json(response);
    
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({
      "error": "Internal server error",
      "message": error.message,
      "timestamp": new Date().toISOString()
    });
  }
}
