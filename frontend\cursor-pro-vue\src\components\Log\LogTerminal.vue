<template>
  <div class="log-terminal-container">
    <!-- 日志显示区域 -->
    <div 
      ref="logAreaRef"
      class="log-area"
      :class="{ 'log-page': isLogPage }"
      @scroll="handleScroll"
    >
      <div 
        v-for="log in logs" 
        :key="log.id"
        class="log-entry"
        v-html="formatLogContent(log)"
      ></div>
      
      <!-- 空状态 -->
      <div v-if="logs.length === 0" class="log-empty">
        [终端日志输出区]
      </div>
    </div>

    <!-- 回到底部按钮 -->
    <button 
      v-show="showScrollButton"
      class="scroll-to-bottom-btn"
      @click="scrollToBottom"
    >
      回到底部
    </button>

    <!-- 日志控制栏 -->
    <div v-if="showControls" class="log-controls">
      <button 
        class="log-control-btn"
        @click="handleClear"
        title="清空日志"
      >
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="3 6 5 6 21 6"></polyline>
          <path d="m19 6-2 14a2 2 0 0 1-2 2H9a2 2 0 0 1-2-2L5 6"></path>
          <path d="m10 11 0 6"></path>
          <path d="m14 11 0 6"></path>
        </svg>
        清空
      </button>
      
      <button 
        class="log-control-btn"
        @click="handleExport"
        title="导出日志"
      >
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
          <polyline points="7 10 12 15 17 10"></polyline>
          <line x1="12" y1="15" x2="12" y2="3"></line>
        </svg>
        导出
      </button>

      <div class="log-stats">
        <span class="log-count">{{ logs.length }} 条日志</span>
        <span v-if="errorCount > 0" class="error-count">{{ errorCount }} 错误</span>
        <span v-if="warningCount > 0" class="warning-count">{{ warningCount }} 警告</span>
      </div>

      <label class="auto-scroll-toggle">
        <input 
          type="checkbox" 
          v-model="autoScrollEnabled"
          @change="handleAutoScrollChange"
        />
        自动滚动
      </label>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { useLogStore, type LogEntry } from '@/stores/logs'

interface LogTerminalProps {
  logs?: LogEntry[]
  autoScroll?: boolean
  maxHeight?: string
  showControls?: boolean
  isLogPage?: boolean
}

interface LogTerminalEvents {
  'clear': []
  'export': []
  'scroll-change': [autoScroll: boolean]
}

const props = withDefaults(defineProps<LogTerminalProps>(), {
  logs: () => [],
  autoScroll: true,
  maxHeight: '380px',
  showControls: true,
  isLogPage: false
})

const emit = defineEmits<LogTerminalEvents>()

const logStore = useLogStore()

// 响应式引用
const logAreaRef = ref<HTMLElement>()
const showScrollButton = ref(false)
const autoScrollEnabled = ref(props.autoScroll)
const userScrolled = ref(false)

// 计算属性
const logs = computed(() => {
  return props.logs.length > 0 ? props.logs : logStore.logs
})

const errorCount = computed(() => {
  return logs.value.filter(log => log.level === 'error').length
})

const warningCount = computed(() => {
  return logs.value.filter(log => log.level === 'warning').length
})

// 方法
const formatLogContent = (log: LogEntry) => {
  let content = log.content
  
  // 应用美化的颜色化规则
  content = content.replace(/\[日志\]/g, '<span style="color:#60a5fa;font-weight:bold;">[日志]</span>')
  content = content.replace(/(失败|错误|Exception|Traceback)/g, '<span style="color:#f87171;font-weight:bold;">$1</span>')
  content = content.replace(/(成功|完成)/g, '<span style="color:#10b981;font-weight:bold;">$1</span>')
  content = content.replace(/(machineId 路径: .*)/g, '<span style="color:#facc15;">$1</span>')
  content = content.replace(/(machineId 内容: .*)/g, '<span style="color:#b5f4a5;">$1</span>')

  // 新增美化规则 - 隐藏框框字符
  content = content.replace(/═+/g, '')
  content = content.replace(/┌[─]+┐/g, '')
  content = content.replace(/└[─]+┘/g, '')
  content = content.replace(/│\s*/g, '  ')
  // 移除图标，使用文字标识
  content = content.replace(/🚀/g, '<span style="color:#f59e0b;">[START]</span>')
  content = content.replace(/✨/g, '<span style="color:#fbbf24;">[DONE]</span>')
  content = content.replace(/📊/g, '<span style="color:#3b82f6;">[DATA]</span>')
  content = content.replace(/💡/g, '<span style="color:#f59e0b;">[TIP]</span>')
  content = content.replace(/✅/g, '<span style="color:#10b981;">[OK]</span>')
  content = content.replace(/🔗|👤|📋|🔄/g, '<span style="color:#8b5cf6;">[INFO]</span>')
  content = content.replace(/(正常运行|连接成功|加载完成|获取完成|已启用)/g, '<span style="color:#10b981;font-weight:bold;">$1</span>')
  content = content.replace(/(未登录|未检测到)/g, '<span style="color:#f59e0b;">$1</span>')
  
  // 根据日志级别添加前缀颜色
  const timestamp = new Date(log.timestamp).toLocaleTimeString()
  const levelColors = {
    info: '#60a5fa',
    success: '#10b981', 
    warning: '#f59e0b',
    error: '#f87171'
  }
  
  const levelColor = levelColors[log.level] || '#e5e5e5'
  const prefix = `<span style="color:${levelColor};font-weight:bold;">[${timestamp}]</span> `
  
  return prefix + content
}

const handleScroll = () => {
  if (!logAreaRef.value) return
  
  const { scrollTop, scrollHeight, clientHeight } = logAreaRef.value
  const isAtBottom = scrollTop >= (scrollHeight - clientHeight - 5)
  
  userScrolled.value = !isAtBottom
  showScrollButton.value = userScrolled.value
  
  // 如果用户滚动了，暂时禁用自动滚动
  if (userScrolled.value && autoScrollEnabled.value) {
    logStore.setAutoScroll(false)
  }
}

const scrollToBottom = () => {
  if (!logAreaRef.value) return
  
  logAreaRef.value.scrollTop = logAreaRef.value.scrollHeight
  userScrolled.value = false
  showScrollButton.value = false
  
  // 重新启用自动滚动
  autoScrollEnabled.value = true
  logStore.setAutoScroll(true)
}

const handleClear = () => {
  emit('clear')
  if (props.logs.length === 0) {
    logStore.clearLogs()
  }
}

const handleExport = () => {
  emit('export')
  if (props.logs.length === 0) {
    logStore.exportLogs()
  }
}

const handleAutoScrollChange = () => {
  logStore.setAutoScroll(autoScrollEnabled.value)
  emit('scroll-change', autoScrollEnabled.value)
}

// 监听日志变化，自动滚动到底部
watch(logs, async () => {
  if (autoScrollEnabled.value && !userScrolled.value) {
    await nextTick()
    scrollToBottom()
  }
}, { deep: true })

// 监听store的autoScroll状态
watch(() => logStore.autoScroll, (newValue) => {
  autoScrollEnabled.value = newValue
})

// 组件挂载时初始化
onMounted(() => {
  autoScrollEnabled.value = logStore.autoScroll
  
  // 初始滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
})
</script>

<style scoped>
.log-terminal-container {
  position: relative;
}

/* 日志输出区 - 玻璃态动态效果 */
.log-area {
  position: relative;
  margin: 6px 2px 8px 2px;
  padding: 16px 18px;
  border-radius: 14px;
  z-index: 1111;
  overflow-y: auto;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  font-size: 0.95rem;
  white-space: pre;
  font-feature-settings: "liga" 0;
  font-variant-ligatures: none;
  line-height: 1.6;
  letter-spacing: 0.02em;
  color: #e2e8f0;
  box-shadow: 20px 20px 60px rgba(0, 0, 0, 0.3), -20px -20px 60px rgba(255, 255, 255, 0.05);
}

.log-area::before {
  content: '';
  position: absolute;
  top: 5px;
  left: 5px;
  right: 5px;
  bottom: 5px;
  z-index: -2;
  background: rgba(30, 41, 59, 0.95);
  backdrop-filter: blur(24px);
  border-radius: 10px;
  outline: 2px solid rgba(255, 255, 255, 0.1);
  pointer-events: none;
}

.log-area::after {
  content: '';
  position: absolute;
  z-index: -3;
  top: 50%;
  left: 50%;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  background: linear-gradient(45deg, #0ea5e9, #8b5cf6, #ec4899);
  opacity: 0.6;
  filter: blur(12px);
  animation: blob-bounce 5s infinite ease;
  pointer-events: none;
}

@keyframes blob-bounce {
  0% {
    transform: translate(-50%, -50%) translate3d(0, 0, 0);
  }
  25% {
    transform: translate(-50%, -50%) translate3d(100px, 0, 0);
  }
  50% {
    transform: translate(-50%, -50%) translate3d(100px, 100px, 0);
  }
  75% {
    transform: translate(-50%, -50%) translate3d(0, 100px, 0);
  }
  100% {
    transform: translate(-50%, -50%) translate3d(0, 0, 0);
  }
}

/* 终端日志页面需要固定高度 */
.log-area.log-page {
  min-height: 280px;
  max-height: 380px;
  overflow-y: auto;
}

/* 日志区域滚动条样式 */
.log-area::-webkit-scrollbar {
  width: 4px;
}

.log-area::-webkit-scrollbar-track {
  background: transparent;
}

.log-area::-webkit-scrollbar-thumb {
  background: rgba(16, 185, 129, 0.3);
  border-radius: 2px;
}

.log-area::-webkit-scrollbar-thumb:hover {
  background: rgba(16, 185, 129, 0.5);
}

.log-entry {
  margin-bottom: 2px;
  word-wrap: break-word;
}

.log-empty {
  color: #6b7280;
  font-style: italic;
  text-align: center;
  padding: 20px;
}

/* 回到底部按钮 - 与原版完全一致 */
.scroll-to-bottom-btn {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 12px;
  font-family: 'Fira Mono', 'Consolas', monospace;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.scroll-to-bottom-btn:hover {
  background: #059669;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

/* 日志控制栏 */
.log-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: #23272e;
  border-radius: 8px;
  margin-top: 8px;
  border: 1px solid #2d323a;
  font-family: 'Fira Mono', 'Consolas', monospace;
  font-size: 0.9rem;
}

.log-control-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: #374151;
  color: #e5e5e5;
  border: 1px solid #4b5563;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.8rem;
  font-family: inherit;
}

.log-control-btn:hover {
  background: #4b5563;
  border-color: #6b7280;
}

.log-stats {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
  font-size: 0.8rem;
}

.log-count {
  color: #94a3b8;
}

.error-count {
  color: #f87171;
  font-weight: 600;
}

.warning-count {
  color: #f59e0b;
  font-weight: 600;
}

.auto-scroll-toggle {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #94a3b8;
  font-size: 0.8rem;
  cursor: pointer;
}

.auto-scroll-toggle input[type="checkbox"] {
  margin: 0;
  cursor: pointer;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .log-area {
    margin: 8px 2px 0 2px;
    padding: 6px 6px;
  }
  
  .log-controls {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .log-stats {
    margin-left: 0;
    order: -1;
    width: 100%;
  }
}
</style>
