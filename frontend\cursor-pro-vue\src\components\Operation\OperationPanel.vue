<template>
  <div class="operation-panel">
    <!-- 快速操作区域 -->
    <div class="hero-section">
      <div class="hero-title">
        <div class="hero-text">一键摆烂</div>
      </div>

      <div class="hero-container">
        <div class="hero-actions">
          <div
            class="flip-card"
            @click="handleOperation('auto-workflow')"
            :class="{ 'loading': loadingStates['auto-workflow'] }"
          >
            <div class="first-content">
              <span>完整流程</span>
            </div>
            <div class="second-content">
              <span>自动化全流程</span>
            </div>
          </div>

          <div
            class="flip-card"
            @click="handleOperation('auto-register')"
            :class="{ 'loading': loadingStates['auto-register'] }"
          >
            <div class="first-content">
              <span>自动注册</span>
            </div>
            <div class="second-content">
              <span>临时邮箱注册</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分隔线 -->
    <div class="section-divider"></div>

    <!-- 功能模块区域 -->
    <div class="modules-container">
      <div class="dashboard-modules">

        <!-- 系统配置模块 -->
        <div class="cartoon">
          <div class="card-header">
            <div class="icon">
              <svg viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="3"></circle>
                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
              </svg>
            </div>
            <h3 class="card-title">系统配置</h3>
          </div>
          <div class="card-body">
            <button
              v-for="action in systemActions"
              :key="action.id"
              class="card-button"
              :disabled="loadingStates[action.id]"
              @click="handleOperation(action.id)"
            >
              {{ loadingStates[action.id] ? '处理中...' : action.text }}
            </button>
          </div>
        </div>

        <!-- 机器ID工具模块 -->
        <div class="cartoon">
          <div class="card-header">
            <div class="icon">
              <svg viewBox="0 0 24 24">
                <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"></path>
              </svg>
            </div>
            <h3 class="card-title">机器ID工具</h3>
          </div>
          <div class="card-body">
            <button
              v-for="action in machineIdActions"
              :key="action.id"
              class="card-button"
              :disabled="loadingStates[action.id]"
              @click="handleOperation(action.id)"
            >
              {{ loadingStates[action.id] ? '处理中...' : action.text }}
            </button>
          </div>
        </div>

        <!-- 检查工具模块 -->
        <div class="cartoon">
          <div class="card-header">
            <div class="icon">
              <svg viewBox="0 0 24 24">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.35-4.35"></path>
              </svg>
            </div>
            <h3 class="card-title">检查工具</h3>
          </div>
          <div class="card-body">
            <button
              v-for="action in checkActions"
              :key="action.id"
              class="card-button"
              :disabled="loadingStates[action.id]"
              @click="handleOperation(action.id)"
            >
              {{ loadingStates[action.id] ? '处理中...' : action.text }}
            </button>
          </div>
        </div>

        <!-- 快捷工具模块 -->
        <div class="cartoon">
          <div class="card-header">
            <div class="icon">
              <svg viewBox="0 0 24 24">
                <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"></path>
              </svg>
            </div>
            <h3 class="card-title">快捷工具</h3>
          </div>
          <div class="card-body">
            <button
              v-for="action in quickToolsActions"
              :key="action.id"
              class="card-button"
              :disabled="loadingStates[action.id]"
              @click="handleOperation(action.id)"
            >
              {{ loadingStates[action.id] ? '处理中...' : action.text }}
            </button>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue'

interface OperationPanelEvents {
  'operation': [type: string]
}

interface ActionItem {
  id: string
  text: string
}

const emit = defineEmits<OperationPanelEvents>()

// 加载状态管理
const loadingStates = reactive<Record<string, boolean>>({})

// 操作按钮配置
const systemActions: ActionItem[] = [
  { id: 'show-config', text: '显示配置' },
  { id: 'init-cursor', text: '初始化Cursor' }
]

const machineIdActions: ActionItem[] = [
  { id: 'reset-machine-code', text: '重置机器码' }
]

const checkActions: ActionItem[] = [
  { id: 'auto-update', text: '禁用自动更新' },
  { id: 'bypass-version', text: '绕过版本检查' }
]

const quickToolsActions: ActionItem[] = [
  { id: 'get-random-config', text: '获取随机配置' },
  { id: 'get-verification-code', text: '获取验证码' }
]





// 处理操作
const handleOperation = (type: string) => {
  if (loadingStates[type]) return
  
  loadingStates[type] = true
  emit('operation', type)
  
  // 模拟操作完成后重置加载状态
  setTimeout(() => {
    loadingStates[type] = false
  }, 2000)
}
</script>

<style scoped>
.operation-panel {
  padding: 24px;
}

/* 快速操作区域 */
.hero-section {
  margin-bottom: 24px;
}

/* 分隔线 */
.section-divider {
  height: 4px;
  background: linear-gradient(90deg, transparent 0%, #3d424a 20%, #60a5fa 50%, #3d424a 80%, transparent 100%);
  margin: 32px 0;
  border-radius: 2px;
  position: relative;
  overflow: hidden;
  animation: dividerPulse 3s ease-in-out infinite;
}

.section-divider::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 12px;
  height: 12px;
  background: #60a5fa;
  border-radius: 50%;
  box-shadow: 0 0 20px rgba(96, 165, 250, 0.8);
  animation: dotPulse 2s ease-in-out infinite;
}

.section-divider::after {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  width: 0%;
  height: 100%;
  background: linear-gradient(90deg, transparent 0%, rgba(96, 165, 250, 0.8) 20%, rgba(96, 165, 250, 1) 50%, rgba(96, 165, 250, 0.8) 80%, transparent 100%);
  transform: translateX(-50%);
  animation: expandFromCenter 3s ease-in-out infinite;
}

@keyframes dividerPulse {
  0%, 100% {
    opacity: 0.8;
    transform: scaleY(1);
  }
  50% {
    opacity: 1;
    transform: scaleY(1.2);
  }
}

@keyframes dotPulse {
  0%, 100% {
    transform: translateX(-50%) scale(1);
    box-shadow: 0 0 20px rgba(96, 165, 250, 0.8);
  }
  50% {
    transform: translateX(-50%) scale(1.3);
    box-shadow: 0 0 30px rgba(96, 165, 250, 1);
  }
}

@keyframes expandFromCenter {
  0% {
    width: 0%;
    opacity: 0;
  }
  20% {
    width: 30%;
    opacity: 0.6;
  }
  50% {
    width: 80%;
    opacity: 1;
  }
  80% {
    width: 100%;
    opacity: 0.8;
  }
  100% {
    width: 0%;
    opacity: 0;
  }
}

.hero-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
  color: #60a5fa;
  font-size: 1.4rem;
  font-weight: 700;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* 一键摆烂背景容器 */
.hero-container {
  background: #23272e;
  border-radius: 12px;
  padding: 24px;
  border: 1.5px solid #2d323a;
}

.hero-icon {
  font-size: 1.6rem;
}

.hero-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(254px, 1fr));
  gap: 32px;
  justify-items: center;
}

/* 翻转卡片样式 */
.flip-card {
  width: 280px;
  height: 190px;
  background: #07182E;
  position: relative;
  display: flex;
  place-content: center;
  place-items: center;
  overflow: hidden;
  border-radius: 20px;
  transition: all 0.4s ease-out;
  cursor: pointer;
  font-size: 30px;
  font-weight: 900;
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
}

.flip-card::before {
  content: '';
  position: absolute;
  width: 100px;
  background-image: linear-gradient(180deg, rgb(0, 183, 255), rgb(255, 48, 255));
  height: 380px;
  animation: rotBGimg 3s linear infinite;
  transition: all 0.2s linear;
  left: 50%;
  top: 50%;
  transform-origin: center;
}

@keyframes rotBGimg {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.flip-card::after {
  content: '';
  position: absolute;
  background: #07182E;
  inset: 5px;
  border-radius: 15px;
}

.flip-card:hover {
  transform: scale(1.1);
}

.flip-card:hover::before {
  background: conic-gradient(from 0deg, rgb(81, 255, 0) 0deg, purple 90deg, rgb(81, 255, 0) 180deg, purple 270deg, rgb(81, 255, 0) 360deg);
  animation: rotBGimg 2s linear infinite;
}

.first-content {
  height: 100%;
  width: 100%;
  transition: all 0.4s;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 1;
  border-radius: 15px;
  color: white;
  font-size: 1.2rem;
  font-weight: 700;
  text-align: center;
  position: relative;
  z-index: 1;
}

.flip-card:hover .first-content {
  height: 0px;
  opacity: 0;
}

.second-content {
  height: 0%;
  width: 100%;
  opacity: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 15px;
  transition: all 0.4s;
  font-size: 0px;
  transform: rotate(90deg) scale(-1);
  color: white;
  text-align: center;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.flip-card:hover .second-content {
  opacity: 1;
  height: 100%;
  font-size: 1rem;
  transform: rotate(0deg);
}

.flip-card.loading {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.flip-card.loading:hover {
  transform: none;
  border-radius: 10px;
}



/* 功能模块背景容器 */
.modules-container {
  background: #23272e;
  border-radius: 12px;
  padding: 24px;
  border: 1.5px solid #2d323a;
}

/* 功能模块区域 */
.dashboard-modules {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

/* 新的模块卡片样式 */
.module-card {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  border: 2px solid #000000;
  box-shadow: 4px 4px #000000;
  transition: all 0.2s ease;
  margin-bottom: 20px;
}

.module-card:hover {
  transform: translateY(-2px);
  box-shadow: 6px 6px #000000;
}

/* 模块头部样式 */
.module-header {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  color: #ffffff;
  font-weight: 700;
  border-bottom: 2px solid #000000;
}

.system-header {
  background: #87CEEB; /* 蓝色 */
}

.machine-header {
  background: #FFB6C1; /* 粉色 */
}

.check-header {
  background: #98FB98; /* 绿色 */
}

.module-icon {
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.module-title {
  font-size: 1.1rem;
  font-weight: 700;
  color: #000000;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* 模块内容区域 */
.module-content {
  padding: 20px;
  background: #ffffff;
}

.module-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.module-button {
  background: #007BFF;
  color: #ffffff;
  border: 2px solid #000000;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 2px 2px #000000;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

.module-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 3px 3px #000000;
}

.module-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #6c757d;
  transform: none;
  box-shadow: 2px 2px #000000;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .hero-actions {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .operation-panel {
    padding: 16px;
  }
  
  .hero-actions {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .hero-card {
    width: 100%;
    max-width: 300px;
    height: 180px;
  }
  
  .dashboard-modules {
    grid-template-columns: 1fr;
  }
  
  .module-actions {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 强制重置背景样式但保留动画 */
.hero-card .hero-card-title,
.hero-card .logo2,
.hero-card .content .logo .logo2 {
  background: none !important;
  background-color: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
  border-radius: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* trail元素保持动画能力 */
.hero-card .trail {
  background: none;
  background-color: transparent;
}

/* 确保hover时trail动画正常工作 */
.hero-card:hover .trail {
  animation: trail 1s ease-in-out !important;
}

/* 海浪效果样式 */
.hero-card.e-card {
  background: transparent;
  overflow: hidden;
}

.wave {
  position: absolute;
  width: 540px;
  height: 700px;
  opacity: 0.6;
  left: 0;
  top: 0;
  margin-left: -50%;
  margin-top: -70%;
  background: linear-gradient(744deg, #af40ff, #5b42f3 60%, #00ddeb);
  border-radius: 40%;
  animation: wave 55s infinite linear;
}

.wave:nth-child(2),
.wave:nth-child(3) {
  top: 210px;
}

.playing .wave {
  border-radius: 40%;
  animation: wave 3000ms infinite linear;
}

.playing .wave:nth-child(2) {
  animation-duration: 4000ms;
}

.wave:nth-child(2) {
  animation-duration: 50s;
}

.playing .wave:nth-child(3) {
  animation-duration: 5000ms;
}

.wave:nth-child(3) {
  animation-duration: 45s;
}

@keyframes wave {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 卡通动漫风卡片样式 */
.cartoon {
    background-color: #ffffff;
    border: 3px solid #000;
    box-shadow: 8px 8px 0px #000;
    color: #000;
    border-radius: 10px;
    width: 100%;
    overflow: hidden;
    transition: all 0.4s ease;
}

.cartoon .card-header {
    display: flex;
    align-items: center;
    padding: 20px;
    gap: 10px;
    background-color: #ffd700;
    border-bottom: 3px solid #000;
}

.cartoon .icon svg {
    width: 30px;
    height: 30px;
    fill: #000;
    stroke: #000;
    stroke-width: 1.5;
    transition: transform 0.3s ease;
}

.cartoon:hover .icon svg {
    transform: rotate(-15deg) scale(1.1);
}

.cartoon .card-title {
    font-size: 20px;
    color: #000;
    font-weight: 700;
    text-shadow: 2px 2px 0px rgba(255,255,255,0.5);
    margin: 0;
}

.cartoon .card-body {
    padding: 20px;
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.cartoon .card-button {
    padding: 12px 20px;
    font-size: 16px;
    background-color: #ff4757;
    color: #fff;
    border: 3px solid #000;
    border-radius: 8px;
    box-shadow: 4px 4px 0px #000;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
    text-align: center;
}

.cartoon .card-button:hover:not(:disabled) {
    background-color: #ff6b81;
    transform: translate(2px, 2px);
    box-shadow: 2px 2px 0px #000;
}

.cartoon .card-button:active {
    transform: translate(4px, 4px);
    box-shadow: 0px 0px 0px #000;
}

.cartoon .card-button:disabled {
    background-color: #95a5a6;
    cursor: not-allowed;
    opacity: 0.7;
}

.cartoon .card-button:disabled:hover {
    transform: none;
    box-shadow: 4px 4px 0px #000;
}

/* 不同卡片的颜色主题 */
.cartoon:nth-child(1) .card-header {
    background-color: #74b9ff; /* 蓝色 - 系统配置 */
}

.cartoon:nth-child(1) .card-button {
    background-color: #0984e3;
}

.cartoon:nth-child(1) .card-button:hover:not(:disabled) {
    background-color: #74b9ff;
}

.cartoon:nth-child(2) .card-header {
    background-color: #fd79a8; /* 粉色 - 机器ID工具 */
}

.cartoon:nth-child(2) .card-button {
    background-color: #e84393;
}

.cartoon:nth-child(2) .card-button:hover:not(:disabled) {
    background-color: #fd79a8;
}

.cartoon:nth-child(3) .card-header {
    background-color: #55efc4; /* 绿色 - 检查工具 */
}

.cartoon:nth-child(3) .card-button {
    background-color: #00b894;
}

.cartoon:nth-child(3) .card-button:hover:not(:disabled) {
    background-color: #55efc4;
}
</style>
