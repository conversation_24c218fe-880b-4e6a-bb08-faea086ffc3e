import sys
import os
# 将项目根目录添加到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import requests
import re
import datetime
import time
from typing import Optional
from email_tabs.email_tab_interface import EmailTabInterface

class TempMailPlusTab(EmailTabInterface):
    """Implementation of EmailTabInterface for tempmail.plus"""
    
    def __init__(self, email: str, epin: str, translator=None, 
                polling_interval: int = 2, max_attempts: int = 10):
        """Initialize TempMailPlusTab
        
        Args:
            email: The email address to check
            epin: The epin token for authentication
            translator: Optional translator for internationalization
            polling_interval: Time in seconds between polling attempts
            max_attempts: Maximum number of polling attempts
        """
        self.email = email
        self.epin = epin
        self.translator = translator
        self.base_url = "https://tempmail.plus/api"
        self.headers = {
            'accept': 'application/json',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'referer': 'https://tempmail.plus/zh/',
            'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
            'x-requested-with': 'XMLHttpRequest'
        }
        self.cookies = {'email': email}
        self._cached_mail_id = None  # Cache for mail_id
        self._cached_verification_code = None  # Cache for verification code
        
        # Polling configuration
        self.polling_interval = polling_interval
        self.max_attempts = max_attempts
        self.current_attempt = 0
        
    def refresh_inbox(self) -> None:
        """Refresh the email inbox"""
        pass
            
    def check_for_cursor_email(self) -> bool:
        """Check if there is a new email and immediately retrieve verification code
        
        Returns:
            bool: True if new email found and verification code retrieved, False otherwise
        """
        # Reset attempt counter
        self.current_attempt = 0
            
        # Polling logic
        while self.current_attempt < self.max_attempts:
            found = self._check_email_once()
            if found:
                # Successfully found email and retrieved verification code
                self.current_attempt = 0  # Reset counter for next use
                return True
                
            # Not found, continue polling
            self.current_attempt += 1
            if self.current_attempt < self.max_attempts:
                # 直接使用中文显示轮询信息，不依赖翻译器
                print(f"轮询邮件中: 第 {self.current_attempt}/{self.max_attempts} 次尝试")
                time.sleep(self.polling_interval)
            
        # Exceeded maximum attempts
        print(f"已达到最大尝试次数 ({self.max_attempts})，未找到验证邮件。")
        return False
        
    def _check_email_once(self) -> bool:
        """Single attempt to check for email
        
        Returns:
            bool: True if new email found and verification code retrieved, False otherwise
        """
        try:
            params = {
                'email': self.email,
                'epin': self.epin
            }
            response = requests.get(
                f"{self.base_url}/mails",
                params=params,
                headers=self.headers,
                cookies=self.cookies
            )
            response.raise_for_status()
            
            data = response.json()
            if data.get('result') and data.get('mail_list'):
                # Check all emails, not just new ones (since forwarded emails may not be marked as new)
                for mail in data['mail_list']:
                    self._cached_mail_id = mail.get('mail_id')
                    print(f"🔍 检查邮件ID: {self._cached_mail_id}, is_new: {mail.get('is_new')}")

                    # Try to retrieve verification code from this email
                    verification_code = self._extract_verification_code()
                    if verification_code:
                        self._cached_verification_code = verification_code
                        print(f"✅ 在邮件ID {self._cached_mail_id} 中找到验证码: {verification_code}")
                        return True
                    else:
                        print(f"❌ 邮件ID {self._cached_mail_id} 中未找到验证码")
            return False
        except Exception as e:
            print(f"检查邮件失败: {str(e)}")
            return False
    
    def _extract_verification_code(self) -> str:
        """Extract verification code from email content
        
        Returns:
            str: The verification code if found, empty string otherwise
        """
        try:
            if not self._cached_mail_id:
                return ""
                
            params = {
                'email': self.email,
                'epin': self.epin
            }
            response = requests.get(
                f"{self.base_url}/mails/{self._cached_mail_id}",
                params=params,
                headers=self.headers,
                cookies=self.cookies
            )
            response.raise_for_status()
            
            data = response.json()
            if not data.get('result'):
                return ""
                
            # Get email content
            from_mail = data.get('from_mail', '')
            text = data.get('text', '')
            html = data.get('html', '')
            subject = data.get('subject', '')

            # Check if this is a Cursor email by checking sender OR content
            is_cursor_email = False

            # Method 1: Check sender email
            if 'cursor' in from_mail.lower():
                is_cursor_email = True
                print(f"✅ 通过发件人识别为Cursor邮件: {from_mail}")

            # Method 2: Check content for Cursor-related keywords
            if not is_cursor_email:
                content_to_check = (text + ' ' + html + ' ' + subject).lower()
                cursor_keywords = ['cursor', 'verify your email', 'verification code', '<EMAIL>', 'verify your email address']
                found_keywords = [kw for kw in cursor_keywords if kw in content_to_check]
                if found_keywords:
                    is_cursor_email = True
                    print(f"✅ 通过内容识别为Cursor邮件，关键词: {found_keywords}")

            if not is_cursor_email:
                print(f"❌ 不是Cursor邮件 - 发件人: {from_mail}, 主题: {subject}")
                return ""

            print(f"🔍 开始提取验证码...")

            # Extract verification code using multiple methods
            verification_code = ""

            # Method 1: Original pattern (two newlines)
            match = re.search(r'\n\n(\d{6})\n\n', text)
            if match:
                verification_code = match.group(1)
                print(f"✅ 方法1成功: {verification_code}")

            # Method 2: Single line with 6 digits
            if not verification_code:
                lines = text.split('\n')
                for line in lines:
                    line = line.strip()
                    if re.match(r'^\d{6}$', line):
                        verification_code = line
                        print(f"✅ 方法2成功: {verification_code}")
                        break

            # Method 3: Any 6 digits in text (as fallback)
            if not verification_code:
                match = re.search(r'\b(\d{6})\b', text)
                if match:
                    verification_code = match.group(1)
                    print(f"✅ 方法3成功: {verification_code}")

            if verification_code:
                print(f"🎯 最终提取的验证码: {verification_code}")
            else:
                print(f"❌ 未能提取验证码")

            return verification_code
        except Exception as e:
            print(f"提取验证码失败: {str(e)}")
            return ""
            
    def get_verification_code(self) -> str:
        """Get the verification code from cache
        
        Returns:
            str: The cached verification code if available, empty string otherwise
        """
        return self._cached_verification_code or ""

    @classmethod
    def create_new_email(cls, translator=None):
        """Create a new temporary email account

        Returns:
            TempMailPlusTab: Instance with new email account, or None if failed
        """
        try:
            import requests

            # Generate new email
            response = requests.get("https://tempmail.plus/api/mails/automail")
            response.raise_for_status()

            data = response.json()
            if data.get('result') and data.get('mail'):
                email = data['mail']
                epin = data.get('epin', '')

                print(f"✅ 新邮箱生成成功: {email}")
                return cls(email, epin, translator)
            else:
                print("❌ 邮箱生成失败: API返回无效数据")
                return None

        except Exception as e:
            print(f"❌ 邮箱生成失败: {str(e)}")
            return None

if __name__ == "__main__":
    import os
    import sys
    import configparser
    
    # 导入项目根目录的config模块
    from config import get_config
    
    # Try to import translator
    try:
        from main import Translator
        translator = Translator()
        print("成功加载翻译器")
    except ImportError:
        print("无法加载翻译器，将使用默认文本")
        translator = None
    
    config = get_config(translator)
    email = ""
    epin = ""
    
    # 首先尝试从配置文件获取
    if config and config.has_section('TempMailPlus'):
        email = config.get('TempMailPlus', 'email', fallback='')
        epin = config.get('TempMailPlus', 'epin', fallback='')
        
    # 如果配置文件中没有设置或值为空，则提示用户输入
    if not email:
        print("\n未在配置文件中找到邮箱地址或为空，请手动输入：")
        email = input("请输入 TempMailPlus 邮箱地址: ").strip()
    
    if not epin:
        print("\n未在配置文件中找到 epin 码或为空，请手动输入：")
        epin = input("请输入 TempMailPlus epin 码: ").strip()
    
    # 验证输入是否有效
    if not email or not epin:
        print("错误：邮箱地址和 epin 码不能为空")
        sys.exit(1)
    
    # 询问是否保存到配置文件
    if not (config and config.has_section('TempMailPlus') and config.get('TempMailPlus', 'email', fallback='') == email and config.get('TempMailPlus', 'epin', fallback='') == epin):
        save_to_config = input("\n是否将邮箱和 epin 保存到配置文件？(y/n): ").strip().lower()
        if save_to_config == 'y':
            try:
                # 确保配置目录存在
                config_dir = os.path.join(os.path.expanduser("~"), "Documents", ".cursor-pro")
                os.makedirs(config_dir, exist_ok=True)
                
                # 读取现有配置或创建新配置
                config_file = os.path.join(config_dir, "config.ini")
                if not config:
                    config = configparser.ConfigParser()
                
                # 确保 TempMailPlus 部分存在
                if not config.has_section('TempMailPlus'):
                    config.add_section('TempMailPlus')
                
                # 更新配置
                config.set('TempMailPlus', 'enabled', 'true')
                config.set('TempMailPlus', 'email', email)
                config.set('TempMailPlus', 'epin', epin)
                
                # 保存配置
                with open(config_file, 'w', encoding='utf-8') as f:
                    config.write(f)
                
                print(f"已将设置保存到配置文件: {config_file}")
            except Exception as e:
                print(f"保存配置失败: {str(e)}")
    
    try:
        print(f"\n配置的电子邮件: {email}")
        
        # Initialize TempMailPlusTab, pass translator
        mail_tab = TempMailPlusTab(email, epin, translator)
        
        # Check if there is a Cursor email
        print("检查Cursor验证电子邮件...")
        if mail_tab.check_for_cursor_email():
            print("找到Cursor验证电子邮件")
            
            # Get verification code
            verification_code = mail_tab.get_verification_code()
            if verification_code:
                print(f"验证码: {verification_code}")
            else:
                print("无法获取验证码")
        else:
            print("未找到Cursor验证电子邮件")
            
    except configparser.Error as e:
        print(f"配置文件错误: {str(e)}")
    except Exception as e:
        print(f"发生错误: {str(e)}")
        import traceback
        traceback.print_exc() 