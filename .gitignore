.vercel
node_modules/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt
.DS_Store
Thumbs.db

# Electron build outputs
dist/
dist-electron/
dist-electron-*/
release/
release-*/
installer/
*.asar
*.exe
*.dmg
*.AppImage
*.deb
*.rpm

# Vue build outputs
cursor-pro-vue/dist/
cursor-pro-vue/dist-electron/
cursor-pro-vue/dist-electron-*/
cursor-pro-vue/release/
cursor-pro-vue/release-*/
cursor-pro-vue/installer/

# Logs
logs/
*.log

# ===== 用户数据文件 =====
# 这些文件包含用户的个人数据，不应该提交到版本控制

# 账户数据文件
cursor_accounts.txt
cursor_accounts_backup.txt
cursor_pro.db

# 配置文件
config.ini
settings.json
register_config.json

# 备份文件
*.backup
accounts_backup_*.txt
accounts_restore_backup_*.txt
backup_*/
backups/

# 数据文件
block_domain.txt

# 临时文件
temp/
cache/
*.tmp
*.temp

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 迁移相关文件
migration_backup_*/
migration_report_*.json

# 用户特定的日志
app.log
debug.log
error.log
