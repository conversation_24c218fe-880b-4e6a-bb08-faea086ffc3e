<template>
  <FormCard title="注册选项">
    <div class="form-fields">
      <div class="form-field">
        <label class="field-label">地区</label>
        <select 
          class="field-select" 
          :value="region" 
          @change="updateRegion"
        >
          <option value="US">美国 (US)</option>
          <option value="UK">英国 (UK)</option>
          <option value="CA">加拿大 (CA)</option>
          <option value="AU">澳大利亚 (AU)</option>
          <option value="DE">德国 (DE)</option>
          <option value="FR">法国 (FR)</option>
          <option value="JP">日本 (JP)</option>
          <option value="KR">韩国 (KR)</option>
        </select>
      </div>
      
      <div class="form-field">
        <label class="field-label">注册模式</label>
        <select 
          class="field-select" 
          :value="registerMode" 
          @change="updateRegisterMode"
        >
          <option value="auto">自动模式</option>
          <option value="manual">手动模式</option>
          <option value="batch">批量模式</option>
        </select>
      </div>
    </div>
  </FormCard>
</template>

<script setup lang="ts">
import FormCard from '../Common/FormCard.vue'

// Props定义
interface Props {
  region: string
  registerMode: string
}

// Emits定义
interface Emits {
  (e: 'update:region', value: string): void
  (e: 'update:registerMode', value: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 更新方法
const updateRegion = (event: Event) => {
  const target = event.target as HTMLSelectElement
  emit('update:region', target.value)
}

const updateRegisterMode = (event: Event) => {
  const target = event.target as HTMLSelectElement
  emit('update:registerMode', target.value)
}
</script>

<style scoped>
.form-fields {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field-label {
  color: #006aaaff;
  font-weight: bold;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  font-size: 1.15em;
  text-shadow: 0em 0.1em 0em var(--color2);
}

.field-select {
  background-color: white;
  border: none;
  border-right: 5px solid #ff7c90ff;
  border-bottom: 5px solid #ff7c90ff;
  border-bottom-right-radius: 25em;
  height: 2.5em;
  width: 90%;
  transition: all 0.5s ease;
  color: #ff7c90ff;
  font-weight: bold;
  padding-left: 0.5em;
  font-size: 1.05em;
  box-shadow: 1em 0em 0em;
  outline: none;
  cursor: pointer;
}

.field-select:hover {
  width: 100%;
  background-color: white;
  box-shadow: 0em 0em 0em;
}

.field-select:focus {
  width: 100%;
  background-color: white;
  box-shadow: 0em 0em 0em;
  border-color: #006aaaff;
}

.field-select option {
  color: #333;
  background-color: white;
  padding: 0.5em;
}
</style>
