/**
 * 日志状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 日志类型
export interface LogEntry {
  id: number
  timestamp: number
  level: 'info' | 'success' | 'warning' | 'error'
  content: string
  source?: string
}

export const useLogStore = defineStore('logs', () => {
  // 状态
  const logs = ref<LogEntry[]>([])
  const maxLogs = ref(1000) // 最大日志条数
  const autoScroll = ref(true)
  const logCounter = ref(0)
  const initialized = ref(false) // 初始化标志
  const dashboardMounted = ref(false) // Dashboard挂载标志

  // 计算属性
  const logCount = computed(() => logs.value.length)
  
  const errorCount = computed(() => 
    logs.value.filter(log => log.level === 'error').length
  )
  
  const warningCount = computed(() => 
    logs.value.filter(log => log.level === 'warning').length
  )

  // 方法
  const addLog = (level: LogEntry['level'], content: string, source?: string) => {
    const logEntry: LogEntry = {
      id: ++logCounter.value,
      timestamp: Date.now(),
      level,
      content,
      source
    }

    logs.value.push(logEntry)

    // 限制日志数量
    if (logs.value.length > maxLogs.value) {
      logs.value.shift()
    }

    console.log(`[Log ${level.toUpperCase()}]`, content)
  }

  const addInfo = (content: string, source?: string) => {
    addLog('info', content, source)
  }

  const addSuccess = (content: string, source?: string) => {
    addLog('success', content, source)
  }

  const addWarning = (content: string, source?: string) => {
    addLog('warning', content, source)
  }

  const addError = (content: string, source?: string) => {
    addLog('error', content, source)
  }

  const clearLogs = () => {
    console.warn('🗑️ 清空日志被调用！当前日志数量:', logs.value.length)
    console.trace('🔍 调用堆栈:')
    logs.value = []
    logCounter.value = 0
  }

  const exportLogs = () => {
    const logText = logs.value
      .map(log => {
        const time = new Date(log.timestamp).toLocaleString()
        const source = log.source ? `[${log.source}]` : ''
        return `[${time}] ${source} ${log.level.toUpperCase()}: ${log.content}`
      })
      .join('\n')

    // 创建下载链接
    const blob = new Blob([logText], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `cursor-logs-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    addSuccess('日志已导出')
  }

  const setAutoScroll = (enabled: boolean) => {
    autoScroll.value = enabled
  }

  const setMaxLogs = (max: number) => {
    maxLogs.value = max
    
    // 如果当前日志数超过新的限制，删除旧日志
    if (logs.value.length > max) {
      logs.value = logs.value.slice(-max)
    }
  }

  // 过滤日志
  const getLogsByLevel = (level: LogEntry['level']) => {
    return logs.value.filter(log => log.level === level)
  }

  const getLogsBySource = (source: string) => {
    return logs.value.filter(log => log.source === source)
  }

  const getRecentLogs = (count: number) => {
    return logs.value.slice(-count)
  }

  // 初始化时添加欢迎日志
  const init = () => {
    // 只初始化一次
    if (initialized.value) {
      console.log('📝 日志存储已初始化，跳过重复初始化')
      return
    }

    console.log('📝 首次初始化日志存储')
    initialized.value = true

    // 不再添加默认日志，保持日志区域干净
    // 用户操作时会自动添加相关日志
  }

  // Dashboard初始化日志（只执行一次）
  const initDashboard = () => {
    if (dashboardMounted.value) {
      console.log('📊 Dashboard已初始化，跳过重复日志')
      return
    }

    console.log('📊 首次初始化Dashboard日志')
    dashboardMounted.value = true

    addInfo('🚀 Dashboard 页面已加载', 'Dashboard')
    addInfo('📊 开始初始化账户数据...', 'Dashboard')
  }



  return {
    // 状态
    logs,
    maxLogs,
    autoScroll,

    // 计算属性
    logCount,
    errorCount,
    warningCount,

    // 方法
    addLog,
    addInfo,
    addSuccess,
    addWarning,
    addError,
    clearLogs,
    exportLogs,
    setAutoScroll,
    setMaxLogs,
    getLogsByLevel,
    getLogsBySource,
    getRecentLogs,
    init,
    initDashboard
  }
})
