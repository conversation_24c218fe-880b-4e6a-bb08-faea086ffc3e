/**
 * 账户状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { RealAPI } from '@/api/real-api'

// 定义账户信息类型
interface AccountInfo {
  email: string
  plan: string
  trial_days: number
  pro_used: number
  pro_total: number
  pro_percent: number
  basic_total: string
}

export const useAccountStore = defineStore('account', () => {
  // API实例
  const realAPI = new RealAPI()

  // 状态
  const currentAccount = ref<AccountInfo>({
    email: '未登录',
    plan: '-',
    trial_days: 0,
    pro_used: 0,
    pro_total: 999,
    pro_percent: 0,
    basic_total: 'No Limit'
  })

  const registeredAccounts = ref<string[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const isLoggedIn = computed(() => {
    return currentAccount.value.email !== '未登录'
  })

  const planType = computed(() => {
    switch (currentAccount.value.plan) {
      case 'Pro':
        return 'success'
      case 'Free':
        return 'info'
      default:
        return 'warning'
    }
  })

  const usageStatus = computed(() => {
    const percent = currentAccount.value.pro_percent
    if (percent >= 90) return 'danger'
    if (percent >= 70) return 'warning'
    return 'success'
  })

  // 操作方法
  const fetchAccountInfo = async () => {
    loading.value = true
    error.value = null
    
    try {
      const accountInfo = await realAPI.getAccountInfo()
      currentAccount.value = accountInfo
      console.log('[Store] 账户信息已更新:', accountInfo)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取账户信息失败'
      console.error('[Store] 获取账户信息失败:', err)
    } finally {
      loading.value = false
    }
  }

  const fetchRegisteredAccounts = async () => {
    try {
      const accounts = await realAPI.getRegisteredAccounts()
      // callAPI已经返回解析好的数据，不需要再次JSON.parse
      registeredAccounts.value = accounts || []
      console.log('[Store] 注册账户列表已更新:', accounts)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取注册账户失败'
      console.error('[Store] 获取注册账户失败:', err)
    }
  }

  const autoRegister = async () => {
    loading.value = true
    error.value = null
    
    try {
      const result = await realAPI.autoRegister()
      console.log('[Store] 自动注册结果:', result)
      
      // 注册成功后刷新账户信息
      await fetchAccountInfo()
      await fetchRegisteredAccounts()
      
      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : '自动注册失败'
      console.error('[Store] 自动注册失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const saveAccountInfo = async () => {
    loading.value = true
    error.value = null

    try {
      const result = await realAPI.saveAccountInfo()
      console.log('[Store] 保存账户信息结果:', result)
      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : '保存账户信息失败'
      console.error('[Store] 保存账户信息失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateAccountInfo = (data: any) => {
    // 更新当前账户信息
    if (data.email) {
      currentAccount.value.email = data.email
    }
    console.log('[Store] 账户信息已更新:', data)
  }

  const clearError = () => {
    error.value = null
  }

  // 初始化
  const init = async () => {
    await fetchAccountInfo()
    await fetchRegisteredAccounts()
  }

  return {
    // 状态
    currentAccount,
    registeredAccounts,
    loading,
    error,
    
    // 计算属性
    isLoggedIn,
    planType,
    usageStatus,
    
    // 方法
    fetchAccountInfo,
    fetchRegisteredAccounts,
    autoRegister,
    saveAccountInfo,
    updateAccountInfo,
    clearError,
    init
  }
})
