<template>
  <button
    class="button"
    @click="handleClick"
    :disabled="loading"
  >
    <div class="outline"></div>
    <div class="state state--default">
      <div class="icon">
        <svg v-if="type === 'save'" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g style="filter: url(#shadow)">
            <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z" fill="currentColor"></path>
            <polyline points="17,21 17,13 7,13 7,21" fill="none" stroke="currentColor" stroke-width="2"></polyline>
            <polyline points="7,3 7,8 15,8" fill="none" stroke="currentColor" stroke-width="2"></polyline>
          </g>
          <defs>
            <filter id="shadow">
              <fedropshadow dx="0" dy="1" stdDeviation="0.6" flood-opacity="0.5"></fedropshadow>
            </filter>
          </defs>
        </svg>
        <svg v-else-if="type === 'load'" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g style="filter: url(#shadow)">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" fill="none" stroke="currentColor" stroke-width="2"></path>
            <polyline points="7,10 12,15 17,10" fill="none" stroke="currentColor" stroke-width="2"></polyline>
            <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2"></line>
          </g>
          <defs>
            <filter id="shadow">
              <fedropshadow dx="0" dy="1" stdDeviation="0.6" flood-opacity="0.5"></fedropshadow>
            </filter>
          </defs>
        </svg>
        <svg v-else-if="type === 'reset'" width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g style="filter: url(#shadow)">
            <polyline points="1,4 1,10 7,10" fill="none" stroke="currentColor" stroke-width="2"></polyline>
            <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10" fill="none" stroke="currentColor" stroke-width="2"></path>
          </g>
          <defs>
            <filter id="shadow">
              <fedropshadow dx="0" dy="1" stdDeviation="0.6" flood-opacity="0.5"></fedropshadow>
            </filter>
          </defs>
        </svg>
      </div>
      <p>
        <span v-for="(char, index) in buttonText" :key="index" :style="`--i:${index}`">{{ char }}</span>
      </p>
    </div>
    <div class="state state--sent">
      <div class="icon">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" height="1em" width="1em" stroke-width="0.5px" stroke="black">
          <g style="filter: url(#shadow)">
            <path fill="currentColor" d="M12 22.75C6.07 22.75 1.25 17.93 1.25 12C1.25 6.07 6.07 1.25 12 1.25C17.93 1.25 22.75 6.07 22.75 12C22.75 17.93 17.93 22.75 12 22.75ZM12 2.75C6.9 2.75 2.75 6.9 2.75 12C2.75 17.1 6.9 21.25 12 21.25C17.1 21.25 21.25 17.1 21.25 12C21.25 6.9 17.1 2.75 12 2.75Z"></path>
            <path fill="currentColor" d="M10.5795 15.5801C10.3795 15.5801 10.1895 15.5001 10.0495 15.3601L7.21945 12.5301C6.92945 12.2401 6.92945 11.7601 7.21945 11.4701C7.50945 11.1801 7.98945 11.1801 8.27945 11.4701L10.5795 13.7701L15.7195 8.6301C16.0095 8.3401 16.4895 8.3401 16.7795 8.6301C17.0695 8.9201 17.0695 9.4001 16.7795 9.6901L11.1095 15.3601C10.9695 15.5001 10.7795 15.5801 10.5795 15.5801Z"></path>
          </g>
        </svg>
      </div>
      <p>
        <span style="--i:5">D</span>
        <span style="--i:6">o</span>
        <span style="--i:7">n</span>
        <span style="--i:8">e</span>
      </p>
    </div>
  </button>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance } from 'vue'

interface Props {
  type: 'save' | 'load' | 'reset'
  loading?: boolean
}

interface Emits {
  (e: 'click'): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// 计算按钮文字
const buttonText = computed(() => {
  const slots = getCurrentInstance()?.slots.default?.()
  if (slots && slots[0] && typeof slots[0].children === 'string') {
    return slots[0].children
  }

  // 默认文字
  switch (props.type) {
    case 'save': return '保存配置'
    case 'load': return '加载配置'
    case 'reset': return '重置配置'
    default: return '操作'
  }
})

const handleClick = () => {
  if (!props.loading) {
    emit('click')
  }
}
</script>

<style scoped>
.button {
  --primary: #ff5569;
  --neutral-1: #f7f8f7;
  --neutral-2: #e7e7e7;
  --radius: 14px;

  cursor: pointer;
  border-radius: var(--radius);
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
  border: none;
  box-shadow: 0 0.5px 0.5px 1px rgba(255, 255, 255, 0.2),
    0 10px 20px rgba(0, 0, 0, 0.2), 0 4px 5px 0px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  min-width: 200px;
  padding: 20px;
  height: 68px;
  font-family: "Galano Grotesque", Poppins, Montserrat, sans-serif;
  font-style: normal;
  font-size: 18px;
  font-weight: 600;
}

.button-content {
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.button-icon {
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.button-text {
  transition: all 0.3s ease;
}

/* 保存按钮样式 */
.button-save {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.button-save:hover:not(.loading) {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

/* 加载按钮样式 */
.button-load {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.button-load:hover:not(.loading) {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

/* 重置按钮样式 */
.button-reset {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.button-reset:hover:not(.loading) {
  background: linear-gradient(135deg, #d97706, #b45309);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
}

/* 加载状态 */
.loading {
  cursor: not-allowed;
  opacity: 0.7;
}

.loading .button-icon {
  opacity: 0;
  transform: scale(0);
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 按钮点击效果 */
.animated-button:active:not(.loading) {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .animated-button {
    padding: 10px 20px;
    font-size: 0.9rem;
    min-width: 100px;
    height: 44px;
  }
}
</style>
