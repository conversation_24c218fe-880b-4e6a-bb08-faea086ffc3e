/**
 * 应用常量定义
 */

// 应用信息
export const APP_INFO = {
  NAME: 'Cursor Pro',
  VERSION: 'Vue 3.5.17',
  DESCRIPTION: 'Cursor Pro Vue版本',
  AUTHOR: 'Vue Migration Team'
} as const

// API相关常量
export const API_CONSTANTS = {
  TIMEOUT: 30000, // 30秒超时
  RETRY_COUNT: 3,
  RETRY_DELAY: 1000
} as const

// 日志相关常量
export const LOG_CONSTANTS = {
  MAX_LOGS: 1000,
  MAX_LOG_LENGTH: 10000,
  LEVELS: {
    INFO: 'info',
    SUCCESS: 'success',
    WARNING: 'warning',
    ERROR: 'error'
  } as const,
  COLORS: {
    INFO: '#60a5fa',
    SUCCESS: '#10b981',
    WARNING: '#f59e0b',
    ERROR: '#f87171'
  } as const
} as const

// 主题相关常量
export const THEME_CONSTANTS = {
  COLORS: {
    PRIMARY: '#60a5fa',
    SUCCESS: '#10b981',
    WARNING: '#f59e0b',
    ERROR: '#f87171',
    BACKGROUND: '#181c20',
    SURFACE: '#23272e',
    BORDER: '#2d323a',
    TEXT_PRIMARY: '#e5e5e5',
    TEXT_SECONDARY: '#94a3b8',
    TEXT_MUTED: '#6b7280'
  } as const,
  FONTS: {
    MONO: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif",
    SANS: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif"
  } as const,
  BREAKPOINTS: {
    SM: '640px',
    MD: '768px',
    LG: '1024px',
    XL: '1280px'
  } as const
} as const

// 动画相关常量
export const ANIMATION_CONSTANTS = {
  DURATION: {
    FAST: '0.18s',
    NORMAL: '0.2s',
    SLOW: '0.3s'
  } as const,
  EASING: {
    EASE: 'ease',
    EASE_IN: 'ease-in',
    EASE_OUT: 'ease-out',
    EASE_IN_OUT: 'ease-in-out'
  } as const
} as const

// 存储相关常量
export const STORAGE_KEYS = {
  THEME: 'cursor-theme',
  SIDEBAR_COLLAPSED: 'cursor-sidebar-collapsed',
  LOG_SETTINGS: 'cursor-log-settings',
  USER_PREFERENCES: 'cursor-user-preferences',
  ACCOUNT_INFO: 'cursor-account-info',
  REGISTERED_ACCOUNTS: 'cursor-registered-accounts'
} as const

// 模态框相关常量
export const MODAL_CONSTANTS = {
  Z_INDEX: {
    MODAL: 1000,
    COPY_MODAL: 1100,
    CONFIRM_MODAL: 1200
  } as const,
  ANIMATION_DURATION: 300,
  AUTO_CLOSE_DELAY: 1800
} as const

// 操作类型常量
export const OPERATION_TYPES = {
  // 快速操作
  AUTO_WORKFLOW: 'auto-workflow',
  AUTO_REGISTER: 'auto-register',
  
  // 账户管理
  SAVE_ACCOUNT: 'save-account',
  UPDATE_MACHINE_IDS: 'update-machine-ids',
  BACKUP_SETTINGS: 'backup-settings',
  RESTORE_SETTINGS: 'restore-settings',
  BACKUP_SESSION: 'backup-session',
  RESTORE_SESSION: 'restore-session',
  
  // 系统配置
  SHOW_CONFIG: 'show-config',
  CHROME_CONFIG: 'chrome-config',
  MANUAL_VERIFY: 'manual-verify',
  INIT_CURSOR: 'init-cursor',
  
  // 机器ID工具
  RESET_MACHINE_CODE: 'reset-machine-code',
  
  // 检查工具
  AUTO_UPDATE: 'auto-update',
  BYPASS_VERSION: 'bypass-version',
  CHECK_AUTH: 'check-auth',
  
  // 高级功能
  CHANGE_LANGUAGE: 'change-language',
  BYPASS_TOKEN_LIMIT: 'bypass-token-limit',
  
  // 其他工具
  CONTRIBUTE: 'contribute'
} as const

// 页面路由常量
export const ROUTES = {
  DASHBOARD: 'dashboard',
  FEATURES: 'features',
  SETTINGS: 'settings',
  ABOUT: 'about',
  LOGS: 'logs'
} as const

// 账户状态常量
export const ACCOUNT_STATUS = {
  LOGGED_OUT: 'logged_out',
  LOGGED_IN: 'logged_in',
  LOADING: 'loading',
  ERROR: 'error'
} as const

// 计划类型常量
export const PLAN_TYPES = {
  FREE: 'Free',
  PRO: 'Pro',
  UNKNOWN: 'Unknown'
} as const

// 错误消息常量
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  API_ERROR: 'API调用失败，请稍后重试',
  PARSE_ERROR: '数据解析失败，请检查数据格式',
  VALIDATION_ERROR: '数据验证失败，请检查输入内容',
  PERMISSION_ERROR: '权限不足，请检查授权状态',
  TIMEOUT_ERROR: '操作超时，请稍后重试',
  UNKNOWN_ERROR: '未知错误，请联系技术支持'
} as const

// 成功消息常量
export const SUCCESS_MESSAGES = {
  SAVE_SUCCESS: '保存成功',
  UPDATE_SUCCESS: '更新成功',
  DELETE_SUCCESS: '删除成功',
  COPY_SUCCESS: '复制成功',
  BACKUP_SUCCESS: '备份成功',
  RESTORE_SUCCESS: '恢复成功',
  REGISTER_SUCCESS: '注册成功',
  LOGIN_SUCCESS: '登录成功',
  LOGOUT_SUCCESS: '退出成功'
} as const

// 验证规则常量
export const VALIDATION_RULES = {
  EMAIL: {
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    MESSAGE: '请输入有效的邮箱地址'
  },
  PASSWORD: {
    MIN_LENGTH: 6,
    MAX_LENGTH: 50,
    MESSAGE: '密码长度应在6-50个字符之间'
  },
  NAME: {
    MIN_LENGTH: 1,
    MAX_LENGTH: 50,
    MESSAGE: '姓名长度应在1-50个字符之间'
  }
} as const

// 文件相关常量
export const FILE_CONSTANTS = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: ['application/json', 'text/plain'],
  EXTENSIONS: ['.json', '.txt']
} as const

// 性能相关常量
export const PERFORMANCE_CONSTANTS = {
  DEBOUNCE_DELAY: 300,
  THROTTLE_DELAY: 100,
  LAZY_LOAD_THRESHOLD: 100,
  VIRTUAL_SCROLL_ITEM_HEIGHT: 50
} as const



// 导出所有常量的类型
export type LogLevel = typeof LOG_CONSTANTS.LEVELS[keyof typeof LOG_CONSTANTS.LEVELS]
export type ThemeColor = keyof typeof THEME_CONSTANTS.COLORS
export type OperationType = typeof OPERATION_TYPES[keyof typeof OPERATION_TYPES]
export type RouteName = typeof ROUTES[keyof typeof ROUTES]
export type AccountStatus = typeof ACCOUNT_STATUS[keyof typeof ACCOUNT_STATUS]
export type PlanType = typeof PLAN_TYPES[keyof typeof PLAN_TYPES]
