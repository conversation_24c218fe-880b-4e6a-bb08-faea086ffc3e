#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor Pro - 完整项目打包脚本
统一打包后端和前端，创建完整的分发包
"""

import os
import sys
import shutil
import subprocess
import zipfile
from pathlib import Path
from datetime import datetime

def create_release_directory():
    """创建发布目录"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    release_dir = Path(f'release_cursor_pro_{timestamp}')
    
    if release_dir.exists():
        shutil.rmtree(release_dir)
    
    release_dir.mkdir()
    print(f"📁 创建发布目录: {release_dir}")
    return release_dir

def build_backend():
    """构建后端"""
    print("\n🔧 构建 Python 后端...")
    try:
        subprocess.check_call([sys.executable, 'build_backend.py'])
        return True
    except subprocess.CalledProcessError:
        print("❌ 后端构建失败")
        return False

def build_frontend():
    """构建前端"""
    print("\n🖥️ 构建 Electron 前端...")
    try:
        subprocess.check_call([sys.executable, 'build_frontend.py'])
        return True
    except subprocess.CalledProcessError:
        print("❌ 前端构建失败")
        return False

def create_unified_package(release_dir):
    """创建统一安装包"""
    print("\n📦 创建统一安装包...")
    
    # 创建子目录
    backend_dir = release_dir / 'backend'
    frontend_dir = release_dir / 'frontend'
    docs_dir = release_dir / 'docs'
    
    backend_dir.mkdir()
    frontend_dir.mkdir()
    docs_dir.mkdir()
    
    # 复制后端文件
    if Path('dist').exists():
        shutil.copytree('dist', backend_dir / 'dist', dirs_exist_ok=True)
        print("✅ 复制后端文件")
    else:
        print("⚠️ 后端构建文件不存在")
    
    # 复制前端文件
    if Path('dist-frontend').exists():
        shutil.copytree('dist-frontend', frontend_dir / 'dist', dirs_exist_ok=True)
        print("✅ 复制前端文件")
    else:
        print("⚠️ 前端构建文件不存在")
    
    # 复制文档
    doc_files = [
        'README.md',
        'DISCLAIMER.md',
        'SECURITY.md',
        'FINAL_SETUP_GUIDE.md',
        'CONFIGURATION_GUIDE.md',
        'API_README.md',
        'VERSION_CONTROL_GUIDE.md',
        'ANTI_DETECTION_GUIDE.md',
        'DATA_STORAGE_GUIDE.md',
        'SUPPORT.md',
    ]
    
    for doc_file in doc_files:
        if Path(doc_file).exists():
            shutil.copy2(doc_file, docs_dir / doc_file)
    
    print("✅ 复制文档文件")

def create_installer_script(release_dir):
    """创建安装脚本"""
    installer_content = '''@echo off
title Cursor Pro - Complete Installation
color 0A

echo ========================================
echo  Cursor Pro - Complete Installation
echo  Enhanced Edition with Anti-Detection
echo ========================================
echo.

echo This installer will set up Cursor Pro on your system.
echo.
echo Components included:
echo [1] Python Backend Server
echo [2] Electron Desktop Application  
echo [3] Documentation and Guides
echo.

pause

echo.
echo [1/3] Installing Backend Server...
echo.

REM 创建安装目录
if not exist "C:\\Program Files\\Cursor Pro" (
    mkdir "C:\\Program Files\\Cursor Pro"
)

REM 复制后端文件
xcopy /E /I /Y "backend\\*" "C:\\Program Files\\Cursor Pro\\backend\\"
echo ✓ Backend server installed

echo.
echo [2/3] Installing Desktop Application...
echo.

REM 安装前端应用
cd frontend\\dist
for %%f in (*.exe) do (
    echo Installing %%f...
    start /wait "" "%%f"
)
cd ..\\..

echo ✓ Desktop application installed

echo.
echo [3/3] Setting up shortcuts...
echo.

REM 创建桌面快捷方式
echo Creating desktop shortcut...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\Cursor Pro.lnk'); $Shortcut.TargetPath = 'C:\\Program Files\\Cursor Pro\\backend\\cursor-backend.exe'; $Shortcut.Save()"

echo ✓ Desktop shortcut created

echo.
echo ========================================
echo  Installation Complete!
echo ========================================
echo.
echo Cursor Pro has been successfully installed.
echo.
echo To start using Cursor Pro:
echo 1. Double-click the desktop shortcut, or
echo 2. Run from Start Menu
echo.
echo For documentation, see the 'docs' folder.
echo.
echo Thank you for using Cursor Pro!
echo.
pause
'''
    
    installer_path = release_dir / 'install.cmd'
    with open(installer_path, 'w', encoding='utf-8') as f:
        f.write(installer_content)
    
    print("✅ 创建安装脚本: install.cmd")

def create_uninstaller_script(release_dir):
    """创建卸载脚本"""
    uninstaller_content = '''@echo off
title Cursor Pro - Uninstaller
color 0C

echo ========================================
echo  Cursor Pro - Uninstaller
echo ========================================
echo.

echo This will remove Cursor Pro from your system.
echo.

set /p confirm="Are you sure you want to uninstall? (Y/N): "
if /i "%confirm%" neq "Y" (
    echo Uninstallation cancelled.
    pause
    exit /b
)

echo.
echo Removing Cursor Pro...
echo.

REM 删除安装目录
if exist "C:\\Program Files\\Cursor Pro" (
    rmdir /s /q "C:\\Program Files\\Cursor Pro"
    echo ✓ Application files removed
)

REM 删除桌面快捷方式
if exist "%USERPROFILE%\\Desktop\\Cursor Pro.lnk" (
    del "%USERPROFILE%\\Desktop\\Cursor Pro.lnk"
    echo ✓ Desktop shortcut removed
)

echo.
echo ========================================
echo  Uninstallation Complete!
echo ========================================
echo.
echo Cursor Pro has been removed from your system.
echo.
pause
'''
    
    uninstaller_path = release_dir / 'uninstall.cmd'
    with open(uninstaller_path, 'w', encoding='utf-8') as f:
        f.write(uninstaller_content)
    
    print("✅ 创建卸载脚本: uninstall.cmd")

def create_readme(release_dir):
    """创建发布说明"""
    readme_content = '''# Cursor Pro - Complete Release Package

## 📦 Package Contents

This package contains the complete Cursor Pro application with all components:

### 🔧 Backend Server
- `backend/dist/cursor-backend.exe` - Main backend server
- `backend/dist/` - Supporting files and configurations

### 🖥️ Desktop Application  
- `frontend/dist/` - Electron desktop application installer
- Modern Vue.js interface with enhanced features

### 📚 Documentation
- `docs/` - Complete documentation and guides
- Setup instructions, configuration guides, and troubleshooting

## 🚀 Quick Installation

1. **Run the installer**: Double-click `install.cmd`
2. **Follow the prompts**: The installer will guide you through the process
3. **Launch the app**: Use the desktop shortcut or Start Menu

## 📋 Manual Installation

If you prefer manual installation:

1. **Backend**: Copy `backend/dist/` to your preferred location
2. **Frontend**: Run the installer in `frontend/dist/`
3. **Start**: Run `cursor-backend.exe` to start the backend server

## 🔧 System Requirements

- **OS**: Windows 10/11 (64-bit)
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 500MB free space
- **Network**: Internet connection for updates

## 📖 Documentation

See the `docs/` folder for:
- `FINAL_SETUP_GUIDE.md` - Complete setup instructions
- `CONFIGURATION_GUIDE.md` - Configuration options
- `API_README.md` - API documentation
- `ANTI_DETECTION_GUIDE.md` - Anti-detection features
- `SUPPORT.md` - Support and troubleshooting

## 🆘 Support

If you encounter any issues:
1. Check the documentation in `docs/`
2. Review the troubleshooting guides
3. Ensure all system requirements are met

## ⚠️ Important Notes

- This software is for educational and research purposes
- Please read `DISCLAIMER.md` and `SECURITY.md` before use
- Keep your installation updated for best performance

---

**Cursor Pro Enhanced Edition**  
*Advanced Anti-Detection System Included*
'''
    
    readme_path = release_dir / 'README.txt'
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 创建发布说明: README.txt")

def create_zip_package(release_dir):
    """创建 ZIP 压缩包"""
    print("\n🗜️ 创建 ZIP 压缩包...")
    
    zip_name = f'{release_dir.name}.zip'
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(release_dir):
            for file in files:
                file_path = Path(root) / file
                arc_name = file_path.relative_to(release_dir.parent)
                zipf.write(file_path, arc_name)
    
    print(f"✅ 创建压缩包: {zip_name}")
    return zip_name

def main():
    """主函数"""
    print("=" * 60)
    print("🏗️  Cursor Pro - 完整项目打包工具")
    print("=" * 60)
    
    # 创建发布目录
    release_dir = create_release_directory()
    
    # 构建后端
    if not build_backend():
        print("❌ 后端构建失败，停止打包")
        sys.exit(1)
    
    # 构建前端
    if not build_frontend():
        print("❌ 前端构建失败，停止打包")
        sys.exit(1)
    
    # 创建统一包
    create_unified_package(release_dir)
    
    # 创建安装脚本
    create_installer_script(release_dir)
    create_uninstaller_script(release_dir)
    create_readme(release_dir)
    
    # 创建压缩包
    zip_file = create_zip_package(release_dir)
    
    print("\n" + "=" * 60)
    print("🎉 完整打包成功!")
    print("=" * 60)
    print(f"📁 发布目录: {release_dir}")
    print(f"📦 压缩包: {zip_file}")
    print("\n📋 分发说明:")
    print("1. 将压缩包分发给用户")
    print("2. 用户解压后运行 install.cmd")
    print("3. 按照安装向导完成安装")
    print("\n🚀 安装后用户可以:")
    print("- 使用桌面快捷方式启动")
    print("- 从开始菜单启动")
    print("- 查看 docs 文件夹获取帮助")

if __name__ == "__main__":
    main()
