<template>
  <button 
    class="operation-btn"
    :class="[
      `btn-${type}`,
      { 
        'btn-loading': loading,
        'btn-disabled': disabled,
        'btn-block': block
      }
    ]"
    :disabled="disabled || loading"
    @click="handleClick"
  >
    <!-- 图标 -->
    <div v-if="icon || loading" class="btn-icon">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-spinner">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M21 12a9 9 0 11-6.219-8.56"/>
        </svg>
      </div>
      <!-- 普通图标 -->
      <div v-else-if="icon" v-html="icon"></div>
    </div>
    
    <!-- 按钮文本 -->
    <div class="btn-text">{{ text }}</div>
    
    <!-- 描述信息（悬停显示） -->
    <div v-if="description" class="btn-description">
      {{ description }}
    </div>
  </button>
</template>

<script setup lang="ts">
interface OperationButtonProps {
  type?: 'primary' | 'secondary' | 'warning' | 'danger' | 'success'
  icon?: string
  text: string
  description?: string
  loading?: boolean
  disabled?: boolean
  block?: boolean
}

interface OperationButtonEvents {
  'click': []
}

const props = withDefaults(defineProps<OperationButtonProps>(), {
  type: 'primary',
  loading: false,
  disabled: false,
  block: false
})

const emit = defineEmits<OperationButtonEvents>()

const handleClick = () => {
  if (!props.loading && !props.disabled) {
    emit('click')
  }
}
</script>

<style scoped>
/* 操作按钮基础样式 - 与原版feature-btn完全一致 */
.operation-btn {
  background: #181c20;
  border: 2px solid #2d323a;
  color: #e5e5e5;
  border-radius: 14px;
  padding: 14px 8px 10px 14px;
  font-size: 1.12rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  font-weight: normal;
  letter-spacing: 1px;
  text-shadow: none;
  cursor: pointer;
  transition: all 0.3s ease;
  will-change: transform;
  position: relative;
  display: flex;
  align-items: center;
  gap: 10px;
  min-height: 60px;
  overflow: hidden;
  box-shadow: none;
  text-align: left;
  width: 100%;
}

.operation-btn:hover:not(.btn-disabled):not(.btn-loading) {
  background: #10b981;
  color: #fff;
  border: 2px solid #10b981;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* 按钮类型样式 */
.btn-primary {
  background: #181c20;
  border-color: #2d323a;
}

.btn-primary:hover:not(.btn-disabled):not(.btn-loading) {
  background: #10b981;
  border-color: #10b981;
}

.btn-secondary {
  background: #374151;
  border-color: #4b5563;
}

.btn-secondary:hover:not(.btn-disabled):not(.btn-loading) {
  background: #4b5563;
  border-color: #6b7280;
}

.btn-warning {
  background: #181c20;
  border-color: #f59e0b;
  color: #f59e0b;
}

.btn-warning:hover:not(.btn-disabled):not(.btn-loading) {
  background: #f59e0b;
  border-color: #f59e0b;
  color: #fff;
}

.btn-danger {
  background: #181c20;
  border-color: #ef4444;
  color: #ef4444;
}

.btn-danger:hover:not(.btn-disabled):not(.btn-loading) {
  background: #ef4444;
  border-color: #ef4444;
  color: #fff;
}

.btn-success {
  background: #10b981;
  border-color: #10b981;
  color: #fff;
}

.btn-success:hover:not(.btn-disabled):not(.btn-loading) {
  background: #059669;
  border-color: #059669;
}

/* 按钮状态 */
.btn-loading {
  cursor: not-allowed;
  opacity: 0.7;
}

.btn-disabled {
  cursor: not-allowed;
  opacity: 0.5;
  background: #374151 !important;
  border-color: #4b5563 !important;
  color: #6b7280 !important;
}

.btn-block {
  width: 100%;
}

/* 图标样式 */
.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 18px;
  height: 18px;
}

.btn-icon svg {
  width: 18px;
  height: 18px;
}

/* 加载动画 */
.loading-spinner svg {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 按钮文本 */
.btn-text {
  font-weight: 500;
  flex: 1;
}

/* 描述信息 - 与原版feature-desc完全一致 */
.btn-description {
  display: none;
  position: absolute;
  left: 0;
  top: 100%;
  background: #23272e;
  color: #e5e5e5;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.9rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  border: 1px solid #2d323a;
  z-index: 1000;
  width: 260px;
  margin-top: 4px;
  font-family: 'Fira Mono', 'Consolas', monospace;
  font-weight: normal;
  letter-spacing: 0.5px;
  line-height: 1.4;
}

.operation-btn:hover .btn-description {
  display: block;
}

/* 现代化操作按钮样式（用于设置页面） */
.modern-action-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 1rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  font-weight: 500;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid;
  min-width: 140px;
  justify-content: center;
  text-align: center;
}

.modern-action-btn.primary {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-color: #10b981;
  color: #ffffff;
}

.modern-action-btn.primary:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  border-color: #059669;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.modern-action-btn.secondary {
  background: #374151;
  border-color: #4b5563;
  color: #e5e5e5;
}

.modern-action-btn.secondary:hover {
  background: #4b5563;
  border-color: #6b7280;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(75, 85, 99, 0.4);
}

.modern-action-btn.warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border-color: #f59e0b;
  color: #ffffff;
}

.modern-action-btn.warning:hover {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
  border-color: #d97706;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .operation-btn {
    padding: 12px 6px 8px 12px;
    font-size: 1rem;
    min-height: 50px;
  }
  
  .btn-description {
    width: 200px;
    font-size: 0.8rem;
  }
  
  .modern-action-btn {
    padding: 10px 16px;
    font-size: 0.9rem;
    min-width: 120px;
  }
}
</style>
