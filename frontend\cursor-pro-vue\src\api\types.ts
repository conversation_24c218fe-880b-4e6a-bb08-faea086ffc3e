/**
 * API类型定义
 * 定义所有API请求和响应的类型
 */

// ============= 通用类型 =============

export interface PaginationParams {
  page?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginationResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// ============= 账户相关类型 =============

export interface AccountInfo {
  id: string
  email: string
  username?: string
  firstName?: string
  lastName?: string
  avatar?: string
  status: 'active' | 'inactive' | 'suspended'
  createdAt: string
  updatedAt: string
}

export interface LoginRequest {
  email: string
  password: string
  rememberMe?: boolean
}

export interface LoginResponse {
  token: string
  refreshToken: string
  user: AccountInfo
  expiresIn: number
}

export interface RegisterRequest {
  email: string
  password: string
  firstName: string
  lastName: string
  region?: string
}

export interface RegisterResponse {
  success: boolean
  user: AccountInfo
  message: string
}

// ============= 设置相关类型 =============

export interface SettingsData {
  email: string
  tempmailDomain: string
  tempmailEpin: string
  firstName: string
  lastName: string
  password: string
  region: string
  registerMode: 'auto' | 'manual' | 'batch'
  showBrowser: boolean
  timeout: number
  retryCount: number
}

export interface SaveSettingsRequest {
  settings: SettingsData
}

export interface SaveSettingsResponse {
  success: boolean
  message: string
  savedAt: string
}

export interface LoadSettingsResponse {
  settings: SettingsData
  lastModified: string
}

// ============= 操作相关类型 =============

export interface OperationRequest {
  type: 'register' | 'login' | 'reset' | 'verify'
  data?: any
  options?: {
    timeout?: number
    retryCount?: number
    showBrowser?: boolean
  }
}

export interface OperationResponse {
  success: boolean
  result?: any
  message: string
  duration: number
  logs: string[]
}

export interface OperationStatus {
  id: string
  type: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  progress: number
  message: string
  startTime: string
  endTime?: string
  logs: string[]
}

// ============= 日志相关类型 =============

export interface LogEntry {
  id: string
  timestamp: string
  level: 'info' | 'warning' | 'error' | 'success'
  message: string
  source?: string
  data?: any
}

export interface LogsRequest {
  level?: string
  source?: string
  startTime?: string
  endTime?: string
  limit?: number
}

export interface LogsResponse {
  logs: LogEntry[]
  total: number
  hasMore: boolean
}

// ============= 机器ID相关类型 =============

export interface MachineIdInfo {
  id: string
  name: string
  status: 'active' | 'inactive'
  lastUsed: string
  createdAt: string
}

export interface MachineIdRequest {
  action: 'get' | 'reset' | 'generate'
  options?: {
    force?: boolean
    backup?: boolean
  }
}

export interface MachineIdResponse {
  machineId: string
  status: string
  message: string
  backup?: string
}

// ============= OAuth相关类型 =============

export interface OAuthConfig {
  clientId: string
  clientSecret: string
  redirectUri: string
  scope: string[]
  provider: 'google' | 'github' | 'microsoft' | 'discord'
}

export interface OAuthRequest {
  provider: string
  config: OAuthConfig
  action: 'authorize' | 'token' | 'refresh'
}

export interface OAuthResponse {
  success: boolean
  authUrl?: string
  token?: string
  refreshToken?: string
  expiresIn?: number
  userInfo?: any
}

// ============= 系统相关类型 =============

export interface SystemInfo {
  version: string
  platform: string
  nodeVersion: string
  pythonVersion: string
  uptime: number
  memory: {
    used: number
    total: number
  }
  cpu: {
    usage: number
    cores: number
  }
}

export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy'
  services: {
    database: 'up' | 'down'
    api: 'up' | 'down'
    cache: 'up' | 'down'
  }
  timestamp: string
}

// ============= 配置相关类型 =============

export interface AppConfig {
  apiUrl: string
  timeout: number
  retryCount: number
  logLevel: 'debug' | 'info' | 'warn' | 'error'
  features: {
    oauth: boolean
    machineId: boolean
    advancedMode: boolean
  }
}

export interface UpdateConfigRequest {
  config: Partial<AppConfig>
}

export interface UpdateConfigResponse {
  success: boolean
  config: AppConfig
  message: string
}

// ============= 错误相关类型 =============

export interface ApiErrorResponse {
  error: string
  message: string
  code?: string
  details?: any
  timestamp: string
}

export interface ValidationError {
  field: string
  message: string
  value?: any
}

export interface ValidationErrorResponse extends ApiErrorResponse {
  errors: ValidationError[]
}

// ============= 文件相关类型 =============

export interface FileUploadRequest {
  file: File
  type: 'config' | 'log' | 'backup'
  metadata?: Record<string, any>
}

export interface FileUploadResponse {
  success: boolean
  fileId: string
  filename: string
  size: number
  url: string
}

export interface FileDownloadRequest {
  fileId: string
  type: 'config' | 'log' | 'backup'
}

// ============= 统计相关类型 =============

export interface UsageStats {
  totalOperations: number
  successfulOperations: number
  failedOperations: number
  averageResponseTime: number
  lastOperationTime: string
  dailyStats: {
    date: string
    operations: number
    successRate: number
  }[]
}

export interface StatsRequest {
  startDate?: string
  endDate?: string
  granularity?: 'hour' | 'day' | 'week' | 'month'
}

export interface StatsResponse {
  stats: UsageStats
  period: {
    start: string
    end: string
  }
}

// ============= 导出所有类型 =============

export type {
  // 重新导出基础类型
  ApiResponse,
  RequestConfig,
  ApiError,
  ApiErrorType
} from './base'
