/**
 * 统一错误处理器
 * 提供用户友好的错误处理和提示
 */

import { ElMessage, ElNotification } from 'element-plus'
import { ApiError, ApiErrorType } from './base'

// 错误消息映射
const ERROR_MESSAGES: Record<ApiErrorType, string> = {
  [ApiErrorType.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
  [ApiErrorType.TIMEOUT_ERROR]: '请求超时，请稍后重试',
  [ApiErrorType.SERVER_ERROR]: '服务器内部错误，请稍后重试',
  [ApiErrorType.CLIENT_ERROR]: '请求参数错误，请检查输入',
  [ApiErrorType.VALIDATION_ERROR]: '数据验证失败，请检查输入格式',
  [ApiErrorType.AUTHENTICATION_ERROR]: '身份验证失败，请重新登录',
  [ApiErrorType.AUTHORIZATION_ERROR]: '权限不足，无法执行此操作'
}

// 错误处理配置
interface ErrorHandlerConfig {
  showToast?: boolean
  showNotification?: boolean
  logError?: boolean
  reportError?: boolean
  customMessage?: string
}

/**
 * 错误处理器类
 */
export class ErrorHandler {
  private static instance: ErrorHandler
  private errorReportUrl?: string
  private userId?: string

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler()
    }
    return ErrorHandler.instance
  }

  /**
   * 配置错误处理器
   */
  configure(config: {
    errorReportUrl?: string
    userId?: string
  }): void {
    this.errorReportUrl = config.errorReportUrl
    this.userId = config.userId
  }

  /**
   * 处理API错误
   */
  async handleError(
    error: ApiError | Error,
    context?: string,
    config: ErrorHandlerConfig = {}
  ): Promise<void> {
    const {
      showToast = true,
      showNotification = false,
      logError = true,
      reportError = false,
      customMessage
    } = config

    // 确保是ApiError类型
    const apiError = error instanceof ApiError ? error : this.convertToApiError(error)

    // 记录错误日志
    if (logError) {
      this.logError(apiError, context)
    }

    // 显示用户友好的错误提示
    const userMessage = customMessage || this.getUserFriendlyMessage(apiError)
    
    if (showToast) {
      this.showToast(userMessage, apiError.type)
    }
    
    if (showNotification) {
      this.showNotification(userMessage, apiError, context)
    }

    // 上报错误（如果配置了）
    if (reportError && this.errorReportUrl) {
      await this.reportError(apiError, context)
    }

    // 特殊错误处理
    await this.handleSpecialErrors(apiError)
  }

  /**
   * 转换为ApiError
   */
  private convertToApiError(error: Error): ApiError {
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      return new ApiError('网络连接失败', ApiErrorType.NETWORK_ERROR)
    } else if (error.name === 'TimeoutError') {
      return new ApiError('请求超时', ApiErrorType.TIMEOUT_ERROR)
    } else {
      return new ApiError(error.message, ApiErrorType.NETWORK_ERROR)
    }
  }

  /**
   * 获取用户友好的错误消息
   */
  private getUserFriendlyMessage(error: ApiError): string {
    // 优先使用预定义的错误消息
    const baseMessage = ERROR_MESSAGES[error.type] || '操作失败，请稍后重试'

    // 根据具体错误状态码提供更详细的信息
    if (error.status) {
      switch (error.status) {
        case 400:
          return '请求参数错误，请检查输入内容'
        case 401:
          return '登录已过期，请重新登录'
        case 403:
          return '权限不足，无法执行此操作'
        case 404:
          return '请求的资源不存在'
        case 422:
          return '数据格式错误，请检查输入'
        case 429:
          return '请求过于频繁，请稍后重试'
        case 500:
          return '服务器内部错误，请联系管理员'
        case 502:
          return '服务器网关错误，请稍后重试'
        case 503:
          return '服务暂时不可用，请稍后重试'
        case 504:
          return '服务器响应超时，请稍后重试'
        default:
          return baseMessage
      }
    }

    return baseMessage
  }

  /**
   * 显示Toast提示
   */
  private showToast(message: string, type: ApiErrorType): void {
    const messageType = this.getMessageType(type)
    
    ElMessage({
      message,
      type: messageType,
      duration: 4000,
      showClose: true
    })
  }

  /**
   * 显示通知
   */
  private showNotification(message: string, error: ApiError, context?: string): void {
    const notificationType = this.getNotificationType(error.type)
    
    ElNotification({
      title: '操作失败',
      message: `${message}${context ? `\n上下文: ${context}` : ''}`,
      type: notificationType,
      duration: 6000,
      position: 'top-right'
    })
  }

  /**
   * 记录错误日志
   */
  private logError(error: ApiError, context?: string): void {
    const logData = {
      timestamp: new Date().toISOString(),
      type: error.type,
      message: error.message,
      status: error.status,
      context,
      userId: this.userId,
      userAgent: navigator.userAgent,
      url: window.location.href,
      stack: error.stack
    }

    console.group('🚨 API错误详情')
    console.error('错误类型:', error.type)
    console.error('错误消息:', error.message)
    console.error('HTTP状态:', error.status)
    console.error('上下文:', context)
    console.error('完整错误:', error)
    console.error('日志数据:', logData)
    console.groupEnd()

    // 可以在这里发送到日志服务
    // logService.addLog({
    //   level: 'error',
    //   message: error.message,
    //   source: 'ErrorHandler',
    //   data: logData
    // })
  }

  /**
   * 上报错误
   */
  private async reportError(error: ApiError, context?: string): Promise<void> {
    if (!this.errorReportUrl) return

    try {
      const reportData = {
        type: error.type,
        message: error.message,
        status: error.status,
        context,
        userId: this.userId,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        stack: error.stack
      }

      await fetch(this.errorReportUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(reportData)
      })
    } catch (reportError) {
      console.error('错误上报失败:', reportError)
    }
  }

  /**
   * 处理特殊错误
   */
  private async handleSpecialErrors(error: ApiError): Promise<void> {
    switch (error.type) {
      case ApiErrorType.AUTHENTICATION_ERROR:
        // 清除认证信息
        localStorage.removeItem('auth_token')
        localStorage.removeItem('refresh_token')
        
        // 可以在这里触发重新登录流程
        // router.push('/login')
        break

      case ApiErrorType.AUTHORIZATION_ERROR:
        // 权限不足，可能需要升级账户或联系管理员
        break

      case ApiErrorType.NETWORK_ERROR:
        // 网络错误，可以提示用户检查网络连接
        break

      default:
        break
    }
  }

  /**
   * 获取消息类型
   */
  private getMessageType(errorType: ApiErrorType): 'error' | 'warning' | 'info' {
    switch (errorType) {
      case ApiErrorType.VALIDATION_ERROR:
      case ApiErrorType.CLIENT_ERROR:
        return 'warning'
      case ApiErrorType.AUTHENTICATION_ERROR:
      case ApiErrorType.AUTHORIZATION_ERROR:
        return 'info'
      default:
        return 'error'
    }
  }

  /**
   * 获取通知类型
   */
  private getNotificationType(errorType: ApiErrorType): 'error' | 'warning' | 'info' {
    return this.getMessageType(errorType)
  }

  /**
   * 创建重试函数
   */
  createRetryHandler<T>(
    apiCall: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): () => Promise<T> {
    return async (): Promise<T> => {
      let lastError: ApiError | undefined

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          return await apiCall()
        } catch (error) {
          lastError = error instanceof ApiError ? error : this.convertToApiError(error as Error)
          
          // 某些错误不应该重试
          if (this.shouldNotRetry(lastError)) {
            throw lastError
          }

          // 最后一次尝试失败
          if (attempt === maxRetries) {
            throw lastError
          }

          // 等待后重试
          await new Promise(resolve => setTimeout(resolve, delay * attempt))
          console.log(`🔄 重试第 ${attempt} 次...`)
        }
      }

      throw lastError
    }
  }

  /**
   * 判断是否不应该重试
   */
  private shouldNotRetry(error: ApiError): boolean {
    return [
      ApiErrorType.AUTHENTICATION_ERROR,
      ApiErrorType.AUTHORIZATION_ERROR,
      ApiErrorType.VALIDATION_ERROR,
      ApiErrorType.CLIENT_ERROR
    ].includes(error.type)
  }
}

// 导出单例实例
export const errorHandler = ErrorHandler.getInstance()

// 导出便捷函数
export const handleApiError = (
  error: ApiError | Error,
  context?: string,
  config?: ErrorHandlerConfig
) => errorHandler.handleError(error, context, config)
