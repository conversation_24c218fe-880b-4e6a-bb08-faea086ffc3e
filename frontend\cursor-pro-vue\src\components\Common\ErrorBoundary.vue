<template>
  <div class="error-boundary">
    <div v-if="hasError" class="error-display">
      <div class="error-icon">⚠️</div>
      <div class="error-title">出现了一个错误</div>
      <div class="error-message">{{ errorMessage }}</div>
      <div class="error-actions">
        <button class="error-btn" @click="handleRetry">重试</button>
        <button class="error-btn secondary" @click="handleReload">刷新页面</button>
      </div>
      <details v-if="errorDetails" class="error-details">
        <summary>错误详情</summary>
        <pre>{{ errorDetails }}</pre>
      </details>
    </div>
    <slot v-else></slot>
  </div>
</template>

<script setup lang="ts">
import { ref, onErrorCaptured } from 'vue'
import { useLogStore } from '@/stores/logs'

const logStore = useLogStore()

const hasError = ref(false)
const errorMessage = ref('')
const errorDetails = ref('')

// 捕获子组件错误
onErrorCaptured((error: Error, instance, info) => {
  hasError.value = true
  errorMessage.value = error.message || '未知错误'
  errorDetails.value = `${error.stack}\n\n组件信息: ${info}`
  
  // 记录错误日志
  logStore.addError(`组件错误: ${error.message}`, 'ErrorBoundary')
  
  // 阻止错误继续传播
  return false
})

const handleRetry = () => {
  hasError.value = false
  errorMessage.value = ''
  errorDetails.value = ''
}

const handleReload = () => {
  window.location.reload()
}
</script>

<style scoped>
.error-boundary {
  width: 100%;
  height: 100%;
}

.error-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  background: #23272e;
  border-radius: 16px;
  border: 1px solid #ef4444;
  margin: 20px;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.error-title {
  color: #ef4444;
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 12px;
  font-family: 'Fira Mono', 'Consolas', monospace;
}

.error-message {
  color: #e5e5e5;
  font-size: 1rem;
  margin-bottom: 24px;
  max-width: 500px;
  line-height: 1.5;
  font-family: 'Fira Mono', 'Consolas', monospace;
}

.error-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.error-btn {
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid;
  font-family: 'Fira Mono', 'Consolas', monospace;
  background: #ef4444;
  border-color: #ef4444;
  color: #ffffff;
}

.error-btn:hover {
  background: #dc2626;
  border-color: #dc2626;
}

.error-btn.secondary {
  background: #374151;
  border-color: #4b5563;
  color: #e5e5e5;
}

.error-btn.secondary:hover {
  background: #4b5563;
  border-color: #6b7280;
}

.error-details {
  width: 100%;
  max-width: 600px;
  text-align: left;
}

.error-details summary {
  color: #94a3b8;
  cursor: pointer;
  padding: 8px;
  font-family: 'Fira Mono', 'Consolas', monospace;
}

.error-details pre {
  background: #181c20;
  color: #e5e5e5;
  padding: 12px;
  border-radius: 6px;
  font-size: 0.8rem;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  border: 1px solid #2d323a;
  margin-top: 8px;
}
</style>
