/**
 * 备份相关API服务
 * 处理设置和会话的备份、恢复、删除等操作
 */

import { BaseAPI } from '../base'

// 备份相关类型定义
export interface BackupInfo {
  id: string
  name: string
  type: 'settings' | 'session' | 'full'
  size: number
  createdAt: string
  description?: string
  tags?: string[]
}

export interface BackupRequest {
  name?: string
  type: 'settings' | 'session' | 'full'
  description?: string
  includeUserData?: boolean
  compress?: boolean
}

export interface BackupResponse {
  success: boolean
  backupId: string
  message: string
  size: number
  location: string
}

export interface RestoreRequest {
  backupId: string
  options?: {
    overwrite?: boolean
    selective?: string[]
    dryRun?: boolean
  }
}

export interface RestoreResponse {
  success: boolean
  message: string
  restoredItems: string[]
  warnings?: string[]
}

/**
 * 备份服务类
 * 继承BaseAPI，提供备份相关的API调用
 */
export class BackupService extends BaseAPI {
  /**
   * 备份设置
   */
  async backupSettings(request?: Partial<BackupRequest>): Promise<BackupResponse> {
    console.log('🔄 [BackupService] 备份设置:', request)

    const backupRequest: BackupRequest = {
      type: 'settings',
      compress: true,
      ...request
    }

    const response = await this.post<BackupResponse>('backup/settings', backupRequest)
    console.log('📡 [BackupService] 备份设置响应:', response)
    
    return response.data
  }

  /**
   * 备份会话
   */
  async backupSession(request?: Partial<BackupRequest>): Promise<BackupResponse> {
    console.log('🔄 [BackupService] 备份会话:', request)

    const backupRequest: BackupRequest = {
      type: 'session',
      compress: true,
      ...request
    }

    const response = await this.post<BackupResponse>('backup/session', backupRequest)
    console.log('📡 [BackupService] 备份会话响应:', response)
    
    return response.data
  }

  /**
   * 完整备份
   */
  async fullBackup(request?: Partial<BackupRequest>): Promise<BackupResponse> {
    console.log('🔄 [BackupService] 完整备份:', request)

    const backupRequest: BackupRequest = {
      type: 'full',
      compress: true,
      includeUserData: true,
      ...request
    }

    const response = await this.post<BackupResponse>('backup/full', backupRequest)
    console.log('📡 [BackupService] 完整备份响应:', response)
    
    return response.data
  }

  /**
   * 列出设置备份
   */
  async listSettingsBackups(): Promise<{
    backups: BackupInfo[]
    total: number
  }> {
    console.log('🔄 [BackupService] 列出设置备份')

    const response = await this.get<{
      backups: BackupInfo[]
      total: number
    }>('backup/settings/list')
    
    console.log('📡 [BackupService] 设置备份列表响应:', response)
    return response.data
  }

  /**
   * 列出会话备份
   */
  async listSessionBackups(): Promise<{
    backups: BackupInfo[]
    total: number
  }> {
    console.log('🔄 [BackupService] 列出会话备份')

    const response = await this.get<{
      backups: BackupInfo[]
      total: number
    }>('backup/session/list')
    
    console.log('📡 [BackupService] 会话备份列表响应:', response)
    return response.data
  }

  /**
   * 列出所有备份
   */
  async listAllBackups(filters?: {
    type?: 'settings' | 'session' | 'full'
    startDate?: string
    endDate?: string
    tags?: string[]
  }): Promise<{
    backups: BackupInfo[]
    total: number
    totalSize: number
  }> {
    console.log('🔄 [BackupService] 列出所有备份:', filters)

    const response = await this.get<{
      backups: BackupInfo[]
      total: number
      totalSize: number
    }>('backup/list', filters)
    
    console.log('📡 [BackupService] 所有备份列表响应:', response)
    return response.data
  }

  /**
   * 恢复设置
   */
  async restoreSettings(request: RestoreRequest): Promise<RestoreResponse> {
    console.log('🔄 [BackupService] 恢复设置:', request)

    const response = await this.post<RestoreResponse>('backup/settings/restore', request)
    console.log('📡 [BackupService] 恢复设置响应:', response)
    
    return response.data
  }

  /**
   * 恢复会话
   */
  async restoreSession(request: RestoreRequest): Promise<RestoreResponse> {
    console.log('🔄 [BackupService] 恢复会话:', request)

    const response = await this.post<RestoreResponse>('backup/session/restore', request)
    console.log('📡 [BackupService] 恢复会话响应:', response)
    
    return response.data
  }

  /**
   * 删除备份
   */
  async deleteBackup(backupId: string): Promise<{
    success: boolean
    message: string
  }> {
    console.log('🔄 [BackupService] 删除备份:', backupId)

    const response = await this.delete<{
      success: boolean
      message: string
    }>(`backup/${backupId}`)
    
    console.log('📡 [BackupService] 删除备份响应:', response)
    return response.data
  }

  /**
   * 删除设置备份
   */
  async deleteSettingsBackup(backupId: string): Promise<{
    success: boolean
    message: string
  }> {
    console.log('🔄 [BackupService] 删除设置备份:', backupId)

    const response = await this.delete<{
      success: boolean
      message: string
    }>(`backup/settings/${backupId}`)
    
    console.log('📡 [BackupService] 删除设置备份响应:', response)
    return response.data
  }

  /**
   * 获取备份详情
   */
  async getBackupDetails(backupId: string): Promise<BackupInfo & {
    contents: string[]
    metadata: Record<string, any>
    checksum: string
  }> {
    console.log('🔄 [BackupService] 获取备份详情:', backupId)

    const response = await this.get<BackupInfo & {
      contents: string[]
      metadata: Record<string, any>
      checksum: string
    }>(`backup/${backupId}/details`)
    
    console.log('📡 [BackupService] 备份详情响应:', response)
    return response.data
  }

  /**
   * 验证备份完整性
   */
  async validateBackup(backupId: string): Promise<{
    valid: boolean
    issues: string[]
    checksum: string
    size: number
  }> {
    console.log('🔄 [BackupService] 验证备份完整性:', backupId)

    const response = await this.post<{
      valid: boolean
      issues: string[]
      checksum: string
      size: number
    }>(`backup/${backupId}/validate`)
    
    console.log('📡 [BackupService] 备份验证响应:', response)
    return response.data
  }

  /**
   * 导出备份
   */
  async exportBackup(backupId: string, format: 'zip' | 'tar' | 'json' = 'zip'): Promise<Blob> {
    console.log('🔄 [BackupService] 导出备份:', backupId, format)

    const response = await this.get<any>(`backup/${backupId}/export`, { format })
    
    // 将响应数据转换为Blob
    const mimeType = format === 'json' ? 'application/json' : 'application/octet-stream'
    const blob = new Blob([response.data], { type: mimeType })
    
    console.log('📡 [BackupService] 备份导出完成')
    return blob
  }

  /**
   * 导入备份
   */
  async importBackup(file: File, options?: {
    name?: string
    overwrite?: boolean
    validate?: boolean
  }): Promise<BackupResponse> {
    console.log('🔄 [BackupService] 导入备份:', file.name, options)

    const formData = new FormData()
    formData.append('file', file)
    if (options) {
      formData.append('options', JSON.stringify(options))
    }

    // 使用原生fetch处理文件上传
    const response = await fetch(`${this.baseURL || 'http://localhost:8080'}/api/backup/import`, {
      method: 'POST',
      body: formData
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const result = await response.json()
    console.log('📡 [BackupService] 导入备份响应:', result)
    
    return result.data
  }

  /**
   * 获取备份统计
   */
  async getBackupStats(): Promise<{
    totalBackups: number
    totalSize: number
    byType: Record<string, number>
    oldestBackup: string
    newestBackup: string
    averageSize: number
  }> {
    console.log('🔄 [BackupService] 获取备份统计')

    const response = await this.get<{
      totalBackups: number
      totalSize: number
      byType: Record<string, number>
      oldestBackup: string
      newestBackup: string
      averageSize: number
    }>('backup/stats')
    
    console.log('📡 [BackupService] 备份统计响应:', response)
    return response.data
  }
}

// 创建备份服务实例
export const backupService = new BackupService()

// 添加默认拦截器
backupService.addRequestInterceptor(async (config) => {
  // 为备份操作添加特殊标识
  config.headers = {
    ...config.headers,
    'X-Service': 'BackupService',
    'X-Operation-Type': 'backup'
  }
  return config
})

backupService.addResponseInterceptor(async (response) => {
  // 记录备份操作时间
  console.log(`💾 [BackupService] 备份操作完成: ${Date.now()}`)
  return response
})

backupService.addErrorInterceptor(async (error) => {
  // 备份操作的错误处理
  console.error('❌ [BackupService] 备份操作错误:', {
    type: error.type,
    message: error.message,
    status: error.status
  })
  return error
})
