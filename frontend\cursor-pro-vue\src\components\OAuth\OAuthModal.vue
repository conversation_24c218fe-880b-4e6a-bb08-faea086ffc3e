<template>
  <BaseModal
    :visible="visible"
    title="OAuth 认证"
    width="500px"
    @close="handleClose"
  >
    <!-- OAuth选择界面 -->
    <div v-if="step === 'select'" class="oauth-select">
      <div class="oauth-title">选择认证方式</div>
      <div class="oauth-options">
        <button 
          class="oauth-btn google"
          @click="handleGoogleAuth"
          :disabled="loading"
        >
          <div class="oauth-icon">🔍</div>
          <div class="oauth-text">
            <div class="oauth-name">Google 认证</div>
            <div class="oauth-desc">使用Google账户登录</div>
          </div>
        </button>
        

      </div>
    </div>

    <!-- 认证进行中界面 -->
    <div v-else-if="step === 'processing'" class="oauth-processing">
      <div class="processing-spinner"></div>
      <div class="processing-text">{{ processingText }}</div>
      <div class="processing-tips">
        <p>• 请在弹出的浏览器窗口中完成认证</p>
        <p>• 认证完成后会自动返回</p>
        <p>• 请勿关闭此窗口</p>
      </div>
    </div>

    <!-- 认证结果界面 -->
    <div v-else-if="step === 'result'" class="oauth-result">
      <div v-if="success" class="result-success">
        <div class="result-icon">SUCCESS</div>
        <div class="result-title">认证成功！</div>
        <div class="result-info">
          <p><strong>邮箱:</strong> {{ resultData.email }}</p>
          <p><strong>认证类型:</strong> {{ resultData.authType }}</p>
        </div>
      </div>

      <div v-else class="result-error">
        <div class="result-icon">ERROR</div>
        <div class="result-title">认证失败</div>
        <div class="result-error-msg">{{ errorMessage }}</div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <button
        v-if="step === 'select'"
        class="card__button card__button--cancel"
        @click="handleClose"
      >
        取消
      </button>

      <button
        v-if="step === 'processing'"
        class="card__button card__button--cancel"
        @click="handleCancel"
      >
        取消认证
      </button>

      <div v-if="step === 'result'" style="display: flex; gap: 15px; justify-content: center;">
        <button
          v-if="success"
          class="card__button card__button--confirm"
          @click="handleClose"
        >
          完成
        </button>
        <button
          v-else
          class="card__button card__button--confirm"
          @click="handleRetry"
        >
          重试
        </button>
        <button
          class="card__button card__button--cancel"
          @click="handleClose"
        >
          关闭
        </button>
      </div>
    </template>
  </BaseModal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import BaseModal from '../Modal/BaseModal.vue'
import { realAPI } from '@/api/real-api'
import { useLogStore } from '@/stores/logs'

interface OAuthModalProps {
  visible: boolean
}

interface OAuthModalEvents {
  'close': []
  'success': [data: any]
  'update:visible': [visible: boolean]
}

const props = defineProps<OAuthModalProps>()
const emit = defineEmits<OAuthModalEvents>()

const logStore = useLogStore()

// 状态管理
const step = ref<'select' | 'processing' | 'result'>('select')
const loading = ref(false)
const success = ref(false)
const errorMessage = ref('')
const processingText = ref('')
const resultData = ref<any>({})

// 方法
const handleGoogleAuth = async () => {
  step.value = 'processing'
  processingText.value = '正在启动Google认证...'
  loading.value = true
  
  try {
    logStore.addInfo('开始Google OAuth认证', 'OAuth')
    
    const result = await realAPI.startGoogleOAuth()
    
    if (result.success) {
      success.value = true
      resultData.value = {
        email: result.email,
        authType: 'Google',
        accessToken: result.accessToken,
        refreshToken: result.refreshToken
      }
      
      logStore.addSuccess(`Google认证成功: ${result.email}`, 'OAuth')
      emit('success', resultData.value)
    } else {
      success.value = false
      errorMessage.value = result.error || 'Google认证失败'
      logStore.addError(`Google认证失败: ${errorMessage.value}`, 'OAuth')
    }
    
    step.value = 'result'
  } catch (error) {
    success.value = false
    errorMessage.value = `认证过程出错: ${(error as Error).message}`
    logStore.addError(errorMessage.value, 'OAuth')
    step.value = 'result'
  } finally {
    loading.value = false
  }
}



const handleCancel = () => {
  loading.value = false
  step.value = 'select'
  processingText.value = ''
  logStore.addInfo('用户取消OAuth认证', 'OAuth')
}

const handleRetry = () => {
  step.value = 'select'
  success.value = false
  errorMessage.value = ''
  resultData.value = {}
}

const handleClose = () => {
  // 重置状态
  step.value = 'select'
  loading.value = false
  success.value = false
  errorMessage.value = ''
  processingText.value = ''
  resultData.value = {}
  
  emit('close')
  emit('update:visible', false)
}
</script>

<style scoped>
.oauth-select {
  padding: 20px 0;
}

.oauth-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #e5e5e5;
  margin-bottom: 24px;
  text-align: center;
  font-family: 'Fira Mono', 'Consolas', monospace;
}

.oauth-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.oauth-btn {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  background: #1a1e23;
  border: 1px solid #374151;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Fira Mono', 'Consolas', monospace;
  gap: 16px;
}

.oauth-btn:hover:not(:disabled) {
  background: #252a31;
  border-color: #4b5563;
  transform: translateY(-2px);
}

.oauth-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.oauth-btn.google:hover:not(:disabled) {
  border-color: #4285f4;
  box-shadow: 0 4px 12px rgba(66, 133, 244, 0.2);
}



.oauth-icon {
  font-size: 2rem;
  min-width: 40px;
  text-align: center;
}

.oauth-text {
  flex: 1;
  text-align: left;
}

.oauth-name {
  font-size: 1rem;
  font-weight: 600;
  color: #e5e5e5;
  margin-bottom: 4px;
}

.oauth-desc {
  font-size: 0.85rem;
  color: #94a3b8;
}

.oauth-processing {
  padding: 40px 20px;
  text-align: center;
}

.processing-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #374151;
  border-top: 4px solid #60a5fa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.processing-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: #e5e5e5;
  margin-bottom: 20px;
  font-family: 'Fira Mono', 'Consolas', monospace;
}

.processing-tips {
  text-align: left;
  color: #94a3b8;
  font-size: 0.9rem;
  line-height: 1.6;
}

.processing-tips p {
  margin: 8px 0;
}

.oauth-result {
  padding: 20px;
  text-align: center;
}

.result-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.result-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 16px;
  font-family: 'Fira Mono', 'Consolas', monospace;
}

.result-success .result-title {
  color: #10b981;
}

.result-error .result-title {
  color: #f87171;
}

.result-info {
  text-align: left;
  background: #1a1e23;
  border: 1px solid #374151;
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.result-info p {
  margin: 8px 0;
  color: #e5e5e5;
  font-size: 0.9rem;
  font-family: 'Fira Mono', 'Consolas', monospace;
}

.result-error-msg {
  color: #f87171;
  font-size: 0.9rem;
  margin-top: 12px;
  padding: 12px;
  background: rgba(248, 113, 113, 0.1);
  border: 1px solid rgba(248, 113, 113, 0.3);
  border-radius: 6px;
}

.modal-btn {
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid;
  font-family: 'Fira Mono', 'Consolas', monospace;
  min-width: 80px;
}

.modal-btn {
  background: #374151;
  border-color: #4b5563;
  color: #e5e5e5;
}

.modal-btn:hover:not(:disabled) {
  background: #4b5563;
  border-color: #6b7280;
}

.modal-btn.primary {
  background: #10b981;
  border-color: #10b981;
  color: #fff;
}

/* 按钮样式 */
.card__button {
  border: 3px solid #000;
  background: #000;
  color: #fff;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: bold;
  text-transform: uppercase;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s;
  min-width: 100px;
}

.card__button--cancel {
  background: #dc2626;
  border-color: #dc2626;
}

.card__button--cancel::before {
  content: "Sure?";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 105%;
  background-color: #f87171;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.card__button--cancel:hover::before {
  transform: translateY(0);
}

.card__button--confirm::before {
  content: "Sure?";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 105%;
  background-color: #5ad641;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.card__button--confirm:hover::before {
  transform: translateY(0);
}

.card__button:active {
  transform: scale(0.95);
}
</style>
