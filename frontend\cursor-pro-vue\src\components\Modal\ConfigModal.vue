<template>
  <BaseModal
    :visible="visible"
    title="配置内容"
    width="600px"
    max-width="90vw"
    @close="handleClose"
  >
    <!-- 配置内容显示区域 -->
    <div class="config-modal-content">
      <div v-if="loading" class="config-loading">
        <div class="loading-spinner"></div>
        <p>正在加载配置信息...</p>
      </div>
      
      <div v-else-if="error" class="config-error">
        <div class="error-icon">❌</div>
        <p>{{ error }}</p>
      </div>
      
      <div v-else class="config-content">
        <pre class="config-text" v-html="formattedConfig"></pre>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <button
        class="card__button card__button--confirm"
        @click="handleCopy"
        :disabled="loading || !!error"
      >
        复制全部
      </button>
      <button
        class="card__button card__button--cancel"
        @click="handleClose"
      >
        关闭
      </button>
    </template>
  </BaseModal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BaseModal from './BaseModal.vue'
import { realAPI } from '@/api/real-api'
import { useLogStore } from '@/stores/logs'

interface ConfigModalProps {
  visible: boolean
}

interface ConfigModalEvents {
  'close': []
  'update:visible': [visible: boolean]
}

const props = defineProps<ConfigModalProps>()
const emit = defineEmits<ConfigModalEvents>()

const logStore = useLogStore()

// 状态
const loading = ref(false)
const error = ref('')
const configData = ref('')

// 计算属性
const formattedConfig = computed(() => {
  if (!configData.value) return ''
  
  let formatted = configData.value
  
  // 配置内容高亮美化（与原版一致）
  formatted = formatted.replace(/^【配置内容】/m, '<span style="color:#10b981;font-weight:bold;">【配置内容】</span>')
  formatted = formatted.replace(/^[-]{10,}/gm, '<span style="color:#60a5fa;">$&</span>')
  formatted = formatted.replace(/^\[.*?\]/gm, match => `<span style='color:#60a5fa;'>${match}</span>`)
  formatted = formatted.replace(/^\s*([\w_]+)\s*=\s*([^\n]+)$/gm, (m, k, v) => {
    return `<span style='color:#b5f4a5;'>${k}</span> = <span style='color:#fff;'>${v}</span>`
  })
  
  return formatted.replace(/\n/g, '<br>')
})

// 方法
const loadConfig = async () => {
  loading.value = true
  error.value = ''
  
  try {
    logStore.addInfo('正在获取配置信息...', 'ConfigModal')
    const config = await realAPI.showConfig()
    configData.value = config
    logStore.addSuccess('配置信息获取成功', 'ConfigModal')
  } catch (err) {
    const errorMsg = `获取配置信息失败: ${(err as Error).message}`
    error.value = errorMsg
    logStore.addError(errorMsg, 'ConfigModal')
  } finally {
    loading.value = false
  }
}

const handleCopy = async () => {
  if (!configData.value) return
  
  try {
    await navigator.clipboard.writeText(configData.value)
    logStore.addSuccess('配置信息已复制到剪贴板', 'ConfigModal')
    
    // 显示复制成功提示
    if (window.showCopyModal) {
      window.showCopyModal('配置信息已复制到剪贴板！')
    }
  } catch (err) {
    logStore.addError('复制失败，请手动选择复制', 'ConfigModal')
  }
}

const handleClose = () => {
  emit('close')
  emit('update:visible', false)
}

// 监听visible变化，自动加载配置
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    loadConfig()
  } else {
    // 关闭时清理数据
    configData.value = ''
    error.value = ''
  }
})
</script>

<style scoped>
.config-modal-content {
  min-height: 200px;
  max-height: 500px;
  overflow-y: auto;
}

.config-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #94a3b8;
  gap: 16px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #374151;
  border-top: 3px solid #60a5fa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.config-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #f87171;
  gap: 16px;
  text-align: center;
}

.error-icon {
  font-size: 2rem;
}

.config-content {
  padding: 16px 0;
}

.config-text {
  background: #181c20;
  border: 1px solid #2d323a;
  border-radius: 8px;
  padding: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  font-size: 0.9rem;
  line-height: 1.5;
  color: #e5e5e5;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  max-height: 400px;
  overflow-y: auto;
}

.config-text::-webkit-scrollbar {
  width: 6px;
}

.config-text::-webkit-scrollbar-track {
  background: transparent;
}

.config-text::-webkit-scrollbar-thumb {
  background: #64748b4d;
  border-radius: 3px;
}

.config-text::-webkit-scrollbar-thumb:hover {
  background: #64748b80;
}

.modal-btn {
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid;
  font-family: 'Fira Mono', 'Consolas', monospace;
  min-width: 80px;
}

.modal-btn {
  background: #374151;
  border-color: #4b5563;
  color: #e5e5e5;
}

.modal-btn:hover:not(:disabled) {
  background: #4b5563;
  border-color: #6b7280;
}

.modal-btn.primary {
  background: #10b981;
  border-color: #10b981;
  color: #fff;
}

/* 按钮样式 */
.card__button {
  border: 3px solid #000;
  background: #000;
  color: #fff;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: bold;
  text-transform: uppercase;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s;
  min-width: 100px;
}

.card__button--cancel {
  background: #dc2626;
  border-color: #dc2626;
}

.card__button--cancel::before {
  content: "Sure?";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 105%;
  background-color: #f87171;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.card__button--cancel:hover::before {
  transform: translateY(0);
}

.card__button--confirm::before {
  content: "Sure?";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 105%;
  background-color: #5ad641;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.card__button--confirm:hover::before {
  transform: translateY(0);
}

.card__button:active {
  transform: scale(0.95);
}

.card__button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.card__button:disabled::before {
  display: none;
}
</style>
