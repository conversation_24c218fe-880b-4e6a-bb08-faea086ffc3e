<template>
  <BaseModal
    :visible="visible"
    title="高级功能"
    width="600px"
    @close="handleClose"
  >
    <!-- 高级功能列表 -->
    <div class="advanced-content">
      <div class="advanced-section">
        <div class="section-title">版本管理</div>
        <div class="feature-grid">
          <button
            class="feature-btn"
            @click="handleBypassVersion"
            :disabled="loading"
          >
            <div class="feature-icon">UNLOCK</div>
            <div class="feature-text">
              <div class="feature-name">绕过版本检查</div>
              <div class="feature-desc">跳过Cursor版本限制</div>
            </div>
          </button>
        </div>
      </div>

      <div class="advanced-section">
        <div class="section-title">Token管理</div>
        <div class="feature-grid">
          <button 
            class="feature-btn"
            @click="handleBypassTokenLimit"
            :disabled="loading"
          >
            <div class="feature-icon">♾️</div>
            <div class="feature-text">
              <div class="feature-name">绕过Token限制</div>
              <div class="feature-desc">解除Token使用限制</div>
            </div>
          </button>
        </div>
      </div>

      <div class="advanced-section">
        <div class="section-title">机器ID管理</div>
        <div class="feature-grid">
          <button
            class="feature-btn"
            @click="handleResetMachineId"
            :disabled="loading"
          >
            <div class="feature-icon">RESET</div>
            <div class="feature-text">
              <div class="feature-name">重置机器ID</div>
              <div class="feature-desc">重新生成机器标识</div>
            </div>
          </button>

          <button 
            class="feature-btn"
            @click="handleFakeMachineId"
            :disabled="loading"
          >
            <div class="feature-icon">🎭</div>
            <div class="feature-text">
              <div class="feature-name">伪造机器ID</div>
              <div class="feature-desc">生成虚假机器标识</div>
            </div>
          </button>

          <button 
            class="feature-btn"
            @click="handleRestoreMachineId"
            :disabled="loading"
          >
            <div class="feature-icon">↩️</div>
            <div class="feature-text">
              <div class="feature-name">恢复机器ID</div>
              <div class="feature-desc">恢复原始机器标识</div>
            </div>
          </button>

          <button 
            class="feature-btn"
            @click="handleShowMachineId"
            :disabled="loading"
          >
            <div class="feature-icon">VIEW</div>
            <div class="feature-text">
              <div class="feature-name">查看机器ID</div>
              <div class="feature-desc">显示当前机器标识</div>
            </div>
          </button>
        </div>
      </div>

      <div class="advanced-section">
        <div class="section-title">⚠️ 危险操作</div>
        <div class="feature-grid">
          <button 
            class="feature-btn danger"
            @click="handleTotallyReset"
            :disabled="loading"
          >
            <div class="feature-icon">DANGER</div>
            <div class="feature-text">
              <div class="feature-name">完全重置</div>
              <div class="feature-desc">彻底重置所有设置</div>
            </div>
          </button>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">{{ loadingText }}</div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <button
        class="card__button card__button--cancel"
        @click="handleClose"
      >
        关闭
      </button>
    </template>
  </BaseModal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import BaseModal from '../Modal/BaseModal.vue'
import { realAPI } from '@/api/real-api'
import { useLogStore } from '@/stores/logs'
import { useAppStore } from '@/stores/app'

interface AdvancedModalProps {
  visible: boolean
}

interface AdvancedModalEvents {
  'close': []
  'update:visible': [visible: boolean]
}

const props = defineProps<AdvancedModalProps>()
const emit = defineEmits<AdvancedModalEvents>()

const logStore = useLogStore()
const appStore = useAppStore()

// 状态
const loading = ref(false)
const loadingText = ref('')

// 方法
const showResult = (title: string, content: string) => {
  if (window.showCopyModal) {
    window.showCopyModal(`${title}\n\n${content}`)
  }
}

const handleBypassVersion = async () => {
  loading.value = true
  loadingText.value = '正在绕过版本检查...'
  
  try {
    logStore.addInfo('开始绕过版本检查', 'Advanced')
    const result = await realAPI.bypassVersionCheck()
    logStore.addSuccess('版本检查绕过完成', 'Advanced')
    showResult('绕过版本检查', result)
  } catch (error) {
    logStore.addError(`绕过版本检查失败: ${(error as Error).message}`, 'Advanced')
  } finally {
    loading.value = false
  }
}

const handleBypassTokenLimit = async () => {
  loading.value = true
  loadingText.value = '正在绕过Token限制...'
  
  try {
    logStore.addInfo('开始绕过Token限制', 'Advanced')
    const result = await realAPI.bypassTokenLimit()
    logStore.addSuccess('Token限制绕过完成', 'Advanced')
    showResult('绕过Token限制', result)
  } catch (error) {
    logStore.addError(`绕过Token限制失败: ${(error as Error).message}`, 'Advanced')
  } finally {
    loading.value = false
  }
}

const handleResetMachineId = async () => {
  loading.value = true
  loadingText.value = '正在重置机器ID...'
  
  try {
    logStore.addInfo('开始重置机器ID', 'Advanced')
    const result = await realAPI.resetMachineId()
    logStore.addSuccess('机器ID重置完成', 'Advanced')
    showResult('重置机器ID', result)
  } catch (error) {
    logStore.addError(`重置机器ID失败: ${(error as Error).message}`, 'Advanced')
  } finally {
    loading.value = false
  }
}

const handleFakeMachineId = async () => {
  loading.value = true
  loadingText.value = '正在伪造机器ID...'
  
  try {
    logStore.addInfo('开始伪造机器ID', 'Advanced')
    const result = await realAPI.fakeMachineId()
    logStore.addSuccess('机器ID伪造完成', 'Advanced')
    showResult('伪造机器ID', result)
  } catch (error) {
    logStore.addError(`伪造机器ID失败: ${(error as Error).message}`, 'Advanced')
  } finally {
    loading.value = false
  }
}

const handleRestoreMachineId = async () => {
  loading.value = true
  loadingText.value = '正在恢复机器ID...'
  
  try {
    logStore.addInfo('开始恢复机器ID', 'Advanced')
    const result = await realAPI.restoreMachineId()
    logStore.addSuccess('机器ID恢复完成', 'Advanced')
    showResult('恢复机器ID', result)
  } catch (error) {
    logStore.addError(`恢复机器ID失败: ${(error as Error).message}`, 'Advanced')
  } finally {
    loading.value = false
  }
}

const handleShowMachineId = async () => {
  loading.value = true
  loadingText.value = '正在获取机器ID...'
  
  try {
    logStore.addInfo('开始获取机器ID', 'Advanced')
    const result = await realAPI.getMachineId()
    logStore.addSuccess('机器ID获取完成', 'Advanced')
    showResult('当前机器ID', result)
  } catch (error) {
    logStore.addError(`获取机器ID失败: ${(error as Error).message}`, 'Advanced')
  } finally {
    loading.value = false
  }
}

const handleTotallyReset = () => {
  appStore.showConfirm(
    '⚠️ 危险操作确认',
    '确定要执行完全重置吗？\n\n这将：\n• 清除所有配置\n• 重置所有设置\n• 删除所有数据\n\n此操作不可撤销！',
    async () => {
      loading.value = true
      loadingText.value = '正在执行完全重置...'
      
      try {
        logStore.addInfo('开始执行完全重置', 'Advanced')
        const result = await realAPI.totallyResetCursor()
        logStore.addSuccess('完全重置执行完成', 'Advanced')
        showResult('完全重置', result)
      } catch (error) {
        logStore.addError(`完全重置失败: ${(error as Error).message}`, 'Advanced')
      } finally {
        loading.value = false
      }
    }
  )
}

const handleClose = () => {
  emit('close')
  emit('update:visible', false)
}
</script>

<style scoped>
.advanced-content {
  position: relative;
  padding: 20px 0;
}

.advanced-section {
  margin-bottom: 32px;
}

.advanced-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #e5e5e5;
  margin-bottom: 16px;
  font-family: 'Fira Mono', 'Consolas', monospace;
  border-bottom: 1px solid #374151;
  padding-bottom: 8px;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 12px;
}

.feature-btn {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #1a1e23;
  border: 1px solid #374151;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Fira Mono', 'Consolas', monospace;
  gap: 12px;
  text-align: left;
}

.feature-btn:hover:not(:disabled) {
  background: #252a31;
  border-color: #4b5563;
  transform: translateY(-1px);
}

.feature-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.feature-btn.danger {
  border-color: #dc2626;
}

.feature-btn.danger:hover:not(:disabled) {
  background: rgba(220, 38, 38, 0.1);
  border-color: #ef4444;
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.2);
}

.feature-icon {
  font-size: 1.5rem;
  min-width: 32px;
  text-align: center;
}

.feature-text {
  flex: 1;
}

.feature-name {
  font-size: 0.95rem;
  font-weight: 600;
  color: #e5e5e5;
  margin-bottom: 4px;
}

.feature-desc {
  font-size: 0.8rem;
  color: #94a3b8;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(35, 39, 46, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  z-index: 10;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #374151;
  border-top: 3px solid #60a5fa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #e5e5e5;
  font-size: 0.9rem;
  font-family: 'Fira Mono', 'Consolas', monospace;
}

.modal-btn {
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid;
  font-family: 'Fira Mono', 'Consolas', monospace;
  min-width: 80px;
  background: #374151;
  border-color: #4b5563;
  color: #e5e5e5;
}

/* 按钮样式 */
.card__button {
  border: 3px solid #000;
  background: #000;
  color: #fff;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: bold;
  text-transform: uppercase;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s;
  min-width: 100px;
}

.card__button--cancel {
  background: #dc2626;
  border-color: #dc2626;
}

.card__button--cancel::before {
  content: "Sure?";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 105%;
  background-color: #f87171;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.card__button--cancel:hover::before {
  transform: translateY(0);
}

.card__button:active {
  transform: scale(0.95);
}
</style>
