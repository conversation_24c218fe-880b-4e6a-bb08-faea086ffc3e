@echo off
title Cursor Pro - Release Builder
color 0A

echo ========================================
echo  Cursor Pro - Release Builder
echo  Complete Project Packaging Tool
echo ========================================
echo.

echo This tool will build a complete release package for Cursor Pro.
echo.
echo The process includes:
echo [1] Building Python backend (PyInstaller)
echo [2] Building Electron frontend (npm + electron-builder)
echo [3] Creating unified installer package
echo [4] Generating documentation and scripts
echo.

echo Prerequisites:
echo - Python 3.8+ with pip
echo - Node.js 16+ with npm
echo - All project dependencies installed
echo.

set /p confirm="Continue with the build process? (Y/N): "
if /i "%confirm%" neq "Y" (
    echo Build cancelled.
    pause
    exit /b
)

echo.
echo ========================================
echo  Starting Build Process...
echo ========================================
echo.

REM 检查 Python
echo [Check] Verifying Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not found! Please install Python 3.8+
    pause
    exit /b 1
)
echo ✓ Python found

REM 检查 Node.js
echo [Check] Verifying Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found! Please install Node.js 16+
    pause
    exit /b 1
)
echo ✓ Node.js found

REM 检查 npm
echo [Check] Verifying npm installation...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm not found! Please install npm
    pause
    exit /b 1
)
echo ✓ npm found

echo.
echo ========================================
echo  Building Release Package...
echo ========================================
echo.

REM 运行完整打包脚本
echo [Build] Starting complete build process...
python build_all.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ Build process failed!
    echo Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo ========================================
echo  Build Complete!
echo ========================================
echo.

echo 🎉 Release package has been created successfully!
echo.
echo 📁 Check the following directories:
echo    - release_cursor_pro_* (folder)
echo    - release_cursor_pro_*.zip (archive)
echo.
echo 📋 Next steps:
echo 1. Test the package on a clean system
echo 2. Distribute the ZIP file to users
echo 3. Provide installation instructions
echo.
echo 🚀 Users can install by:
echo 1. Extract the ZIP file
echo 2. Run install.cmd as Administrator
echo 3. Follow the installation wizard
echo.

pause
