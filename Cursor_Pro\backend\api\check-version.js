// 智能版本控制API - 开发者可控制的版本发布系统
const fs = require('fs');
const path = require('path');

module.exports = (req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // 获取请求参数
  const currentVersion = req.query.current_version || '1.0.0';
  const appName = req.query.app_name || 'CursorPro';
  const fingerprint = req.query.fingerprint || 'unknown';

  console.log(`[${new Date().toISOString()}] 版本检查请求:`);
  console.log(`- 当前版本: ${currentVersion}`);
  console.log(`- 应用名称: ${appName}`);
  console.log(`- 设备指纹: ${fingerprint}`);

  try {
    // 读取版本配置
    const configPath = path.join(process.cwd(), 'api', 'version-config.json');
    let versionConfig;

    try {
      const configData = fs.readFileSync(configPath, 'utf8');
      versionConfig = JSON.parse(configData).version_control;
    } catch (error) {
      console.error('读取版本配置失败，使用默认配置:', error);
      // 默认配置
      versionConfig = {
        latest_version: "1.0.0",
        min_supported_version: "1.0.0",
        force_update: false,
        update_message: "当前版本是最新版本",
        download_message: "请到QQ群/微信群下载最新版本",
        download_url: "",
        maintenance_mode: false
      };
    }

    // 检查维护模式
    if (versionConfig.maintenance_mode) {
      return res.status(503).json({
        status: "maintenance",
        message: versionConfig.maintenance_message || "系统维护中，请稍后再试",
        timestamp: new Date().toISOString()
      });
    }

    // 版本比较函数
    const compareVersions = (v1, v2) => {
      const parts1 = v1.split('.').map(Number);
      const parts2 = v2.split('.').map(Number);
      const maxLength = Math.max(parts1.length, parts2.length);

      for (let i = 0; i < maxLength; i++) {
        const part1 = parts1[i] || 0;
        const part2 = parts2[i] || 0;
        if (part1 < part2) return -1;
        if (part1 > part2) return 1;
      }
      return 0;
    };

    // 检查当前版本状态
    const isLatest = compareVersions(currentVersion, versionConfig.latest_version) >= 0;
    const isSupported = compareVersions(currentVersion, versionConfig.min_supported_version) >= 0;
    const hasUpdate = compareVersions(currentVersion, versionConfig.latest_version) < 0;

    // 构建响应
    const response = {
      // Cursor兼容字段
      "hasUpdate": hasUpdate,
      "version": "0.42.4", // Cursor版本号（固定）
      "releaseNotes": versionConfig.update_message,
      "downloadUrl": versionConfig.download_url,
      "forceUpdate": versionConfig.force_update || !isSupported,

      // 版本控制系统字段
      "status": "success",
      "message": isLatest ? "当前版本是最新版本" : "发现新版本",
      "latest_version": versionConfig.latest_version,
      "min_supported_version": versionConfig.min_supported_version,
      "current_version": currentVersion,
      "force_update": versionConfig.force_update || !isSupported,
      "update_message": versionConfig.update_message,
      "download_message": versionConfig.download_message,
      "is_supported": isSupported,
      "is_latest": isLatest,
      "timestamp": new Date().toISOString(),

      // 统计信息
      "request_info": {
        "app_name": appName,
        "fingerprint": fingerprint,
        "user_agent": req.headers['user-agent'] || 'unknown'
      }
    };

    // 记录请求日志
    console.log(`版本检查结果: ${isLatest ? '最新版本' : '需要更新'}, 支持状态: ${isSupported ? '支持' : '不支持'}`);

    res.setHeader('Content-Type', 'application/json');
    res.status(200).json(response);

  } catch (error) {
    console.error('版本检查API错误:', error);

    res.status(500).json({
      "status": "error",
      "error": "Internal server error",
      "message": error.message,
      "timestamp": new Date().toISOString()
    });
  }
}
