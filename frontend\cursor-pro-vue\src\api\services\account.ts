/**
 * 账户相关API服务
 * 处理用户账户信息、认证、注册等操作
 */

import { BaseAPI } from '../base'
import type {
  AccountInfo,
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  RegisterResponse,
  OperationRequest,
  OperationResponse,
  OperationStatus
} from '../types'

/**
 * 账户服务类
 * 继承BaseAPI，提供账户相关的API调用
 */
export class AccountService extends BaseAPI {
  /**
   * 获取账户信息
   */
  async getAccountInfo(): Promise<AccountInfo | null> {
    console.log('🔄 [AccountService] 获取账户信息')

    try {
      const response = await this.get<AccountInfo>('account/info')
      console.log('📡 [AccountService] 账户信息响应:', response)
      
      return response.data
    } catch (error) {
      console.log('⚠️ [AccountService] 获取账户信息失败')
      return null
    }
  }

  /**
   * 更新账户信息
   */
  async updateAccountInfo(info: Partial<AccountInfo>): Promise<AccountInfo> {
    console.log('🔄 [AccountService] 更新账户信息:', info)

    const response = await this.put<AccountInfo>('account/info', info)
    console.log('📡 [AccountService] 更新账户信息响应:', response)
    
    return response.data
  }

  /**
   * 用户登录
   */
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    console.log('🔄 [AccountService] 用户登录:', { email: credentials.email })

    const response = await this.post<LoginResponse>('auth/login', credentials)
    console.log('📡 [AccountService] 登录响应:', response)
    
    return response.data
  }

  /**
   * 用户注册
   */
  async register(userData: RegisterRequest): Promise<RegisterResponse> {
    console.log('🔄 [AccountService] 用户注册:', { email: userData.email })

    const response = await this.post<RegisterResponse>('auth/register', userData)
    console.log('📡 [AccountService] 注册响应:', response)
    
    return response.data
  }

  /**
   * 用户登出
   */
  async logout(): Promise<boolean> {
    console.log('🔄 [AccountService] 用户登出')

    const response = await this.post<{ success: boolean }>('auth/logout')
    console.log('📡 [AccountService] 登出响应:', response)
    
    return response.data.success
  }

  /**
   * 刷新令牌
   */
  async refreshToken(refreshToken: string): Promise<{
    token: string
    refreshToken: string
    expiresIn: number
  }> {
    console.log('🔄 [AccountService] 刷新令牌')

    const response = await this.post<{
      token: string
      refreshToken: string
      expiresIn: number
    }>('auth/refresh', { refreshToken })
    
    console.log('📡 [AccountService] 令牌刷新响应:', response)
    return response.data
  }

  /**
   * 修改密码
   */
  async changePassword(oldPassword: string, newPassword: string): Promise<boolean> {
    console.log('🔄 [AccountService] 修改密码')

    const response = await this.post<{ success: boolean }>('account/change-password', {
      oldPassword,
      newPassword
    })
    
    console.log('📡 [AccountService] 修改密码响应:', response)
    return response.data.success
  }

  /**
   * 重置密码
   */
  async resetPassword(email: string): Promise<boolean> {
    console.log('🔄 [AccountService] 重置密码:', email)

    const response = await this.post<{ success: boolean }>('auth/reset-password', { email })
    console.log('📡 [AccountService] 重置密码响应:', response)
    
    return response.data.success
  }

  /**
   * 验证邮箱
   */
  async verifyEmail(token: string): Promise<boolean> {
    console.log('🔄 [AccountService] 验证邮箱')

    const response = await this.post<{ success: boolean }>('auth/verify-email', { token })
    console.log('📡 [AccountService] 邮箱验证响应:', response)
    
    return response.data.success
  }

  /**
   * 执行账户操作
   */
  async executeOperation(operation: OperationRequest): Promise<OperationResponse> {
    console.log('🔄 [AccountService] 执行账户操作:', operation)

    const response = await this.post<OperationResponse>('account/operation', operation)
    console.log('📡 [AccountService] 操作响应:', response)
    
    return response.data
  }

  /**
   * 获取操作状态
   */
  async getOperationStatus(operationId: string): Promise<OperationStatus> {
    console.log('🔄 [AccountService] 获取操作状态:', operationId)

    const response = await this.get<OperationStatus>(`account/operation/${operationId}/status`)
    console.log('📡 [AccountService] 操作状态响应:', response)
    
    return response.data
  }

  /**
   * 取消操作
   */
  async cancelOperation(operationId: string): Promise<boolean> {
    console.log('🔄 [AccountService] 取消操作:', operationId)

    const response = await this.post<{ success: boolean }>(`account/operation/${operationId}/cancel`)
    console.log('📡 [AccountService] 取消操作响应:', response)
    
    return response.data.success
  }

  /**
   * 获取账户统计信息
   */
  async getAccountStats(): Promise<{
    totalOperations: number
    successfulOperations: number
    failedOperations: number
    lastOperationTime: string
  }> {
    console.log('🔄 [AccountService] 获取账户统计')

    const response = await this.get<{
      totalOperations: number
      successfulOperations: number
      failedOperations: number
      lastOperationTime: string
    }>('account/stats')
    
    console.log('📡 [AccountService] 账户统计响应:', response)
    return response.data
  }

  /**
   * 删除账户
   */
  async deleteAccount(password: string): Promise<boolean> {
    console.log('🔄 [AccountService] 删除账户')

    const response = await this.delete<{ success: boolean }>('account')
    console.log('📡 [AccountService] 删除账户响应:', response)
    
    return response.data.success
  }

  /**
   * 导出账户数据
   */
  async exportAccountData(): Promise<Blob> {
    console.log('🔄 [AccountService] 导出账户数据')

    const response = await this.get<any>('account/export')
    
    // 将响应数据转换为Blob
    const blob = new Blob([JSON.stringify(response.data, null, 2)], {
      type: 'application/json'
    })
    
    console.log('📡 [AccountService] 账户数据导出完成')
    return blob
  }

  /**
   * 获取账户活动日志
   */
  async getAccountActivity(limit: number = 50): Promise<Array<{
    id: string
    action: string
    timestamp: string
    ip: string
    userAgent: string
    details?: any
  }>> {
    console.log('🔄 [AccountService] 获取账户活动日志')

    const response = await this.get<{
      activities: Array<{
        id: string
        action: string
        timestamp: string
        ip: string
        userAgent: string
        details?: any
      }>
    }>('account/activity', { limit })
    
    console.log('📡 [AccountService] 账户活动响应:', response)
    return response.data.activities
  }
}

// 创建账户服务实例
export const accountService = new AccountService()

// 添加默认拦截器
accountService.addRequestInterceptor(async (config) => {
  // 添加认证头
  const token = localStorage.getItem('auth_token')
  if (token) {
    config.headers = {
      ...config.headers,
      'Authorization': `Bearer ${token}`
    }
  }
  return config
})

accountService.addResponseInterceptor(async (response) => {
  // 处理认证相关响应
  if (response.data && typeof response.data === 'object') {
    // 如果响应包含新的token，保存它
    if ('token' in response.data) {
      localStorage.setItem('auth_token', response.data.token as string)
    }
    if ('refreshToken' in response.data) {
      localStorage.setItem('refresh_token', response.data.refreshToken as string)
    }
  }
  return response
})

accountService.addErrorInterceptor(async (error) => {
  // 处理认证错误
  if (error.status === 401) {
    // 清除过期的token
    localStorage.removeItem('auth_token')
    localStorage.removeItem('refresh_token')
    
    // 可以在这里触发重新登录
    console.log('🔄 [AccountService] 认证过期，需要重新登录')
  }
  return error
})
