const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron')

// 暴露安全的API给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 窗口控制API
  minimizeWindow: () => ipcRenderer.invoke('window-minimize'),
  maximizeWindow: () => ipcRenderer.invoke('window-maximize'),
  closeWindow: () => ipcRenderer.invoke('window-close'),
  isMaximized: () => ipcRenderer.invoke('window-is-maximized'),

  // 窗口状态变化监听
  onWindowStateChanged: (callback) => {
    ipcRenderer.on('window-state-changed', (event, data) => callback(data))
  },

  // 移除窗口状态变化监听
  removeWindowStateListener: () => {
    ipcRenderer.removeAllListeners('window-state-changed')
  },

  // 系统信息
  platform: process.platform,

  // 应用信息
  isElectron: true,

  // 版本信息
  versions: {
    node: process.versions.node,
    chrome: process.versions.chrome,
    electron: process.versions.electron
  }
})

// 在窗口加载完成后通知主进程
window.addEventListener('DOMContentLoaded', () => {
  console.log('🖥️ Electron preload script loaded')
  console.log('📱 Platform:', process.platform)
  console.log('⚡ Electron version:', process.versions.electron)
})
