#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Cursor Pro静默启动器
"""

import os
import time
import socket
import subprocess
import requests
from pathlib import Path

def check_port(host, port, timeout=5):
    """检查端口是否开放"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except:
        return False

def check_backend_api():
    """检查后端API是否响应"""
    try:
        response = requests.get('http://localhost:8080/api/check-version', timeout=5)
        return response.status_code == 200
    except:
        return False

def check_processes():
    """检查Cursor Pro进程"""
    try:
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq Cursor Pro.exe'], 
                              capture_output=True, text=True, shell=True)
        return 'Cursor Pro.exe' in result.stdout
    except:
        return False

def test_silent_launcher():
    """测试静默启动器"""
    print("🧪 Cursor Pro v4.0 静默启动器测试")
    print("=" * 50)
    
    # 检查文件是否存在
    launcher_path = "release_cursor_pro_final_v4/Cursor Pro.exe"
    if not os.path.exists(launcher_path):
        print("❌ 静默启动器文件不存在")
        return False
    
    print("✅ 静默启动器文件存在")
    
    # 启动前检查
    print("\n📋 启动前状态检查:")
    print(f"   后端端口8080: {'✅ 已占用' if check_port('localhost', 8080) else '❌ 未占用'}")
    print(f"   Cursor Pro进程: {'✅ 运行中' if check_processes() else '❌ 未运行'}")
    
    # 启动静默启动器
    print(f"\n🚀 启动静默启动器...")
    try:
        # 使用绝对路径启动
        abs_path = os.path.abspath(launcher_path)
        subprocess.Popen([abs_path], cwd=os.path.dirname(abs_path))
        print("✅ 启动器已执行")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    # 等待服务启动
    print("\n⏳ 等待服务启动...")
    for i in range(15):  # 等待最多15秒
        time.sleep(1)
        if check_port('localhost', 8080):
            print(f"✅ 后端服务已启动 (耗时: {i+1}秒)")
            break
        print(f"   等待中... ({i+1}/15)")
    else:
        print("❌ 后端服务启动超时")
        return False
    
    # 检查API响应
    print("\n🔍 检查API响应...")
    if check_backend_api():
        print("✅ 后端API响应正常")
    else:
        print("❌ 后端API无响应")
    
    # 检查进程状态
    print("\n📊 最终状态检查:")
    print(f"   后端端口8080: {'✅ 正常' if check_port('localhost', 8080) else '❌ 异常'}")
    print(f"   Cursor Pro进程: {'✅ 运行中' if check_processes() else '❌ 未运行'}")
    print(f"   API响应: {'✅ 正常' if check_backend_api() else '❌ 异常'}")
    
    print("\n🎉 测试完成！")
    print("💡 提示: 如果看到前端界面，说明静默启动器工作正常")
    
    return True

def main():
    """主函数"""
    try:
        test_silent_launcher()
    except KeyboardInterrupt:
        print("\n\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")

if __name__ == "__main__":
    main()
