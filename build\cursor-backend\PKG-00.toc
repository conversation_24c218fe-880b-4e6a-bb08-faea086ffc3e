('C:\\Users\\<USER>\\Desktop\\cursor-pro\\build\\cursor-backend\\cursor-backend.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\build\\cursor-backend\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\build\\cursor-backend\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\build\\cursor-backend\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\build\\cursor-backend\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\build\\cursor-backend\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\build\\cursor-backend\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('main', 'C:\\Users\\<USER>\\Desktop\\cursor-pro\\main.py', 'PYSOURCE'),
  ('selenium\\webdriver\\common\\windows\\selenium-manager.exe',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\selenium\\webdriver\\common\\windows\\selenium-manager.exe',
   'BINARY'),
  ('python313.dll', 'D:\\Python\\python313.dll', 'BINARY'),
  ('_lzma.pyd', 'D:\\Python\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\Python\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'D:\\Python\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\Python\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('select.pyd', 'D:\\Python\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\Python\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'D:\\Python\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_overlapped.pyd', 'D:\\Python\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\Python\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\Python\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\Python\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Python\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'D:\\Python\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'D:\\Python\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'D:\\Python\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\PIL\\_webp.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\PIL\\_imagingtk.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_avif.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\PIL\\_avif.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\PIL\\_imagingcms.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\PIL\\_imagingmath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd', 'D:\\Python\\DLLs\\_elementtree.pyd', 'EXTENSION'),
  ('PIL\\_imaging.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\PIL\\_imaging.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\lxml\\etree.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\lxml\\_elementpath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\lxml\\sax.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\lxml\\objectify.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\lxml\\html\\diff.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\_difflib.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\lxml\\html\\_difflib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\lxml\\builder.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'D:\\Python\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('markupsafe\\_speedups.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\markupsafe\\_speedups.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('websockets\\speedups.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\websockets\\speedups.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'D:\\Python\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll', 'D:\\Python\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libssl-3.dll', 'D:\\Python\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libffi-8.dll', 'D:\\Python\\DLLs\\libffi-8.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll', 'D:\\Python\\VCRUNTIME140_1.dll', 'BINARY'),
  ('python3.dll', 'D:\\Python\\python3.dll', 'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-kernel32-legacy-l1-1-1.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-kernel32-legacy-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-2-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-sysinfo-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-1.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-fibers-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jdk-*********-hotspot\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('README.md', 'C:\\Users\\<USER>\\Desktop\\cursor-pro\\README.md', 'DATA'),
  ('api\\admin.js',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\api\\admin.js',
   'DATA'),
  ('api\\check-version.js',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\api\\check-version.js',
   'DATA'),
  ('api\\index.js',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\api\\index.js',
   'DATA'),
  ('api\\version-config.json',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\api\\version-config.json',
   'DATA'),
  ('database\\config.py',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\database\\config.py',
   'DATA'),
  ('database\\sqlite_config.py',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\database\\sqlite_config.py',
   'DATA'),
  ('database\\sqlite_init.sql',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\database\\sqlite_init.sql',
   'DATA'),
  ('docs\\免责声明.md',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\docs\\免责声明.md',
   'DATA'),
  ('docs\\反检测指南.md',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\docs\\反检测指南.md',
   'DATA'),
  ('docs\\安全说明.md',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\docs\\安全说明.md',
   'DATA'),
  ('docs\\完整安装指南.md',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\docs\\完整安装指南.md',
   'DATA'),
  ('docs\\快速上手.md',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\docs\\快速上手.md',
   'DATA'),
  ('docs\\技术支持.md',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\docs\\技术支持.md',
   'DATA'),
  ('docs\\接口文档.md',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\docs\\接口文档.md',
   'DATA'),
  ('docs\\数据存储指南.md',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\docs\\数据存储指南.md',
   'DATA'),
  ('docs\\文档目录.md',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\docs\\文档目录.md',
   'DATA'),
  ('docs\\版本管理指南.md',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\docs\\版本管理指南.md',
   'DATA'),
  ('docs\\配置指南.md',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\docs\\配置指南.md',
   'DATA'),
  ('docs\\项目说明.md',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\docs\\项目说明.md',
   'DATA'),
  ('email_tabs\\email_tab_interface.py',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\email_tabs\\email_tab_interface.py',
   'DATA'),
  ('email_tabs\\tempmail_plus_tab.py',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\email_tabs\\tempmail_plus_tab.py',
   'DATA'),
  ('locales\\zh_cn.json',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\locales\\zh_cn.json',
   'DATA'),
  ('locales\\zh_tw.json',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\locales\\zh_tw.json',
   'DATA'),
  ('package-lock.json',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\package-lock.json',
   'DATA'),
  ('package.json',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\package.json',
   'DATA'),
  ('public\\index.html',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\public\\index.html',
   'DATA'),
  ('requirements.txt',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\requirements.txt',
   'DATA'),
  ('快速上手.md', 'C:\\Users\\<USER>\\Desktop\\cursor-pro\\快速上手.md', 'DATA'),
  ('文档目录.md', 'C:\\Users\\<USER>\\Desktop\\cursor-pro\\文档目录.md', 'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('selenium\\webdriver\\common\\linux\\selenium-manager',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\selenium\\webdriver\\common\\linux\\selenium-manager',
   'DATA'),
  ('selenium\\py.typed',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\selenium\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\macos\\selenium-manager',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\selenium\\webdriver\\common\\macos\\selenium-manager',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v138\\py.typed',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v138\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\remote\\findElements.js',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\findElements.js',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v136\\py.typed',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v136\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\remote\\getAttribute.js',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\getAttribute.js',
   'DATA'),
  ('selenium\\webdriver\\remote\\isDisplayed.js',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\selenium\\webdriver\\remote\\isDisplayed.js',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v137\\py.typed',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v137\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'DATA'),
  ('selenium\\webdriver\\common\\mutation-listener.js',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\selenium\\webdriver\\common\\mutation-listener.js',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('flask-3.1.1.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\REQUESTED',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('websockets-15.0.1.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\websockets-15.0.1.dist-info\\LICENSE',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('flask-3.1.1.dist-info\\licenses\\LICENSE.txt',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('flask-3.1.1.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\entry_points.txt',
   'DATA'),
  ('flask-3.1.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.1.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\METADATA',
   'DATA'),
  ('flask-3.1.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\METADATA',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('websockets-15.0.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\websockets-15.0.1.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('websockets-15.0.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\websockets-15.0.1.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('websockets-15.0.1.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\websockets-15.0.1.dist-info\\top_level.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('websockets-15.0.1.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\websockets-15.0.1.dist-info\\entry_points.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('websockets-15.0.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\websockets-15.0.1.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.1.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\flask-3.1.1.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'DATA'),
  ('websockets-15.0.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\websockets-15.0.1.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\RECORD',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\.venv\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\cursor-pro\\build\\cursor-backend\\base_library.zip',
   'DATA')],
 'python313.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
