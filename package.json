{"name": "cursor-pro-api", "version": "1.1.0", "description": "Cursor Pro API for version check bypass", "main": "api/index.js", "scripts": {"dev": "vercel dev", "build": "mkdir -p public && echo '{\"message\":\"Cursor Pro API\",\"status\":\"ready\"}' > public/index.html", "start": "vercel dev"}, "keywords": ["cursor", "api", "vercel", "bypass"], "author": "tul345", "license": "MIT", "dependencies": {}, "devDependencies": {}, "engines": {"node": ">=18.0.0"}}