"""
Cursor Pro - 专业的Cursor账户管理工具

这是一个用于管理Cursor账户的专业工具包，提供以下功能：
- 账户认证和管理
- 反检测机制
- 数据备份和恢复
- 配置管理
- 工具集合

主要模块：
- core: 核心功能模块
- auth: 认证相关模块
- backup: 备份相关模块
- anti_detection: 反检测模块
- management: 管理模块
- utils: 工具模块
- database: 数据库模块
"""

__version__ = "1.1.0"
__author__ = "Cursor Pro Team"
__description__ = "专业的Cursor账户管理工具"

# 导入核心功能
from .core import main
from .core import api_server

# 导入常用工具
from .utils import common
from .utils import app_version

# 版本信息
def get_version():
    """获取版本信息"""
    return __version__

def get_info():
    """获取包信息"""
    return {
        "name": "cursor_pro",
        "version": __version__,
        "author": __author__,
        "description": __description__
    }

# 公共接口
__all__ = [
    "main",
    "api_server", 
    "common",
    "app_version",
    "get_version",
    "get_info"
]
