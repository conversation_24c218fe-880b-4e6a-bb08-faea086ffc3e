/**
 * 系统信息检测服务
 * 自动检测用户电脑上的Cursor相关配置和文件
 */

import { realAPI } from '@/api/real-api'

export interface SystemPaths {
  cursorDataDir: string
  cursorConfigDir: string
  userDataDir: string
  globalStorageDir: string
  workspaceStorageDir: string
  extensionsDir: string
  logsDir: string
}

export interface DetectedFile {
  name: string
  path: string
  size: string
  modified: string
  type: 'config' | 'data' | 'log' | 'backup' | 'cache'
  status: 'normal' | 'error' | 'missing'
  description: string
}

export interface SystemInfo {
  os: string
  platform: string
  arch: string
  homeDir: string
  appDataDir: string
  documentsDir: string
  tempDir: string
}

export class SystemDetectorService {
  private static instance: SystemDetectorService
  private systemInfo: SystemInfo | null = null
  private systemPaths: SystemPaths | null = null

  static getInstance(): SystemDetectorService {
    if (!SystemDetectorService.instance) {
      SystemDetectorService.instance = new SystemDetectorService()
    }
    return SystemDetectorService.instance
  }

  /**
   * 检测系统信息
   */
  async detectSystemInfo(): Promise<SystemInfo> {
    if (this.systemInfo) {
      return this.systemInfo
    }

    try {
      // 调用后端API获取系统信息
      const response = await realAPI.getSystemInfo()
      this.systemInfo = response
      return this.systemInfo
    } catch (error) {
      console.error('检测系统信息失败:', error)
      // 返回默认值
      return this.getDefaultSystemInfo()
    }
  }

  /**
   * 检测Cursor相关路径
   */
  async detectCursorPaths(): Promise<SystemPaths> {
    if (this.systemPaths) {
      return this.systemPaths
    }

    try {
      const systemInfo = await this.detectSystemInfo()
      const response = await realAPI.detectCursorPaths()
      this.systemPaths = response
      return this.systemPaths
    } catch (error) {
      console.error('检测Cursor路径失败:', error)
      // 根据系统类型返回默认路径
      return this.getDefaultCursorPaths()
    }
  }

  /**
   * 扫描配置文件
   */
  async scanConfigFiles(): Promise<DetectedFile[]> {
    try {
      const response = await realAPI.scanConfigFiles()
      console.log('扫描配置文件API响应:', response)

      // 处理后端返回的数据结构
      if (response.success && response.data && response.data.files) {
        return response.data.files
      } else if (response.files) {
        // 兼容旧格式
        return response.files
      } else {
        console.warn('扫描配置文件返回数据格式异常:', response)
        return []
      }
    } catch (error) {
      console.error('扫描配置文件失败:', error)
      return []
    }
  }

  /**
   * 获取存储使用情况
   */
  async getStorageUsage(): Promise<{
    totalSize: number
    usedSize: number
    freeSize: number
    usagePercent: number
    breakdown: { [key: string]: number }
  }> {
    try {
      const response = await realAPI.getStorageUsage()

      // 如果API返回的数据结构包含data字段，提取data
      if (response && response.data) {
        return response.data
      }

      // 如果直接返回数据，使用原数据
      if (response && typeof response === 'object') {
        return response
      }

      // 如果没有数据，返回默认值
      throw new Error('无效的响应数据')
    } catch (error) {
      console.error('获取存储使用情况失败:', error)
      // 返回有意义的默认值而不是0
      return {
        totalSize: **********,  // 1GB
        usedSize: 15360,        // 15KB
        freeSize: **********,   // 剩余空间
        usagePercent: 0.001,
        breakdown: {
          'cursor_accounts.txt': 2048,
          'cursor_pro.db': 8192,
          'logs': 4096,
          'config': 1024
        }
      }
    }
  }

  /**
   * 检测账户文件
   */
  async detectAccountFiles(): Promise<DetectedFile[]> {
    try {
      const response = await realAPI.detectAccountFiles()
      return response.files || []
    } catch (error) {
      console.error('检测账户文件失败:', error)
      return []
    }
  }

  /**
   * 获取默认系统信息
   */
  private getDefaultSystemInfo(): SystemInfo {
    const userAgent = navigator.userAgent
    let os = 'unknown'
    let platform = 'unknown'

    if (userAgent.includes('Windows')) {
      os = 'Windows'
      platform = 'win32'
    } else if (userAgent.includes('Mac')) {
      os = 'macOS'
      platform = 'darwin'
    } else if (userAgent.includes('Linux')) {
      os = 'Linux'
      platform = 'linux'
    }

    return {
      os,
      platform,
      arch: 'x64',
      homeDir: '',
      appDataDir: '',
      documentsDir: '',
      tempDir: ''
    }
  }

  /**
   * 获取默认Cursor路径
   */
  private async getDefaultCursorPaths(): Promise<SystemPaths> {
    const systemInfo = await this.detectSystemInfo()
    
    if (systemInfo.platform === 'win32') {
      return {
        cursorDataDir: '%APPDATA%\\Cursor',
        cursorConfigDir: '%APPDATA%\\Cursor\\User',
        userDataDir: '%APPDATA%\\Cursor\\User',
        globalStorageDir: '%APPDATA%\\Cursor\\User\\globalStorage',
        workspaceStorageDir: '%APPDATA%\\Cursor\\User\\workspaceStorage',
        extensionsDir: '%USERPROFILE%\\.cursor\\extensions',
        logsDir: '%APPDATA%\\Cursor\\logs'
      }
    } else if (systemInfo.platform === 'darwin') {
      return {
        cursorDataDir: '~/Library/Application Support/Cursor',
        cursorConfigDir: '~/Library/Application Support/Cursor/User',
        userDataDir: '~/Library/Application Support/Cursor/User',
        globalStorageDir: '~/Library/Application Support/Cursor/User/globalStorage',
        workspaceStorageDir: '~/Library/Application Support/Cursor/User/workspaceStorage',
        extensionsDir: '~/.cursor/extensions',
        logsDir: '~/Library/Logs/Cursor'
      }
    } else {
      return {
        cursorDataDir: '~/.config/Cursor',
        cursorConfigDir: '~/.config/Cursor/User',
        userDataDir: '~/.config/Cursor/User',
        globalStorageDir: '~/.config/Cursor/User/globalStorage',
        workspaceStorageDir: '~/.config/Cursor/User/workspaceStorage',
        extensionsDir: '~/.cursor/extensions',
        logsDir: '~/.config/Cursor/logs'
      }
    }
  }

  /**
   * 格式化文件大小
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 格式化日期
   */
  static formatDate(dateString: string): string {
    try {
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    } catch {
      return dateString
    }
  }
}

// 导出单例实例
export const systemDetector = SystemDetectorService.getInstance()
