<template>
  <Teleport to="body">
    <div v-if="visible" class="modal-mask" @click="handleCancel">
      <div class="card modern-confirm-card" :data-type="type" @click.stop>
        <span class="card__title">{{ title }}</span>

        <div class="card__content">
          <p>{{ message }}</p>

          <!-- 账户信息 -->
          <div v-if="accountInfo" class="account-info">
            <div class="info-item">
              <span class="info-label">邮箱:</span>
              <span class="info-value">{{ accountInfo.email }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">密码:</span>
              <span class="info-value">{{ accountInfo.password || '***' }}</span>
            </div>
          </div>

          <!-- 警告信息 -->
          <div v-if="warnings && warnings.length" class="warnings">
            <div class="warning-header">
              <span>警告 - 这将：</span>
            </div>
            <ul class="warning-list">
              <li v-for="warning in warnings" :key="warning">{{ warning }}</li>
            </ul>
          </div>
        </div>

        <div class="card__form">
          <button class="card__button card__button--cancel" @click="handleCancel">
            {{ cancelText }}
          </button>
          <button :class="['card__button', 'card__button--confirm', `card__button--${type}`]" @click="handleConfirm">
            {{ confirmText }}
          </button>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
interface Props {
  visible: boolean
  type?: 'info' | 'warning' | 'danger'
  title: string
  message: string
  confirmText?: string
  cancelText?: string
  accountInfo?: {
    email: string
    password?: string
  }
  warnings?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  type: 'info',
  confirmText: '确定',
  cancelText: '取消'
})

const emit = defineEmits<{
  confirm: []
  cancel: []
}>()

const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
/* From Uiverse.io by 0xnihilism */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.card {
  width: 480px;
  max-width: 90vw;
  padding: 20px;
  background: #fff;
  border: 6px solid #000;
  box-shadow: 12px 12px 0 #000;
  transition: transform 0.3s, box-shadow 0.3s;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.card:hover {
  transform: translate(-5px, -5px);
  box-shadow: 17px 17px 0 #000;
}

.card__title {
  font-size: 32px;
  font-weight: 900;
  color: #000;
  text-transform: uppercase;
  margin-bottom: 15px;
  display: block;
  position: relative;
  overflow: hidden;
}

.card__title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 90%;
  height: 3px;
  background-color: #000;
  transform: translateX(-100%);
  transition: transform 0.3s;
}

.card:hover .card__title::after {
  transform: translateX(0);
}

.card__content {
  font-size: 16px;
  line-height: 1.4;
  color: #000;
  margin-bottom: 20px;
}

.card__content p {
  margin: 0 0 15px 0;
}

.account-info {
  background: #f0f0f0;
  border: 3px solid #000;
  padding: 16px;
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  color: #000;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #000;
  font-size: 14px;
  min-width: 40px;
  font-weight: bold;
}

.info-value {
  color: #000;
  font-size: 14px;
  font-family: 'Courier New', monospace;
}

.warnings {
  background: #fff3cd;
  border: 3px solid #000;
  padding: 16px;
  margin-top: 15px;
}

.warning-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #000;
  font-weight: 900;
  font-size: 14px;
  margin-bottom: 12px;
  text-transform: uppercase;
}

.warning-list {
  margin: 0;
  padding-left: 20px;
  color: #000;
}

.warning-list li {
  margin-bottom: 4px;
  font-size: 14px;
  font-weight: bold;
}

.card__form {
  display: flex;
  flex-direction: row;
  gap: 15px;
  justify-content: center;
}

.card__button {
  border: 3px solid #000;
  background: #000;
  color: #fff;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: bold;
  text-transform: uppercase;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s;
  min-width: 100px;
}

.card__button--cancel {
  background: #dc2626;
  border-color: #dc2626;
}

.card__button--cancel::before {
  content: "Sure?";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 105%;
  background-color: #f87171;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.card__button--cancel:hover::before {
  transform: translateY(0);
}

.card__button--confirm::before,
.card__button--warning::before,
.card__button--danger::before,
.card__button--info::before {
  content: "Sure?";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 105%;
  background-color: #5ad641;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.card__button--confirm:hover::before,
.card__button--warning:hover::before,
.card__button--danger:hover::before,
.card__button--info:hover::before {
  transform: translateY(0);
}

.card__button--warning {
  background: #f59e0b;
  border-color: #f59e0b;
}

.card__button--danger {
  background: #ef4444;
  border-color: #ef4444;
}

.card__button--info {
  background: #3b82f6;
  border-color: #3b82f6;
}

.card__button:active {
  transform: scale(0.95);
}

@keyframes glitch {
  0% {
    transform: translate(2px, 2px);
  }
  25% {
    transform: translate(-2px, -2px);
  }
  50% {
    transform: translate(-2px, 2px);
  }
  75% {
    transform: translate(2px, -2px);
  }
  100% {
    transform: translate(2px, 2px);
  }
}

.glitch {
  animation: glitch 0.3s infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card {
    width: 90vw;
    max-width: 400px;
  }

  .card__form {
    flex-direction: column;
  }

  .card__button {
    width: 100%;
  }
}
</style>
