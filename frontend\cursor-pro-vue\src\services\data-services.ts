/**
 * 专门的数据服务类
 * 为不同类型的数据提供专门的管理服务
 */

import { dataManager, DataLocation, type AccountData, type ConfigData, type RegisterConfig, type BackupInfo } from '@/utils/data-manager'
import { ElMessage } from 'element-plus'

/**
 * 账户数据服务
 */
export class AccountDataService {
  private static readonly STORAGE_KEY = 'cursor-accounts'
  private static readonly LOCATION = DataLocation.API_SERVER

  /**
   * 获取所有账户
   */
  static async getAllAccounts(): Promise<AccountData[]> {
    try {
      const accounts = await dataManager.getData<AccountData[]>(
        this.STORAGE_KEY,
        this.LOCATION,
        []
      )
      return accounts || []
    } catch (error) {
      console.error('❌ 获取账户列表失败:', error)
      return []
    }
  }

  /**
   * 添加账户
   */
  static async addAccount(account: Omit<AccountData, 'createdAt' | 'lastUsed'>): Promise<boolean> {
    try {
      const accounts = await this.getAllAccounts()
      
      // 检查邮箱是否已存在
      if (accounts.some(acc => acc.email === account.email)) {
        ElMessage.warning('该邮箱账户已存在')
        return false
      }

      const newAccount: AccountData = {
        ...account,
        createdAt: new Date().toISOString(),
        lastUsed: new Date().toISOString()
      }

      accounts.push(newAccount)

      const success = await dataManager.setData(
        this.STORAGE_KEY,
        accounts,
        this.LOCATION,
        { backup: true, validate: true, encrypt: true }
      )

      if (success) {
        ElMessage.success('账户添加成功')
      } else {
        ElMessage.error('账户添加失败')
      }

      return success
    } catch (error) {
      console.error('❌ 添加账户失败:', error)
      ElMessage.error('添加账户时发生错误')
      return false
    }
  }

  /**
   * 更新账户
   */
  static async updateAccount(email: string, updates: Partial<AccountData>): Promise<boolean> {
    try {
      const accounts = await this.getAllAccounts()
      const index = accounts.findIndex(acc => acc.email === email)
      
      if (index === -1) {
        ElMessage.warning('账户不存在')
        return false
      }

      accounts[index] = { ...accounts[index], ...updates }

      const success = await dataManager.setData(
        this.STORAGE_KEY,
        accounts,
        this.LOCATION,
        { backup: true, validate: true, encrypt: true }
      )

      if (success) {
        ElMessage.success('账户更新成功')
      } else {
        ElMessage.error('账户更新失败')
      }

      return success
    } catch (error) {
      console.error('❌ 更新账户失败:', error)
      ElMessage.error('更新账户时发生错误')
      return false
    }
  }

  /**
   * 删除账户
   */
  static async deleteAccount(email: string): Promise<boolean> {
    try {
      const accounts = await this.getAllAccounts()
      const filteredAccounts = accounts.filter(acc => acc.email !== email)
      
      if (filteredAccounts.length === accounts.length) {
        ElMessage.warning('账户不存在')
        return false
      }

      const success = await dataManager.setData(
        this.STORAGE_KEY,
        filteredAccounts,
        this.LOCATION,
        { backup: true }
      )

      if (success) {
        ElMessage.success('账户删除成功')
      } else {
        ElMessage.error('账户删除失败')
      }

      return success
    } catch (error) {
      console.error('❌ 删除账户失败:', error)
      ElMessage.error('删除账户时发生错误')
      return false
    }
  }

  /**
   * 更新账户使用时间
   */
  static async updateLastUsed(email: string): Promise<void> {
    await this.updateAccount(email, { lastUsed: new Date().toISOString() })
  }
}

/**
 * 配置数据服务
 */
export class ConfigDataService {
  private static readonly STORAGE_KEY = 'app-config'
  private static readonly LOCATION = DataLocation.API_SERVER

  /**
   * 获取配置
   */
  static async getConfig(): Promise<ConfigData> {
    try {
      const config = await dataManager.getData<ConfigData>(
        this.STORAGE_KEY,
        this.LOCATION,
        this.getDefaultConfig()
      )
      return config || this.getDefaultConfig()
    } catch (error) {
      console.error('❌ 获取配置失败:', error)
      return this.getDefaultConfig()
    }
  }

  /**
   * 保存配置
   */
  static async saveConfig(config: ConfigData): Promise<boolean> {
    try {
      const success = await dataManager.setData(
        this.STORAGE_KEY,
        config,
        this.LOCATION,
        { backup: true, validate: true }
      )

      if (success) {
        ElMessage.success('配置保存成功')
      } else {
        ElMessage.error('配置保存失败')
      }

      return success
    } catch (error) {
      console.error('❌ 保存配置失败:', error)
      ElMessage.error('保存配置时发生错误')
      return false
    }
  }

  /**
   * 重置配置
   */
  static async resetConfig(): Promise<boolean> {
    try {
      const success = await dataManager.setData(
        this.STORAGE_KEY,
        this.getDefaultConfig(),
        this.LOCATION,
        { backup: true }
      )

      if (success) {
        ElMessage.success('配置重置成功')
      } else {
        ElMessage.error('配置重置失败')
      }

      return success
    } catch (error) {
      console.error('❌ 重置配置失败:', error)
      ElMessage.error('重置配置时发生错误')
      return false
    }
  }

  /**
   * 获取默认配置
   */
  private static getDefaultConfig(): ConfigData {
    return {
      general: {
        browserPath: 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
        driverPath: 'chromedriver.exe',
        showBrowser: false,
        timeout: 300,
        retryCount: 3
      },
      tempmail: {
        enabled: false,
        email: '',
        epin: ''
      },
      oauth: {
        showSelectionAlert: false,
        timeout: 120,
        maxAttempts: 3
      },
      language: {
        current: 'zh-CN',
        fallback: 'en',
        autoUpdate: true
      }
    }
  }
}

/**
 * 注册配置服务
 */
export class RegisterConfigService {
  /**
   * 获取API URL - 根据环境自动选择
   */
  private static getApiUrl(path: string): string {
    // 在Electron环境中，使用完整的URL
    if (typeof window !== 'undefined' && window.electronAPI) {
      return `http://localhost:8080${path}`
    }
    // 在浏览器环境中，使用相对路径（Vite会代理）
    return path
  }
  /**
   * 获取注册配置
   */
  static async getRegisterConfig(): Promise<RegisterConfig> {
    try {
      console.log('🔄 发送获取配置请求')

      // 根据环境使用正确的API地址
      const apiUrl = this.getApiUrl('/api/get_register_config')
      const response = await fetch(apiUrl)
      console.log('📡 响应状态:', response.status, response.statusText)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ API响应错误:', errorText)
        return this.getDefaultRegisterConfig()
      }

      const result = await response.json()
      console.log('📡 API响应结果:', result)

      if (result.success && result.data && result.data.data) {
        console.log('✅ 成功获取配置:', result.data.data)
        return result.data.data
      } else {
        console.log('⚠️ 使用默认注册配置')
        return this.getDefaultRegisterConfig()
      }
    } catch (error) {
      console.error('❌ 获取注册配置失败:', error)
      return this.getDefaultRegisterConfig()
    }
  }

  /**
   * 保存注册配置
   */
  static async saveRegisterConfig(config: RegisterConfig): Promise<boolean> {
    try {
      console.log('🔄 发送保存配置请求:', config)

      // 根据环境使用正确的API地址
      const apiUrl = this.getApiUrl('/api/save_register_config')
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      })

      console.log('📡 响应状态:', response.status, response.statusText)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ API响应错误:', errorText)
        ElMessage.error(`保存配置失败: ${response.status} ${response.statusText}`)
        return false
      }

      const result = await response.json()
      console.log('📡 API响应结果:', result)

      if (result.success && result.data && result.data.success) {
        ElMessage.success('注册配置保存成功')
        return true
      } else {
        const errorMsg = result.data?.error || result.error || '未知错误'
        console.error('❌ 保存失败:', errorMsg)
        ElMessage.error(`注册配置保存失败: ${errorMsg}`)
        return false
      }
    } catch (error) {
      console.error('❌ 保存注册配置失败:', error)
      ElMessage.error('保存注册配置时发生错误')
      return false
    }
  }

  /**
   * 获取默认注册配置
   */
  private static getDefaultRegisterConfig(): RegisterConfig {
    return {
      email: '',
      tempmailDomain: '',
      tempmailEpin: '',
      firstName: '',
      lastName: '',
      password: '',
      region: 'US',
      registerMode: 'auto',
      showBrowser: false,
      timeout: 300,
      retryCount: 3
    }
  }
}

/**
 * 备份数据服务
 */
export class BackupDataService {
  private static readonly STORAGE_KEY = 'backups'
  private static readonly LOCATION = DataLocation.FILE_SYSTEM

  /**
   * 获取所有备份
   */
  static async getAllBackups(): Promise<BackupInfo[]> {
    try {
      const backups = await dataManager.getData<BackupInfo[]>(
        this.STORAGE_KEY,
        this.LOCATION,
        []
      )
      return backups || []
    } catch (error) {
      console.error('❌ 获取备份列表失败:', error)
      return []
    }
  }

  /**
   * 创建备份
   */
  static async createBackup(
    type: BackupInfo['type'],
    name: string,
    description?: string
  ): Promise<string> {
    try {
      const backupId = `backup_${type}_${Date.now()}`
      
      // 根据类型获取要备份的数据
      let dataToBackup: any
      switch (type) {
        case 'accounts':
          dataToBackup = await AccountDataService.getAllAccounts()
          break
        case 'settings':
          dataToBackup = await ConfigDataService.getConfig()
          break
        case 'session':
          // 获取会话数据
          dataToBackup = await this.getSessionData()
          break
        case 'full':
          // 获取所有数据
          dataToBackup = {
            accounts: await AccountDataService.getAllAccounts(),
            config: await ConfigDataService.getConfig(),
            registerConfig: await RegisterConfigService.getRegisterConfig(),
            session: await this.getSessionData()
          }
          break
      }

      const backupInfo: BackupInfo = {
        id: backupId,
        type,
        name,
        createdAt: new Date().toISOString(),
        size: JSON.stringify(dataToBackup).length,
        checksum: this.calculateChecksum(dataToBackup),
        description
      }

      // 保存备份数据
      const success = await dataManager.setData(
        `backup/${backupId}`,
        {
          info: backupInfo,
          data: dataToBackup
        },
        this.LOCATION
      )

      if (success) {
        // 更新备份列表
        const backups = await this.getAllBackups()
        backups.push(backupInfo)
        await dataManager.setData(this.STORAGE_KEY, backups, this.LOCATION)
        
        ElMessage.success('备份创建成功')
        return backupId
      } else {
        ElMessage.error('备份创建失败')
        return ''
      }
    } catch (error) {
      console.error('❌ 创建备份失败:', error)
      ElMessage.error('创建备份时发生错误')
      return ''
    }
  }

  /**
   * 恢复备份
   */
  static async restoreBackup(backupId: string): Promise<boolean> {
    try {
      const backupData = await dataManager.getData<{
        info: BackupInfo
        data: any
      }>(`backup/${backupId}`, this.LOCATION)

      if (!backupData) {
        ElMessage.error('备份文件不存在')
        return false
      }

      const { info, data } = backupData

      // 根据备份类型恢复数据
      switch (info.type) {
        case 'accounts':
          await dataManager.setData('cursor-accounts', data, DataLocation.API_SERVER)
          break
        case 'settings':
          await dataManager.setData('app-config', data, DataLocation.API_SERVER)
          break
        case 'full':
          await dataManager.setData('cursor-accounts', data.accounts, DataLocation.API_SERVER)
          await dataManager.setData('app-config', data.config, DataLocation.API_SERVER)
          await dataManager.setData('register-config', data.registerConfig, DataLocation.API_SERVER)
          break
      }

      ElMessage.success('备份恢复成功')
      return true
    } catch (error) {
      console.error('❌ 恢复备份失败:', error)
      ElMessage.error('恢复备份时发生错误')
      return false
    }
  }

  /**
   * 删除备份
   */
  static async deleteBackup(backupId: string): Promise<boolean> {
    try {
      // 删除备份文件
      const success = await dataManager.setData(`backup/${backupId}`, null, this.LOCATION)
      
      if (success) {
        // 更新备份列表
        const backups = await this.getAllBackups()
        const filteredBackups = backups.filter(backup => backup.id !== backupId)
        await dataManager.setData(this.STORAGE_KEY, filteredBackups, this.LOCATION)
        
        ElMessage.success('备份删除成功')
      } else {
        ElMessage.error('备份删除失败')
      }

      return success
    } catch (error) {
      console.error('❌ 删除备份失败:', error)
      ElMessage.error('删除备份时发生错误')
      return false
    }
  }

  /**
   * 获取会话数据
   */
  private static async getSessionData(): Promise<any> {
    // 获取localStorage中的会话相关数据
    const sessionData: any = {}
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith('cursor-')) {
        sessionData[key] = localStorage.getItem(key)
      }
    }
    
    return sessionData
  }

  /**
   * 计算校验和
   */
  private static calculateChecksum(data: any): string {
    const str = JSON.stringify(data)
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash
    }
    return hash.toString(16)
  }
}

// 导出所有服务
export {
  AccountDataService as accountData,
  ConfigDataService as configData,
  RegisterConfigService as registerConfig,
  BackupDataService as backupData
}
