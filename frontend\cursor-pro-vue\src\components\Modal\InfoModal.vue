<template>
  <BaseModal
    :visible="visible"
    :title="title"
    width="600px"
    max-width="90vw"
    @close="handleClose"
  >
    <!-- 信息内容显示区域 -->
    <div class="info-modal-content">
      <div class="info-groups">
        <div class="info-group">
          <div class="info-title">📧 邮箱信息</div>
          <div class="info-content">{{ data.email || '未知' }}</div>
          <div class="info-time">保存时间: {{ formatTime() }}</div>
          <button
            class="copy-btn"
            @click="copyText(data.email || '', $event)"
            title="复制邮箱"
          >
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
            </svg>
            复制
          </button>
        </div>

        <div class="info-group">
          <div class="info-title">👤 姓名信息</div>
          <div class="info-content">{{ data.firstName || '未知' }}</div>
          <button
            class="copy-btn"
            @click="copyText(data.firstName || '', $event)"
            title="复制姓名"
          >
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
            </svg>
            复制
          </button>
        </div>

        <div class="info-group">
          <div class="info-title">👤 姓氏信息</div>
          <div class="info-content">{{ data.lastName || '未知' }}</div>
          <button
            class="copy-btn"
            @click="copyText(data.lastName || '', $event)"
            title="复制姓氏"
          >
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
            </svg>
            复制
          </button>
        </div>

        <div class="info-group">
          <div class="info-title">🔐 登录密码</div>
          <div class="info-content">{{ data.password || '未知' }}</div>
          <button
            class="copy-btn"
            @click="copyText(data.password || '', $event)"
            title="复制密码"
          >
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
            </svg>
            复制
          </button>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <button
        class="card__button card__button--confirm"
        @click="handleCopyAll"
      >
        复制全部
      </button>
      <button
        class="card__button card__button--cancel"
        @click="handleClose"
      >
        关闭
      </button>
    </template>
  </BaseModal>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import BaseModal from './BaseModal.vue'

interface InfoData {
  email?: string
  firstName?: string
  lastName?: string
  password?: string
  timestamp?: number
}

interface InfoModalProps {
  visible: boolean
  title: string
  data: InfoData
}

interface InfoModalEvents {
  'close': []
  'update:visible': [visible: boolean]
}

const props = defineProps<InfoModalProps>()
const emit = defineEmits<InfoModalEvents>()

// 格式化信息内容
const formattedInfo = computed(() => {
  if (!props.data) return ''

  let formatted = ''
  formatted += '【随机配置信息】\n'
  formatted += '-'.repeat(40) + '\n'

  if (props.data.email) {
    formatted += `[邮箱信息]\n`
    formatted += `  email = ${props.data.email}\n`
  }

  if (props.data.firstName) {
    formatted += `[姓名信息]\n`
    formatted += `  firstName = ${props.data.firstName}\n`
  }

  if (props.data.lastName) {
    formatted += `[姓氏信息]\n`
    formatted += `  lastName = ${props.data.lastName}\n`
  }

  if (props.data.password) {
    formatted += `[登录密码]\n`
    formatted += `  password = ${props.data.password}\n`
  }

  formatted += '-'.repeat(40) + '\n'
  formatted += `保存时间: ${formatTime()}\n`

  // 配置内容高亮美化（与ConfigModal一致）
  formatted = formatted.replace(/^【随机配置信息】/m, '<span style="color:#10b981;font-weight:bold;">【随机配置信息】</span>')
  formatted = formatted.replace(/^[-]{10,}/gm, '<span style="color:#60a5fa;">$&</span>')
  formatted = formatted.replace(/^\[.*?\]/gm, match => `<span style='color:#60a5fa;'>${match}</span>`)
  formatted = formatted.replace(/^\s*([\w_]+)\s*=\s*([^\n]+)$/gm, (m, k, v) => {
    return `<span style='color:#b5f4a5;'>${k}</span> = <span style='color:#fff;'>${v}</span>`
  })

  return formatted.replace(/\n/g, '<br>')
})

// 格式化时间
const formatTime = () => {
  if (!props.data.timestamp) {
    return new Date().toLocaleString('zh-CN')
  }
  return new Date(props.data.timestamp).toLocaleString('zh-CN')
}

// 复制单个文本
const copyText = async (text: string, event: Event) => {
  if (!text) {
    console.warn('没有可复制的内容')
    return
  }

  try {
    await navigator.clipboard.writeText(text)

    // 更新按钮状态
    const button = event.target as HTMLButtonElement
    const originalText = button.textContent
    button.textContent = '✅ 已复制'
    button.classList.add('copied')

    setTimeout(() => {
      button.textContent = originalText
      button.classList.remove('copied')
    }, 2000)

    console.log('复制成功:', text)
  } catch (error) {
    console.error('复制失败:', error)
  }
}

// 复制全部内容
const handleCopyAll = async () => {
  if (!props.data) return

  let allText = '【随机配置信息】\n'
  allText += '-'.repeat(40) + '\n'

  if (props.data.email) {
    allText += `[邮箱信息]\n`
    allText += `  email = ${props.data.email}\n`
  }

  if (props.data.firstName) {
    allText += `[姓名信息]\n`
    allText += `  firstName = ${props.data.firstName}\n`
  }

  if (props.data.lastName) {
    allText += `[姓氏信息]\n`
    allText += `  lastName = ${props.data.lastName}\n`
  }

  if (props.data.password) {
    allText += `[登录密码]\n`
    allText += `  password = ${props.data.password}\n`
  }

  allText += '-'.repeat(40) + '\n'
  allText += `保存时间: ${formatTime()}\n`

  try {
    await navigator.clipboard.writeText(allText)
    console.log('复制全部成功')
  } catch (error) {
    console.error('复制失败:', error)
  }
}

const handleClose = () => {
  emit('close')
  emit('update:visible', false)
}
</script>

<style scoped>
.info-modal-content {
  padding: 0;
}

.info-groups {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.info-group {
  background: #181c20;
  border: none;
  border-bottom: 1px solid #2d323a;
  padding: 20px;
  position: relative;
  margin: 0;
  border-radius: 0;
}

.info-group:first-child {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.info-group:last-child {
  border-bottom: none;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.info-title {
  color: #60a5fa;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 8px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.info-content {
  color: #e5e5e5;
  font-size: 1rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  word-break: break-all;
  margin-bottom: 8px;
  line-height: 1.4;
  background: transparent;
  border: none;
  outline: none;
  width: calc(100% - 80px);
}

.info-time {
  color: #9ca3af;
  font-size: 0.8rem;
  margin-bottom: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.copy-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: #374151;
  border: 1px solid #4b5563;
  border-radius: 6px;
  color: #d1d5db;
  padding: 6px 10px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.copy-btn:hover {
  background: #4b5563;
  border-color: #6b7280;
  color: #f3f4f6;
}

.copy-btn.copied {
  background: #10b981;
  border-color: #10b981;
  color: white;
}

.copy-btn svg {
  width: 12px;
  height: 12px;
}

/* 按钮样式 */
.card__button {
  border: 3px solid #000;
  background: #000;
  color: #fff;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: bold;
  text-transform: uppercase;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s;
  min-width: 100px;
}

.card__button--cancel {
  background: #dc2626;
  border-color: #dc2626;
}

.card__button--cancel::before {
  content: "Sure?";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 105%;
  background-color: #f87171;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.card__button--cancel:hover::before {
  transform: translateY(0);
}

.card__button--confirm {
  background: #059669;
  border-color: #059669;
}

.card__button--confirm::before {
  content: "Copy!";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 105%;
  background-color: #34d399;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.card__button--confirm:hover::before {
  transform: translateY(0);
}

.card__button:active {
  transform: scale(0.95);
}
</style>
