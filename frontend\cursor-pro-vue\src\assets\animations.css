/* 全局动画效果 - 与原版完全一致 */

/* 基础动画关键帧 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { 
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to { 
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes modalSlideIn {
  from {
    transform: scale(0.9) translateY(-20px);
    opacity: 0;
  }
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes popIn {
  0% { 
    transform: scale(0.85); 
    opacity: 0.2;
  }
  100% { 
    transform: scale(1); 
    opacity: 1;
  }
}

/* 旋转动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 脉动动画 */
@keyframes dotPulse {
  0%, 100% { 
    opacity: 0.3; 
    transform: scale(1); 
  }
  50% { 
    opacity: 0.8; 
    transform: scale(1.2); 
  }
}

@keyframes emailDot {
  0%, 100% { 
    opacity: 0.6; 
    transform: scale(1); 
  }
  50% { 
    opacity: 1; 
    transform: scale(1.3); 
  }
}

@keyframes loadingPulse {
  0%, 80%, 100% {
    transform: scale(0.5);
    opacity: 0.4;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 呼吸动画 */
@keyframes breathe {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

@keyframes breath {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

/* 弹跳动画 */
@keyframes bounce {
  0%, 80%, 100% { 
    transform: translateY(0); 
  }
  40% { 
    transform: translateY(-6px); 
  }
}

/* 通用过渡效果类 */
.transition-fast {
  transition: all 0.18s ease;
}

.transition-normal {
  transition: all 0.2s ease;
}

.transition-slow {
  transition: all 0.3s ease;
}

.transition-colors {
  transition: background 0.18s, color 0.18s, border 0.18s, box-shadow 0.18s;
}

.transition-transform {
  transition: transform 0.2s ease;
}

.transition-opacity {
  transition: opacity 0.2s ease;
}

/* 悬停效果 */
.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-lift-more:hover {
  transform: translateY(-4px);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-scale-small:hover {
  transform: scale(1.02);
}

/* 加载动画 */
.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner-large {
  width: 24px;
  height: 24px;
  border: 3px solid transparent;
  border-top: 3px solid currentColor;
  border-radius: 50%;
  animation: spin 1.2s linear infinite;
}

.loading-dots {
  display: inline-flex;
  gap: 4px;
}

.loading-dot {
  width: 6px;
  height: 6px;
  background: currentColor;
  border-radius: 50%;
  animation: bounce 1.4s infinite ease-in-out;
}

.loading-dot:nth-child(1) { animation-delay: 0s; }
.loading-dot:nth-child(2) { animation-delay: 0.2s; }
.loading-dot:nth-child(3) { animation-delay: 0.4s; }

.loading-pulse {
  animation: loadingPulse 1.8s infinite ease-in-out;
}

.loading-pulse:nth-child(1) { animation-delay: -0.6s; }
.loading-pulse:nth-child(2) { animation-delay: -0.4s; }
.loading-pulse:nth-child(3) { animation-delay: -0.2s; }

.loading-breathe {
  animation: breathe 2.5s ease-in-out infinite;
}

/* 装饰动画 */
.dot-pulse {
  animation: dotPulse 2s ease-in-out infinite;
}

.dot-pulse:nth-child(2) {
  animation-delay: 0.5s;
}

.email-dot {
  animation: emailDot 3s ease-in-out infinite;
}

/* 进度条动画 */
.progress-bar-animated {
  transition: width 0.4s ease;
}

.progress-bar-smooth {
  transition: width 0.3s ease;
}

/* 模态框动画 */
.modal-fade-enter {
  animation: fadeIn 0.3s ease;
}

.modal-slide-enter {
  animation: modalSlideIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.modal-bounce-enter {
  animation: bounceIn 0.4s ease;
}

/* 卡片动画 */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* 按钮动画 */
.btn-animated {
  transition: all 0.2s ease;
}

.btn-animated:hover {
  transform: translateY(-1px);
}

.btn-animated:active {
  transform: translateY(0);
}

/* 输入框动画 */
.input-animated {
  transition: border-color 0.18s, background 0.18s, box-shadow 0.18s;
}

.input-animated:focus {
  box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.2);
}

/* 图标动画 */
.icon-spin {
  animation: spin 1s linear infinite;
}

.icon-pulse {
  animation: loadingPulse 1.8s infinite ease-in-out;
}

.icon-breathe {
  animation: breathe 2.5s ease-in-out infinite;
}

/* 文本动画 */
.text-breathe {
  animation: breathe 2.5s ease-in-out infinite;
  text-shadow: 0 0 6px rgba(96, 165, 250, 0.2);
}

/* 响应式动画控制 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 性能优化 */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-auto {
  will-change: auto;
}

/* GPU加速 */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}
