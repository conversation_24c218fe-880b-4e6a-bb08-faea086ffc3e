#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版本控制和强制更新系统
确保用户使用最新版本
"""

import os
import sys
import json
import time
import hashlib
import requests
from datetime import datetime, timedelta
from pathlib import Path
import tkinter as tk
from tkinter import messagebox
import webbrowser

class VersionController:
    """版本控制器"""
    
    def __init__(self):
        # 版本验证API地址（使用您实际部署的API）
        self.api_base = "https://cursorpro-api.vercel.app"  # 您的新API域名
        
        # 备用验证地址（暂时禁用，因为没有真实的备用服务器）
        self.backup_apis = [
            # "https://your-backup1.com/api/v1",  # 示例地址，已禁用
            # "https://your-backup2.com/api/v1"   # 示例地址，已禁用
        ]
        
        # 当前版本信息
        self.current_version = self.get_current_version()
        self.app_name = "CursorPro"
        
        # 本地缓存文件
        self.cache_dir = Path.home() / ".cursor-pro-cache"
        self.cache_dir.mkdir(exist_ok=True)
        self.cache_file = self.cache_dir / "version_cache.json"
        
        # 验证配置
        self.check_interval = 30  # 30秒检查一次（几乎实时响应版本更新）
        self.timeout = 10  # 网络请求超时
        # 注意：已移除宽限期机制，版本验证更加严格
        
    def get_current_version(self):
        """获取当前版本号"""
        try:
            # 优先使用内置版本配置（打包后的固定版本）
            from cursor_pro.utils.app_version import get_app_version
            version = get_app_version()
            print(f"✅ 使用内置版本号: {version}")
            return version
        except ImportError:
            # 如果没有版本配置文件，尝试从.env读取
            try:
                possible_paths = ['.env', './.env', os.path.join(os.getcwd(), '.env')]

                for env_path in possible_paths:
                    try:
                        with open(env_path, 'r', encoding='utf-8') as f:
                            for line in f:
                                line = line.strip()
                                if line.startswith('version=') or line.startswith('VERSION='):
                                    version = line.split('=')[1].strip()
                                    if version:
                                        print(f"✅ 从 {env_path} 读取到版本号: {version}")
                                        return version
                    except FileNotFoundError:
                        continue
                    except Exception as e:
                        print(f"⚠️ 读取 {env_path} 失败: {e}")
                        continue

                # 最后的备用版本号
                fallback_version = "1.1.0"
                print(f"⚠️ 无法读取版本号，使用备用版本: {fallback_version}")
                return fallback_version

            except Exception as e:
                print(f"⚠️ 获取版本号异常: {e}")
                return "1.1.0"
    
    def get_machine_fingerprint(self):
        """生成机器指纹（用于统计，不用于限制）"""
        try:
            import platform
            import uuid
            
            # 收集机器信息
            info = {
                'platform': platform.platform(),
                'processor': platform.processor(),
                'mac': str(uuid.getnode()),
                'python': platform.python_version()
            }
            
            # 生成指纹
            fingerprint = hashlib.sha256(
                json.dumps(info, sort_keys=True).encode()
            ).hexdigest()[:16]
            
            return fingerprint
        except:
            return "unknown"
    
    def check_version_online(self):
        """在线检查版本"""
        apis_to_try = [self.api_base] + self.backup_apis
        
        for api_url in apis_to_try:
            try:
                # 构建请求参数
                params = {
                    'current_version': self.current_version,
                    'app_name': self.app_name,
                    'fingerprint': self.get_machine_fingerprint(),
                    'timestamp': int(time.time())
                }

                # 发送请求到版本检查端点
                response = requests.get(
                    f"{api_url}/api/check-version",
                    params=params,
                    timeout=self.timeout,
                    headers={'User-Agent': f'{self.app_name}/{self.current_version}'},
                    verify=False,  # 临时禁用SSL验证以解决连接问题
                    allow_redirects=True
                )
                
                if response.status_code == 200:
                    data = response.json()

                    # 检查API响应格式
                    if data.get('status') == 'success':
                        # 标准的版本检查响应
                        self.save_cache(data)
                        return data
                    else:
                        # 兼容旧格式或错误响应
                        print(f"API响应格式异常: {data}")
                        continue
                    
            except requests.exceptions.SSLError as e:
                print(f"SSL连接错误 - {api_url}: {e}")
                print("💡 提示: 这通常是网络环境或SSL证书问题")
                continue
            except requests.exceptions.ConnectionError as e:
                print(f"网络连接错误 - {api_url}: {e}")
                continue
            except requests.exceptions.Timeout as e:
                print(f"请求超时 - {api_url}: {e}")
                continue
            except Exception as e:
                print(f"API {api_url} 检查失败: {e}")
                print(f"详细错误类型: {type(e).__name__}")
                continue
        
        return None
    
    def save_cache(self, data):
        """保存验证结果到缓存"""
        try:
            cache_data = {
                'data': data,
                'timestamp': time.time(),
                'version': self.current_version
            }
            
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存缓存失败: {e}")
    
    def load_cache(self):
        """从缓存加载验证结果 - 严格模式"""
        try:
            if not self.cache_file.exists():
                print("📋 无缓存文件，需要在线验证")
                return None

            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)

            # 检查缓存是否过期（更严格的时间检查）
            cache_time = cache_data.get('timestamp', 0)
            time_diff = time.time() - cache_time
            if time_diff > self.check_interval:
                print(f"📋 缓存已过期 ({time_diff/60:.1f}分钟前)，需要重新验证")
                return None

            # 检查版本是否匹配
            if cache_data.get('version') != self.current_version:
                print(f"📋 版本不匹配，需要重新验证")
                return None

            print(f"📋 使用有效缓存 (剩余{(self.check_interval - time_diff)/60:.1f}分钟)")
            return cache_data.get('data')

        except Exception as e:
            print(f"❌ 加载缓存失败: {e}")
            return None
    
    def show_update_dialog(self, version_info):
        """显示更新对话框"""
        try:
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            root.title("版本更新")
            
            # 设置窗口属性
            root.attributes('-topmost', True)
            root.geometry("500x300")
            root.resizable(False, False)
            
            # 居中显示
            root.eval('tk::PlaceWindow . center')
            
            # 创建主框架
            main_frame = tk.Frame(root, padx=20, pady=20)
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            # 标题
            title_label = tk.Label(
                main_frame,
                text="🚀 发现新版本！",
                font=("Arial", 16, "bold"),
                fg="red"
            )
            title_label.pack(pady=(0, 10))
            
            # 版本信息
            current_label = tk.Label(
                main_frame,
                text=f"当前版本: {self.current_version}",
                font=("Arial", 10)
            )
            current_label.pack()
            
            latest_label = tk.Label(
                main_frame,
                text=f"最新版本: {version_info.get('latest_version', 'Unknown')}",
                font=("Arial", 10, "bold"),
                fg="green"
            )
            latest_label.pack(pady=(0, 10))
            
            # 更新说明
            if version_info.get('update_message'):
                message_label = tk.Label(
                    main_frame,
                    text=version_info['update_message'],
                    font=("Arial", 9),
                    wraplength=450,
                    justify=tk.LEFT
                )
                message_label.pack(pady=(0, 10))

            # 强制更新提示
            if version_info.get('force_update'):
                force_label = tk.Label(
                    main_frame,
                    text="⚠️ 检测到新版本，请到群里下载最新版本才能继续使用",
                    font=("Arial", 10, "bold"),
                    fg="red",
                    wraplength=450
                )
                force_label.pack(pady=(0, 10))

                # 群下载提示
                group_label = tk.Label(
                    main_frame,
                    text=version_info.get('download_message', '请到QQ群/微信群下载最新版本'),
                    font=("Arial", 9),
                    fg="blue",
                    wraplength=450
                )
                group_label.pack(pady=(0, 15))
            
            # 按钮框架
            button_frame = tk.Frame(main_frame)
            button_frame.pack(pady=(10, 0))
            
            # 下载按钮
            download_btn = tk.Button(
                button_frame,
                text="前往下载",
                command=lambda: self.open_download_page(version_info.get('download_url')),
                bg="green",
                fg="white",
                font=("Arial", 10, "bold"),
                width=12
            )
            download_btn.pack(side=tk.LEFT, padx=(0, 10))
            
            # 如果不是强制更新，显示稍后按钮
            if not version_info.get('force_update'):
                later_btn = tk.Button(
                    button_frame,
                    text="稍后更新",
                    command=root.destroy,
                    width=12
                )
                later_btn.pack(side=tk.LEFT)
            
            # 如果是强制更新，禁用关闭按钮
            if version_info.get('force_update'):
                root.protocol("WM_DELETE_WINDOW", lambda: None)
            
            root.deiconify()  # 显示窗口
            root.mainloop()
            
        except Exception as e:
            print(f"显示更新对话框失败: {e}")
            # 降级到简单对话框
            if version_info.get('force_update'):
                messagebox.showerror(
                    "强制更新",
                    f"检测到新版本 {version_info.get('latest_version')}，必须更新后才能使用！"
                )
            else:
                messagebox.showinfo(
                    "版本更新",
                    f"发现新版本 {version_info.get('latest_version')}，建议及时更新。"
                )
    
    def open_download_page(self, download_url):
        """打开下载页面"""
        try:
            if download_url:
                webbrowser.open(download_url)
            else:
                # 默认下载页面
                webbrowser.open("https://github.com/your-username/cursor-pro/releases")
        except Exception as e:
            print(f"打开下载页面失败: {e}")
    
    def verify_version(self):
        """验证版本，返回是否允许继续运行"""
        print(f"🔍 检查版本更新... 当前版本: {self.current_version}")
        
        # 首先尝试从缓存加载
        version_info = self.load_cache()
        
        # 如果缓存无效，在线检查
        if version_info is None:
            print("📡 在线检查版本...")
            version_info = self.check_version_online()
        
        # 如果在线检查也失败，直接拒绝运行
        if version_info is None:
            print("❌ 无法连接到更新服务器，请检查网络连接")
            messagebox.showerror(
                "版本验证失败",
                "无法连接到版本验证服务器！\n\n"
                "可能原因：\n"
                "1. 网络连接问题\n"
                "2. 服务器维护中\n"
                "3. 防火墙阻止连接\n\n"
                "请检查网络连接后重试，或联系技术支持。"
            )
            return False
        
        # 解析版本信息
        latest_version = version_info.get('latest_version')
        min_version = version_info.get('min_supported_version')
        force_update = version_info.get('force_update', False)
        
        print(f"📋 最新版本: {latest_version}")
        print(f"📋 最低支持版本: {min_version}")
        print(f"📋 强制更新: {force_update}")
        
        # 检查是否需要更新
        if self.version_compare(self.current_version, latest_version) < 0:
            print("🆕 发现新版本")
            
            # 检查是否低于最低支持版本
            if min_version and self.version_compare(self.current_version, min_version) < 0:
                print("❌ 当前版本过低，必须更新")
                self.show_update_dialog(version_info)
                return False
            
            # 检查是否强制更新
            if force_update:
                print("⚠️ 强制更新")
                self.show_update_dialog(version_info)
                return False
            
            # 可选更新
            print("💡 建议更新")
            self.show_update_dialog(version_info)
        
        print("✅ 版本检查通过")
        return True
    
    def handle_offline_mode(self):
        """处理离线模式 - 已禁用宽限期，直接拒绝运行"""
        print("❌ 离线模式已禁用，必须连接服务器验证版本")
        messagebox.showerror(
            "版本验证必需",
            "版本验证失败！\n\n"
            "为了确保您使用的是最新版本，\n"
            "必须连接到服务器进行版本验证。\n\n"
            "请检查网络连接后重试。"
        )
        return False
    
    def version_compare(self, v1, v2):
        """比较版本号 返回: -1(v1<v2), 0(v1==v2), 1(v1>v2)"""
        try:
            def normalize(v):
                return [int(x) for x in v.split('.')]
            
            v1_parts = normalize(v1)
            v2_parts = normalize(v2)
            
            # 补齐长度
            max_len = max(len(v1_parts), len(v2_parts))
            v1_parts.extend([0] * (max_len - len(v1_parts)))
            v2_parts.extend([0] * (max_len - len(v2_parts)))
            
            for i in range(max_len):
                if v1_parts[i] < v2_parts[i]:
                    return -1
                elif v1_parts[i] > v2_parts[i]:
                    return 1
            
            return 0
            
        except Exception as e:
            print(f"版本比较失败: {e}")
            return 0

# 全局版本控制器实例
version_controller = VersionController()

def check_version_before_start():
    """启动前检查版本 - 严格模式"""
    try:
        if not version_controller.verify_version():
            print("❌ 版本验证失败，程序退出")
            sys.exit(1)
    except Exception as e:
        print(f"❌ 版本检查异常: {e}")
        print("❌ 由于版本检查异常，程序无法继续运行")
        # 严格模式：异常时也拒绝运行
        sys.exit(1)

if __name__ == '__main__':
    # 测试版本控制
    check_version_before_start()
    print("✅ 版本验证通过，程序可以继续运行")
