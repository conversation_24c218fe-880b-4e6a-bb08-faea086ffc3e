<template>
  <div class="sidebar">
    <!-- <PERSON>go (可选) -->
    <!-- <img class="logo" src="/images/logo.png" alt="logo" /> -->

    <!-- 标题 -->
    <button class="sidebar-title button" data-text="CURSOR PRO">
      <span class="actual-text">&nbsp;CURSOR PRO&nbsp;</span>
      <span aria-hidden="true" class="hover-text">&nbsp;CURSOR PRO&nbsp;</span>
    </button>
    
    <!-- 菜单 -->
    <div class="menu">
      <div
        v-for="item in menuItems"
        :key="item.id"
        class="comic-brutal-button-container"
      >
        <button
          class="comic-brutal-button"
          :class="{ active: currentView === item.target }"
          @click="handleMenuClick(item.target)"
        >
          <div class="button-inner">
            <span class="button-text">{{ item.label }}</span>
            <div class="halftone-overlay"></div>
            <div class="ink-splatter"></div>
          </div>
          <div class="button-shadow"></div>
          <div class="button-frame"></div>
        </button>
      </div>
    </div>
    
    <!-- 底部区域 -->
    <div class="sidebar-bottom">
      <button class="Btn" @click="handleExit">
        <div class="sign">
          <svg viewBox="0 0 512 512">
            <path d="M377.9 105.9L500.7 228.7c7.2 7.2 11.3 17.1 11.3 27.3s-4.1 20.1-11.3 27.3L377.9 406.1c-6.4 6.4-15 9.9-24 9.9c-18.7 0-33.9-15.2-33.9-33.9l0-62.1-128 0c-17.7 0-32-14.3-32-32l0-64c0-17.7 14.3-32 32-32l128 0 0-62.1c0-18.7 15.2-33.9 33.9-33.9c9 0 17.6 3.6 24 9.9zM160 96L96 96c-17.7 0-32 14.3-32 32l0 256c0 17.7 14.3 32 32 32l64 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-64 0c-53 0-96-43-96-96L0 128C0 75 43 32 96 32l64 0c17.7 0 32 14.3 32 32s-14.3 32-32 32z"/>
          </svg>
        </div>
        <div class="text">退出程序</div>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface SidebarProps {
  collapsed?: boolean
  currentView: string
}

interface SidebarEvents {
  'view-change': [view: string]
  'toggle-collapse': []
}

interface MenuItem {
  id: string
  label: string
  target: string
}

const props = withDefaults(defineProps<SidebarProps>(), {
  collapsed: false
})

const emit = defineEmits<SidebarEvents>()

// 菜单项配置
const menuItems: MenuItem[] = [
  { id: 'dashboard', label: '账号信息', target: 'dashboard' },
  { id: 'features', label: '功能区域', target: 'features' },
  { id: 'settings', label: '配置设置', target: 'settings' },
  { id: 'about', label: '数据管理', target: 'about' },
  { id: 'logs', label: '终端日志', target: 'logs' }
]

// 方法
const handleMenuClick = (target: string) => {
  emit('view-change', target)
}

const handleExit = () => {
  // TODO: 实现退出逻辑
  console.log('退出应用')
}
</script>

<style scoped>
.sidebar {
  width: 300px;
  background: #23272e;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 0 10px 0;
  box-shadow: none;
  min-width: 280px;
  border-right: 1.5px solid #22262c;

}



/* 标题动画按钮样式 */
.sidebar-title.button {
  margin: 24px 0 36px 0;
  height: auto;
  background: transparent;
  padding: 0;
  border: none;
  cursor: pointer;
  --border-right: 4px;
  --text-stroke-color: rgba(96, 165, 250, 1);
  --animation-color: #ec4899;
  --fs-size: 1.8rem;
  letter-spacing: 2px;
  text-decoration: none;
  font-size: var(--fs-size);
  font-family: "Arial", sans-serif;
  position: relative;
  text-transform: uppercase;
  color: transparent;
  -webkit-text-stroke: 2px var(--text-stroke-color);
  font-weight: 800;
  white-space: nowrap;
  display: inline-block;
}

.actual-text {
  display: inline-block;
}

/* 悬停时的文字填充效果 */
.hover-text {
  position: absolute;
  box-sizing: border-box;
  content: attr(data-text);
  color: var(--animation-color);
  width: 0%;
  height: 100%;
  top: 0;
  left: 0;
  border-right: var(--border-right) solid var(--animation-color);
  overflow: hidden;
  transition: 0.5s;
  -webkit-text-stroke: 1px var(--animation-color);
  white-space: nowrap;
  display: flex;
  align-items: center;
}

/* 悬停效果 */
.sidebar-title.button:hover .hover-text {
  width: 100%;
  filter: drop-shadow(0 0 23px var(--animation-color));
}

.menu {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
  padding: 0 15px;
}

/* 漫画风格按钮样式 */
.comic-brutal-button-container {
  --primary-color: #34bfa3;
  --secondary-color: #60a5fa;
  --text-color: #000000;
  --accent-color: #ffef00;
  --panel-color: #ffffff;
  --shadow-color: rgba(0, 0, 0, 0.75);
  --exit-color: #f87171;

  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin: 4px 0;
}

.comic-brutal-button {
  position: relative;
  font-size: 1.2em;
  border: 6px dashed #ffffff;
  border-radius: 12px;
  background: none;
  cursor: pointer;
  padding: 8px;
  transform: rotate(-1deg);
  transform-origin: center;
  transition: transform 0.15s cubic-bezier(0.22, 0.61, 0.36, 1);
  width: 100%;
  max-width: 260px;
  box-shadow: 0 0 0 2px #23272e, 0 0 0 4px #ffffff;
}

.button-inner {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--primary-color);
  color: var(--text-color);
  padding: 0.8em 1.5em;
  z-index: 3;
  overflow: hidden;
  transform: skew(-2deg, 0.5deg);
  transition: transform 0.15s cubic-bezier(0.22, 0.61, 0.36, 1);
  clip-path: polygon(
    0% 10%,
    3% 0%,
    97% 0%,
    100% 10%,
    100% 90%,
    97% 100%,
    3% 100%,
    0% 90%
  );
}

.button-text {
  position: relative;
  font-weight: 800;
  font-size: 1.1em;
  letter-spacing: 0.05em;
  text-transform: none;
  z-index: 5;
  color: #ffffff;
  text-shadow:
    0.05em 0.05em 0 #000000,
    -0.05em -0.05em 0 #000000,
    0.05em -0.05em 0 #000000,
    -0.05em 0.05em 0 #000000,
    0 0 8px rgba(0,0,0,0.8);
  transform: rotate(0deg);
}

.button-frame {
  position: absolute;
  top: -0.2em;
  left: -0.2em;
  right: -0.2em;
  bottom: -0.2em;
  background-color: var(--accent-color);
  border: 0.1em solid var(--text-color);
  z-index: 1;
  transition: transform 0.15s cubic-bezier(0.22, 0.61, 0.36, 1);
}

.button-shadow {
  position: absolute;
  top: 0.3em;
  left: 0.3em;
  right: -0.3em;
  bottom: -0.3em;
  background-color: var(--shadow-color);
  z-index: 0;
  transition: all 0.15s cubic-bezier(0.22, 0.61, 0.36, 1);
}

/* 激活状态样式 */
.comic-brutal-button.active {
  border-color: #60a5fa;
  border-width: 8px;
  border-style: dashed;
  animation: dash 1.5s linear infinite;
  box-shadow: 0 0 0 2px #23272e, 0 0 0 4px #60a5fa, 0 0 20px rgba(96, 165, 250, 0.5);
}

.comic-brutal-button.active .button-inner {
  background-color: var(--secondary-color);
}

.comic-brutal-button.active .button-text {
  color: #ffffff;
  text-shadow:
    0.05em 0.05em 0 #000000,
    -0.05em -0.05em 0 #000000,
    0.05em -0.05em 0 #000000,
    -0.05em 0.05em 0 #000000,
    0 0 12px rgba(0,0,0,1);
}

.comic-brutal-button.active .button-frame {
  border-color: var(--panel-color);
  border-width: 0.15em;
}

/* Halftone effect using CSS */
.halftone-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(
    circle at 30% 30%,
    rgba(0, 0, 0, 0.2) 0.08em,
    transparent 0.08em
  );
  background-size: 0.4em 0.4em;
  background-position: 0 0;
  opacity: 0.3;
  z-index: 2;
  mix-blend-mode: multiply;
}

/* Ink splatter effect using CSS gradients */
.ink-splatter {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.2s ease;
  background: radial-gradient(
      circle at 20% 30%,
      rgba(0, 0, 0, 0.15) 0%,
      rgba(0, 0, 0, 0.08) 20%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 65%,
      rgba(0, 0, 0, 0.15) 0%,
      rgba(0, 0, 0, 0.08) 25%,
      transparent 50%
    );
}

/* Button hover state */
.comic-brutal-button:hover {
  transform: rotate(0deg) scale(1.02);
  border-color: #ffd93d;
  border-width: 8px;
  box-shadow: 0 0 0 2px #23272e, 0 0 0 4px #ffd93d, 0 0 15px rgba(255, 217, 61, 0.4);
}

.comic-brutal-button:hover .button-inner {
  transform: skew(-3deg, 1deg);
  background-color: var(--secondary-color);
}

.comic-brutal-button:hover .button-text {
  color: #ffffff;
  text-shadow:
    0.05em 0.05em 0 #000000,
    -0.05em -0.05em 0 #000000,
    0.05em -0.05em 0 #000000,
    -0.05em 0.05em 0 #000000,
    0 0 12px rgba(0,0,0,1);
}

.comic-brutal-button:hover .button-shadow {
  transform: translate(0.15em, 0.15em);
}

.comic-brutal-button:hover .ink-splatter {
  opacity: 1;
}

/* Button active state */
.comic-brutal-button:active {
  transform: rotate(0) scale(0.98);
}

.comic-brutal-button:active .button-inner {
  transform: skew(0, 0);
}

.comic-brutal-button:active .button-shadow {
  transform: translate(0.08em, 0.08em);
}

.sidebar-bottom {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: auto;
  padding: 16px 10px;
}

/* 退出按钮特殊样式 */
.comic-brutal-button.exit-btn {
  border-color: #f87171;
  border-width: 6px;
  box-shadow: 0 0 0 2px #23272e, 0 0 0 4px #f87171;
}

.comic-brutal-button.exit-btn .button-inner {
  background-color: var(--exit-color);
}

.comic-brutal-button.exit-btn:hover {
  border-color: #dc2626;
  border-width: 8px;
  box-shadow: 0 0 0 2px #23272e, 0 0 0 4px #dc2626, 0 0 15px rgba(220, 38, 38, 0.4);
}

.comic-brutal-button.exit-btn:hover .button-inner {
  background-color: #dc2626;
}

/* 虚线动画 */
@keyframes dash {
  0% {
    border-dash-offset: 0;
  }
  100% {
    border-dash-offset: 20px;
  }
}

/* 展开式退出按钮样式 */
.Btn {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 60px;
  height: 60px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition-duration: .3s;
  box-shadow: 3px 3px 15px rgba(0, 0, 0, 0.3);
  background-color: rgb(255, 65, 65);
}

/* 图标 */
.sign {
  width: 100%;
  transition-duration: .3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sign svg {
  width: 24px;
}

.sign svg path {
  fill: white;
}

/* 文字 */
.text {
  position: absolute;
  right: 0%;
  width: 0%;
  opacity: 0;
  color: white;
  font-size: 1.4em;
  font-weight: 700;
  transition-duration: .3s;
  letter-spacing: 1px;
}

/* 悬停时按钮宽度变化 */
.Btn:hover {
  width: 180px;
  border-radius: 50px;
  transition-duration: .3s;
}

.Btn:hover .sign {
  width: 35%;
  transition-duration: .3s;
  padding-left: 25px;
}

/* 悬停时文字显示 */
.Btn:hover .text {
  opacity: 1;
  width: 70%;
  transition-duration: .3s;
  padding-right: 10px;
}

/* 点击效果 */
.Btn:active {
  transform: translate(2px, 2px);
}
</style>
