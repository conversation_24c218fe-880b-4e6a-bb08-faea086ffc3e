# 🚀 Cursor Pro 快速上手指南

## 📋 两种使用方式

### 🖥️ 桌面版（推荐使用）
```bash
双击运行: start-cursor-pro-desktop.bat
```
- ✅ 原生桌面应用体验
- ✅ 自动启动后端接口服务
- ✅ Electron界面，支持Windows 11圆角设计

### 🌐 网页版
```bash
双击运行: start-cursor-pro-web.bat
```
- ✅ 浏览器访问 http://localhost:8080
- ✅ 跨平台兼容性好
- ✅ 无需额外软件安装

## 🔧 环境要求

### 必需环境
- **Python 3.7+** - 后端接口服务器
- **Node.js 16+** - 前端构建工具（仅桌面版需要）

### 可选环境
- **现代浏览器** - Chrome/Edge/Firefox（网页版使用）

## 💡 使用技巧

### 首次使用步骤
1. 确保Python和Node.js已正确安装
2. 选择桌面版或网页版启动方式
3. 等待后端接口服务启动完成
4. 开始使用Cursor Pro各项功能

### 常见问题解决
- **启动失败**: 检查Python和Node.js是否正确安装
- **端口被占用**: 确保8080端口没有被其他程序占用
- **权限问题**: 尝试以管理员身份运行启动脚本

## 📚 更多帮助文档
- **[项目说明](项目说明.md)** - 详细的项目功能介绍
- **[文档目录](文档目录.md)** - 所有文档的完整索引
- **[配置指南](配置指南.md)** - 高级配置选项说明

---

*🎯 快速上手，立即体验Cursor Pro的强大功能！*
