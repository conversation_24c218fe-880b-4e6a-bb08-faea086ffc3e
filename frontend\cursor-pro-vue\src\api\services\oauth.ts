/**
 * OAuth认证相关API服务
 * 处理Google、GitHub等第三方登录认证
 */

import { BaseAPI } from '../base'
import type {
  OAuthConfig,
  OAuthRequest,
  OAuthResponse
} from '../types'

// OAuth状态管理
export interface OAuthState {
  provider: string
  state: string
  codeVerifier?: string
  redirectUri: string
  timestamp: number
}

// OAuth用户信息
export interface OAuthUserInfo {
  id: string
  email: string
  name: string
  avatar?: string
  provider: string
  providerUserId: string
  accessToken: string
  refreshToken?: string
  expiresAt: number
}

/**
 * OAuth服务类
 * 继承BaseAPI，提供OAuth认证相关的API调用
 */
export class OAuthService extends BaseAPI {
  private oauthStates: Map<string, OAuthState> = new Map()

  /**
   * 开始Google OAuth认证
   */
  async startGoogleOAuth(config?: Partial<OAuthConfig>): Promise<OAuthResponse> {
    console.log('🔄 [OAuthService] 开始Google OAuth认证:', config)

    const oauthConfig: OAuthConfig = {
      provider: 'google',
      clientId: config?.clientId || '',
      clientSecret: config?.clientSecret || '',
      redirectUri: config?.redirectUri || `${window.location.origin}/oauth/callback`,
      scope: config?.scope || ['openid', 'email', 'profile']
    }

    const request: OAuthRequest = {
      provider: 'google',
      config: oauthConfig,
      action: 'authorize'
    }

    const response = await this.post<OAuthResponse>('oauth/google/start', request)
    console.log('📡 [OAuthService] Google OAuth开始响应:', response)

    // 保存OAuth状态
    if (response.data.authUrl) {
      const state = this.generateState()
      this.oauthStates.set(state, {
        provider: 'google',
        state,
        redirectUri: oauthConfig.redirectUri,
        timestamp: Date.now()
      })
    }
    
    return response.data
  }



  /**
   * 处理OAuth回调
   */
  async handleOAuthCallback(
    provider: string,
    code: string,
    state: string
  ): Promise<OAuthResponse & { userInfo?: OAuthUserInfo }> {
    console.log('🔄 [OAuthService] 处理OAuth回调:', provider, state)

    // 验证状态
    const savedState = this.oauthStates.get(state)
    if (!savedState || savedState.provider !== provider) {
      throw new Error('Invalid OAuth state')
    }

    // 清理过期状态
    this.cleanupExpiredStates()

    const request = {
      provider,
      code,
      state,
      redirectUri: savedState.redirectUri
    }

    const response = await this.post<OAuthResponse & { userInfo?: OAuthUserInfo }>(
      `oauth/${provider}/callback`,
      request
    )
    
    console.log('📡 [OAuthService] OAuth回调响应:', response)

    // 清理使用过的状态
    this.oauthStates.delete(state)
    
    return response.data
  }

  /**
   * 刷新OAuth令牌
   */
  async refreshOAuthToken(
    provider: string,
    refreshToken: string
  ): Promise<OAuthResponse> {
    console.log('🔄 [OAuthService] 刷新OAuth令牌:', provider)

    const request: OAuthRequest = {
      provider,
      config: {} as OAuthConfig, // 服务端应该有保存的配置
      action: 'refresh'
    }

    const response = await this.post<OAuthResponse>(`oauth/${provider}/refresh`, {
      ...request,
      refreshToken
    })
    
    console.log('📡 [OAuthService] OAuth令牌刷新响应:', response)
    return response.data
  }

  /**
   * 撤销OAuth授权
   */
  async revokeOAuthToken(
    provider: string,
    token: string
  ): Promise<{ success: boolean; message: string }> {
    console.log('🔄 [OAuthService] 撤销OAuth授权:', provider)

    const response = await this.post<{ success: boolean; message: string }>(
      `oauth/${provider}/revoke`,
      { token }
    )
    
    console.log('📡 [OAuthService] OAuth撤销响应:', response)
    return response.data
  }

  /**
   * 获取OAuth用户信息
   */
  async getOAuthUserInfo(
    provider: string,
    accessToken: string
  ): Promise<OAuthUserInfo> {
    console.log('🔄 [OAuthService] 获取OAuth用户信息:', provider)

    const response = await this.get<OAuthUserInfo>(
      `oauth/${provider}/userinfo`,
      { access_token: accessToken }
    )
    
    console.log('📡 [OAuthService] OAuth用户信息响应:', response)
    return response.data
  }

  /**
   * 获取已连接的OAuth账户
   */
  async getConnectedAccounts(): Promise<{
    accounts: Array<{
      provider: string
      email: string
      name: string
      avatar?: string
      connectedAt: string
      lastUsed: string
      status: 'active' | 'expired' | 'revoked'
    }>
  }> {
    console.log('🔄 [OAuthService] 获取已连接的OAuth账户')

    const response = await this.get<{
      accounts: Array<{
        provider: string
        email: string
        name: string
        avatar?: string
        connectedAt: string
        lastUsed: string
        status: 'active' | 'expired' | 'revoked'
      }>
    }>('oauth/accounts')
    
    console.log('📡 [OAuthService] 已连接账户响应:', response)
    return response.data
  }

  /**
   * 断开OAuth账户连接
   */
  async disconnectAccount(provider: string): Promise<{
    success: boolean
    message: string
  }> {
    console.log('🔄 [OAuthService] 断开OAuth账户连接:', provider)

    const response = await this.delete<{
      success: boolean
      message: string
    }>(`oauth/accounts/${provider}`)
    
    console.log('📡 [OAuthService] 断开连接响应:', response)
    return response.data
  }

  /**
   * 获取OAuth配置
   */
  async getOAuthConfig(provider: string): Promise<{
    clientId: string
    redirectUri: string
    scope: string[]
    enabled: boolean
  }> {
    console.log('🔄 [OAuthService] 获取OAuth配置:', provider)

    const response = await this.get<{
      clientId: string
      redirectUri: string
      scope: string[]
      enabled: boolean
    }>(`oauth/${provider}/config`)
    
    console.log('📡 [OAuthService] OAuth配置响应:', response)
    return response.data
  }

  /**
   * 更新OAuth配置
   */
  async updateOAuthConfig(
    provider: string,
    config: Partial<OAuthConfig>
  ): Promise<{ success: boolean; message: string }> {
    console.log('🔄 [OAuthService] 更新OAuth配置:', provider, config)

    const response = await this.put<{ success: boolean; message: string }>(
      `oauth/${provider}/config`,
      config
    )
    
    console.log('📡 [OAuthService] OAuth配置更新响应:', response)
    return response.data
  }

  /**
   * 生成随机状态字符串
   */
  private generateState(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15)
  }

  /**
   * 清理过期的OAuth状态
   */
  private cleanupExpiredStates(): void {
    const now = Date.now()
    const expireTime = 10 * 60 * 1000 // 10分钟过期

    for (const [state, stateInfo] of this.oauthStates.entries()) {
      if (now - stateInfo.timestamp > expireTime) {
        this.oauthStates.delete(state)
      }
    }
  }

  /**
   * 验证OAuth状态
   */
  validateState(state: string): boolean {
    return this.oauthStates.has(state)
  }

  /**
   * 清理所有OAuth状态
   */
  clearAllStates(): void {
    this.oauthStates.clear()
  }
}

// 创建OAuth服务实例 - 根据环境自动配置
const getApiBaseUrl = () => {
  // 在Electron环境中，确保使用正确的API地址
  if (typeof window !== 'undefined' && window.electronAPI) {
    return 'http://localhost:8080'
  }
  return 'http://localhost:8080'
}

export const oauthService = new OAuthService(getApiBaseUrl())

// 添加默认拦截器
oauthService.addRequestInterceptor(async (config) => {
  // 为OAuth请求添加特殊标识
  config.headers = {
    ...config.headers,
    'X-Service': 'OAuthService',
    'X-Auth-Type': 'oauth'
  }
  return config
})

oauthService.addResponseInterceptor(async (response) => {
  // 处理OAuth响应中的令牌
  if (response.data && typeof response.data === 'object') {
    if ('accessToken' in response.data) {
      console.log('🔐 [OAuthService] OAuth令牌已更新')
    }
  }
  return response
})

oauthService.addErrorInterceptor(async (error) => {
  // OAuth错误的特殊处理
  console.error('❌ [OAuthService] OAuth操作错误:', {
    type: error.type,
    message: error.message,
    status: error.status
  })

  // 清理可能损坏的OAuth状态
  if (error.status === 401 || error.status === 403) {
    oauthService.clearAllStates()
  }

  return error
})
