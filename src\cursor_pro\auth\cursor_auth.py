#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor认证管理 - 纯SQLite版本
"""

import json
import logging
from colorama import Fore, Style, init
from cursor_pro.database.config import get_db_connection_context

# Initialize colorama
init()

# Define emoji and color constants
EMOJI = {
    'DB': '🗄️',
    'UPDATE': '🔄',
    'SUCCESS': '✅',
    'ERROR': '❌',
    'WARN': '⚠️',
    'INFO': 'ℹ️',
    'FILE': '📄',
    'KEY': '🔐'
}

logger = logging.getLogger(__name__)

class CursorAuth:
    """Cursor认证管理器 - 纯SQLite版本"""
    
    def __init__(self, user_id=1, translator=None):
        """
        初始化认证管理器
        
        Args:
            user_id: 用户ID，默认为1（本地用户）
            translator: 翻译器对象
        """
        self.user_id = user_id
        self.translator = translator
        
        # 确保用户存在
        self._ensure_user_exists()
    
    def _ensure_user_exists(self):
        """确保用户记录存在"""
        try:
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                # 检查用户是否存在
                cursor.execute("SELECT id FROM users WHERE id = ?", (self.user_id,))
                if not cursor.fetchone():
                    # 创建默认用户
                    cursor.execute("""
                        INSERT INTO users (id, email, device_id, status)
                        VALUES (?, ?, ?, ?)
                    """, (self.user_id, f'user_{self.user_id}@local', f'device_{self.user_id}', 'active'))
                    conn.commit()
                    logger.info(f"创建默认用户: user_id={self.user_id}")
                
                cursor.close()
                
        except Exception as e:
            logger.error(f"确保用户存在失败: {e}")
    
    def update_auth(self, email=None, access_token=None, refresh_token=None, auth_type="Auth_0"):
        """更新认证信息"""
        try:
            # 记录调试日志
            logger.info(f"[调试] cursor_auth.update_auth 开始执行")
            logger.info(f"[调试] 用户ID: {self.user_id}")
            logger.info(f"[调试] 参数 - email: {email}")
            logger.info(f"[调试] 参数 - access_token: {'存在' if access_token else '不存在'}")
            logger.info(f"[调试] 参数 - refresh_token: {'存在' if refresh_token else '不存在'}")
            logger.info(f"[调试] 参数 - auth_type: {auth_type}")
            
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                # 准备要更新的认证数据
                auth_updates = []
                
                # 添加认证类型
                auth_updates.append(("cursorAuth/cachedSignUpType", auth_type))
                
                if email:
                    auth_updates.append(("cursorAuth/email", email))
                
                if access_token:
                    auth_updates.append(("cursorAuth/accessToken", access_token))
                
                if refresh_token:
                    auth_updates.append(("cursorAuth/refreshToken", refresh_token))
                
                # 批量更新认证信息
                for auth_key, auth_value in auth_updates:
                    # 使用INSERT OR REPLACE语法
                    cursor.execute("""
                        INSERT OR REPLACE INTO cursor_auth (user_id, auth_key, auth_value) 
                        VALUES (?, ?, ?)
                    """, (self.user_id, auth_key, auth_value))
                
                # 如果提供了完整的账户信息，也更新accounts表
                if email and access_token:
                    cursor.execute("""
                        INSERT OR REPLACE INTO cursor_accounts 
                        (user_id, email, access_token, refresh_token, auth_type, is_active)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (self.user_id, email, access_token, refresh_token, auth_type, 1))
                
                conn.commit()
                cursor.close()
            
            print(f"\n{EMOJI['SUCCESS']} {Fore.GREEN}{self.translator.get('auth.auth_updated') if self.translator else 'Authentication updated successfully'}{Style.RESET_ALL}")
            return True
            
        except Exception as e:
            print(f"{EMOJI['ERROR']} {Fore.RED}更新认证信息失败: {e}{Style.RESET_ALL}")
            logger.error(f"更新认证信息失败: {e}")
            return False
    
    def get_auth_info(self, auth_key=None):
        """获取认证信息"""
        try:
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                if auth_key:
                    # 获取特定的认证信息
                    cursor.execute(
                        "SELECT auth_value FROM cursor_auth WHERE user_id = ? AND auth_key = ?",
                        (self.user_id, auth_key)
                    )
                    result = cursor.fetchone()
                    cursor.close()
                    return result[0] if result else None
                else:
                    # 获取所有认证信息
                    cursor.execute(
                        "SELECT auth_key, auth_value FROM cursor_auth WHERE user_id = ?",
                        (self.user_id,)
                    )
                    results = cursor.fetchall()
                    cursor.close()
                    return {row[0]: row[1] for row in results}
                    
        except Exception as e:
            logger.error(f"获取认证信息失败: {e}")
            return None
    
    def get_cursor_accounts(self):
        """获取Cursor账户列表"""
        try:
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT email, access_token, refresh_token, auth_type, is_active, created_at
                    FROM cursor_accounts 
                    WHERE user_id = ? AND is_active = 1
                    ORDER BY created_at DESC
                """, (self.user_id,))
                
                results = cursor.fetchall()
                cursor.close()
                
                accounts = []
                for row in results:
                    accounts.append({
                        'email': row[0],
                        'access_token': row[1],
                        'refresh_token': row[2],
                        'auth_type': row[3],
                        'is_active': bool(row[4]),
                        'created_at': row[5]
                    })
                
                return accounts
                
        except Exception as e:
            logger.error(f"获取账户列表失败: {e}")
            return []
    
    def delete_auth_info(self, auth_key=None):
        """删除认证信息"""
        try:
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                if auth_key:
                    # 删除特定认证信息
                    cursor.execute(
                        "DELETE FROM cursor_auth WHERE user_id = ? AND auth_key = ?",
                        (self.user_id, auth_key)
                    )
                else:
                    # 删除所有认证信息
                    cursor.execute(
                        "DELETE FROM cursor_auth WHERE user_id = ?",
                        (self.user_id,)
                    )
                    # 同时删除账户信息
                    cursor.execute(
                        "DELETE FROM cursor_accounts WHERE user_id = ?",
                        (self.user_id,)
                    )
                
                conn.commit()
                cursor.close()
            
            print(f"{EMOJI['SUCCESS']} {Fore.GREEN}认证信息删除成功{Style.RESET_ALL}")
            return True
            
        except Exception as e:
            print(f"{EMOJI['ERROR']} {Fore.RED}删除认证信息失败: {e}{Style.RESET_ALL}")
            return False
    
    def backup_auth_data(self):
        """备份认证数据"""
        try:
            auth_data = self.get_auth_info()
            accounts_data = self.get_cursor_accounts()
            
            backup_data = {
                'auth_info': auth_data,
                'accounts': accounts_data,
                'user_id': self.user_id
            }
            
            return backup_data
            
        except Exception as e:
            logger.error(f"备份认证数据失败: {e}")
            return None
    
    def restore_auth_data(self, backup_data):
        """恢复认证数据"""
        try:
            if not backup_data:
                return False
            
            # 清除现有数据
            self.delete_auth_info()
            
            # 恢复认证信息
            auth_info = backup_data.get('auth_info', {})
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                for auth_key, auth_value in auth_info.items():
                    cursor.execute("""
                        INSERT INTO cursor_auth (user_id, auth_key, auth_value) 
                        VALUES (?, ?, ?)
                    """, (self.user_id, auth_key, auth_value))
                
                # 恢复账户信息
                accounts = backup_data.get('accounts', [])
                for account in accounts:
                    cursor.execute("""
                        INSERT INTO cursor_accounts 
                        (user_id, email, access_token, refresh_token, auth_type, is_active)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        self.user_id,
                        account['email'],
                        account['access_token'],
                        account['refresh_token'],
                        account['auth_type'],
                        1 if account['is_active'] else 0
                    ))
                
                conn.commit()
                cursor.close()
            
            print(f"{EMOJI['SUCCESS']} {Fore.GREEN}认证数据恢复成功{Style.RESET_ALL}")
            return True
            
        except Exception as e:
            print(f"{EMOJI['ERROR']} {Fore.RED}恢复认证数据失败: {e}{Style.RESET_ALL}")
            return False

# 向后兼容的别名
CursorAuthSQLite = CursorAuth
