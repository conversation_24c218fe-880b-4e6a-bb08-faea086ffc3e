#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
防检测参数调优工具
Anti-Detection Parameter Tuning Tool
"""

import json
import os
import time
from datetime import datetime, timedelta
from colorama import Fore, Style
from typing import Dict, List, Optional

class ParameterTuner:
    """参数调优器"""
    
    def __init__(self):
        self.config_file = "anti_detection_config.json"
        self.performance_log = "performance_log.json"
        self.default_config = self._get_default_config()
        self.current_config = self._load_config()
        
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            "risk_control": {
                "success_rate_threshold": 0.7,
                "max_daily_registrations": 10,
                "high_risk_threshold": 60,
                "medium_risk_threshold": 30
            },
            "behavior_simulation": {
                "typing_speed_min": 0.06,
                "typing_speed_max": 0.12,
                "think_pause_probability": 0.08,
                "error_probability": 0.02,
                "scroll_actions_min": 3,
                "scroll_actions_max": 6
            },
            "timing_control": {
                "base_wait_time": 1.0,
                "variance_factor": 0.5,
                "observation_time_min": 2.0,
                "observation_time_max": 5.0,
                "natural_pause_min": 3.0,
                "natural_pause_max": 8.0
            },
            "safety_limits": {
                "min_delay_between_attempts": 300,  # 5分钟
                "max_delay_between_attempts": 900,  # 15分钟
                "cooldown_after_failure": 1800,    # 30分钟
                "max_consecutive_failures": 3
            }
        }
    
    def _load_config(self) -> Dict:
        """加载配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return self.default_config.copy()
    
    def _save_config(self):
        """保存配置"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.current_config, f, indent=2, ensure_ascii=False)
    
    def _load_performance_log(self) -> List[Dict]:
        """加载性能日志"""
        if os.path.exists(self.performance_log):
            try:
                with open(self.performance_log, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return []
    
    def _save_performance_log(self, log_data: List[Dict]):
        """保存性能日志"""
        with open(self.performance_log, 'w', encoding='utf-8') as f:
            json.dump(log_data, f, indent=2, ensure_ascii=False)
    
    def log_performance(self, success: bool, detection_risk: str, response_time: float, notes: str = ""):
        """记录性能数据"""
        log_data = self._load_performance_log()
        
        entry = {
            "timestamp": datetime.now().isoformat(),
            "success": success,
            "detection_risk": detection_risk,
            "response_time": response_time,
            "notes": notes,
            "config_snapshot": self.current_config.copy()
        }
        
        log_data.append(entry)
        
        # 保持最近100条记录
        if len(log_data) > 100:
            log_data = log_data[-100:]
        
        self._save_performance_log(log_data)
        print(f"{Fore.CYAN}📊 性能数据已记录{Style.RESET_ALL}")
    
    def analyze_performance(self) -> Dict:
        """分析性能数据"""
        log_data = self._load_performance_log()
        
        if not log_data:
            return {"message": "暂无性能数据"}
        
        # 计算基础统计
        total_attempts = len(log_data)
        successful_attempts = sum(1 for entry in log_data if entry["success"])
        success_rate = successful_attempts / total_attempts if total_attempts > 0 else 0
        
        # 计算最近24小时的数据
        now = datetime.now()
        recent_data = [
            entry for entry in log_data
            if datetime.fromisoformat(entry["timestamp"]) > now - timedelta(hours=24)
        ]
        
        recent_success_rate = 0
        if recent_data:
            recent_successful = sum(1 for entry in recent_data if entry["success"])
            recent_success_rate = recent_successful / len(recent_data)
        
        # 风险等级分布
        risk_distribution = {}
        for entry in log_data:
            risk = entry.get("detection_risk", "未知")
            risk_distribution[risk] = risk_distribution.get(risk, 0) + 1
        
        # 平均响应时间
        response_times = [entry.get("response_time", 0) for entry in log_data if entry.get("response_time")]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        return {
            "total_attempts": total_attempts,
            "successful_attempts": successful_attempts,
            "success_rate": f"{success_rate:.1%}",
            "recent_24h_attempts": len(recent_data),
            "recent_24h_success_rate": f"{recent_success_rate:.1%}",
            "risk_distribution": risk_distribution,
            "avg_response_time": f"{avg_response_time:.2f}s",
            "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    
    def suggest_optimizations(self) -> List[str]:
        """建议优化方案"""
        analysis = self.analyze_performance()
        suggestions = []
        
        if "success_rate" in analysis:
            success_rate = float(analysis["success_rate"].rstrip('%')) / 100
            
            if success_rate < 0.5:
                suggestions.extend([
                    "🔴 成功率过低，建议：",
                    "  - 增加操作间隔时间",
                    "  - 降低每日注册数量",
                    "  - 更换IP地址和浏览器环境",
                    "  - 增加人类行为模拟的随机性"
                ])
            elif success_rate < 0.7:
                suggestions.extend([
                    "🟡 成功率偏低，建议：",
                    "  - 适当增加等待时间",
                    "  - 优化打字速度参数",
                    "  - 增加思考停顿频率"
                ])
            else:
                suggestions.append("🟢 成功率良好，保持当前策略")
        
        # 检查风险分布
        if "risk_distribution" in analysis:
            risk_dist = analysis["risk_distribution"]
            high_risk_count = risk_dist.get("高", 0)
            total_count = sum(risk_dist.values())
            
            if total_count > 0 and high_risk_count / total_count > 0.3:
                suggestions.extend([
                    "⚠️ 高风险操作过多，建议：",
                    "  - 大幅增加操作间隔",
                    "  - 暂停操作24小时",
                    "  - 更换网络环境"
                ])
        
        if not suggestions:
            suggestions.append("✅ 当前参数配置良好，无需调整")
        
        return suggestions
    
    def auto_tune_parameters(self):
        """自动调优参数"""
        print(f"{Fore.BLUE}🔧 开始自动参数调优...{Style.RESET_ALL}")
        
        analysis = self.analyze_performance()
        
        if "success_rate" in analysis:
            success_rate = float(analysis["success_rate"].rstrip('%')) / 100
            
            # 根据成功率调整参数
            if success_rate < 0.5:
                # 成功率过低，采用保守策略
                self.current_config["risk_control"]["max_daily_registrations"] = max(3, 
                    self.current_config["risk_control"]["max_daily_registrations"] - 2)
                
                self.current_config["timing_control"]["base_wait_time"] *= 1.5
                self.current_config["timing_control"]["natural_pause_min"] *= 1.3
                self.current_config["timing_control"]["natural_pause_max"] *= 1.3
                
                self.current_config["safety_limits"]["min_delay_between_attempts"] *= 1.5
                
                print(f"{Fore.YELLOW}📉 成功率过低，已调整为保守策略{Style.RESET_ALL}")
                
            elif success_rate > 0.8:
                # 成功率很高，可以适当激进
                self.current_config["risk_control"]["max_daily_registrations"] = min(15,
                    self.current_config["risk_control"]["max_daily_registrations"] + 1)
                
                self.current_config["timing_control"]["base_wait_time"] *= 0.9
                self.current_config["safety_limits"]["min_delay_between_attempts"] *= 0.9
                
                print(f"{Fore.GREEN}📈 成功率很高，已调整为稍微激进策略{Style.RESET_ALL}")
        
        # 检查风险分布并调整
        if "risk_distribution" in analysis:
            risk_dist = analysis["risk_distribution"]
            high_risk_count = risk_dist.get("高", 0)
            total_count = sum(risk_dist.values())
            
            if total_count > 0 and high_risk_count / total_count > 0.2:
                # 高风险操作过多
                self.current_config["safety_limits"]["min_delay_between_attempts"] *= 1.8
                self.current_config["safety_limits"]["cooldown_after_failure"] *= 1.5
                
                print(f"{Fore.RED}⚠️ 高风险操作过多，已增加安全间隔{Style.RESET_ALL}")
        
        self._save_config()
        print(f"{Fore.GREEN}✅ 参数自动调优完成{Style.RESET_ALL}")
    
    def manual_tune_parameters(self):
        """手动调优参数"""
        print(f"{Fore.BLUE}🔧 手动参数调优{Style.RESET_ALL}")
        print("=" * 40)
        
        categories = [
            ("风险控制", "risk_control"),
            ("行为模拟", "behavior_simulation"), 
            ("时间控制", "timing_control"),
            ("安全限制", "safety_limits")
        ]
        
        for category_name, category_key in categories:
            print(f"\n{Fore.CYAN}📂 {category_name}:{Style.RESET_ALL}")
            
            for param_key, param_value in self.current_config[category_key].items():
                print(f"  {param_key}: {param_value}")
                
                try:
                    new_value = input(f"    新值 (回车跳过): ").strip()
                    if new_value:
                        # 尝试转换为适当的类型
                        if isinstance(param_value, float):
                            self.current_config[category_key][param_key] = float(new_value)
                        elif isinstance(param_value, int):
                            self.current_config[category_key][param_key] = int(new_value)
                        else:
                            self.current_config[category_key][param_key] = new_value
                        
                        print(f"    ✅ 已更新为: {new_value}")
                except ValueError:
                    print(f"    ❌ 无效值，跳过")
                except KeyboardInterrupt:
                    print(f"\n{Fore.YELLOW}⏹️ 手动调优已取消{Style.RESET_ALL}")
                    return
        
        self._save_config()
        print(f"\n{Fore.GREEN}✅ 手动参数调优完成{Style.RESET_ALL}")
    
    def reset_to_defaults(self):
        """重置为默认参数"""
        self.current_config = self.default_config.copy()
        self._save_config()
        print(f"{Fore.GREEN}✅ 参数已重置为默认值{Style.RESET_ALL}")
    
    def export_config(self, filename: str = None):
        """导出配置"""
        if not filename:
            filename = f"config_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.current_config, f, indent=2, ensure_ascii=False)
        
        print(f"{Fore.GREEN}✅ 配置已导出到: {filename}{Style.RESET_ALL}")
    
    def import_config(self, filename: str):
        """导入配置"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            self.current_config = imported_config
            self._save_config()
            
            print(f"{Fore.GREEN}✅ 配置已从 {filename} 导入{Style.RESET_ALL}")
        except Exception as e:
            print(f"{Fore.RED}❌ 配置导入失败: {e}{Style.RESET_ALL}")
    
    def show_current_config(self):
        """显示当前配置"""
        print(f"{Fore.BLUE}⚙️ 当前配置:{Style.RESET_ALL}")
        print("=" * 40)
        
        for category, params in self.current_config.items():
            print(f"\n{Fore.CYAN}📂 {category}:{Style.RESET_ALL}")
            for key, value in params.items():
                print(f"  {key}: {value}")

def main():
    """主函数"""
    tuner = ParameterTuner()
    
    while True:
        print(f"\n{Fore.GREEN}🔧 防检测参数调优工具{Style.RESET_ALL}")
        print("=" * 40)
        print("1. 查看性能分析")
        print("2. 获取优化建议")
        print("3. 自动调优参数")
        print("4. 手动调优参数")
        print("5. 显示当前配置")
        print("6. 重置为默认值")
        print("7. 导出配置")
        print("8. 导入配置")
        print("9. 记录测试数据")
        print("0. 退出")
        
        try:
            choice = input(f"\n{Fore.YELLOW}请选择操作 (0-9): {Style.RESET_ALL}").strip()
            
            if choice == "1":
                analysis = tuner.analyze_performance()
                print(f"\n{Fore.CYAN}📊 性能分析:{Style.RESET_ALL}")
                for key, value in analysis.items():
                    print(f"  {key}: {value}")
            
            elif choice == "2":
                suggestions = tuner.suggest_optimizations()
                print(f"\n{Fore.CYAN}💡 优化建议:{Style.RESET_ALL}")
                for suggestion in suggestions:
                    print(f"  {suggestion}")
            
            elif choice == "3":
                tuner.auto_tune_parameters()
            
            elif choice == "4":
                tuner.manual_tune_parameters()
            
            elif choice == "5":
                tuner.show_current_config()
            
            elif choice == "6":
                confirm = input("确认重置为默认值? (y/N): ").strip().lower()
                if confirm == 'y':
                    tuner.reset_to_defaults()
            
            elif choice == "7":
                filename = input("导出文件名 (回车使用默认): ").strip()
                tuner.export_config(filename if filename else None)
            
            elif choice == "8":
                filename = input("导入文件名: ").strip()
                if filename:
                    tuner.import_config(filename)
            
            elif choice == "9":
                success = input("操作成功? (y/N): ").strip().lower() == 'y'
                risk = input("风险等级 (低/中/高): ").strip() or "低"
                response_time = float(input("响应时间(秒): ").strip() or "0")
                notes = input("备注: ").strip()
                
                tuner.log_performance(success, risk, response_time, notes)
            
            elif choice == "0":
                print(f"{Fore.GREEN}👋 再见！{Style.RESET_ALL}")
                break
            
            else:
                print(f"{Fore.RED}❌ 无效选择{Style.RESET_ALL}")
        
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}👋 再见！{Style.RESET_ALL}")
            break
        except Exception as e:
            print(f"{Fore.RED}❌ 操作失败: {e}{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
