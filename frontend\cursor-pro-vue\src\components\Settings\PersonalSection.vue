<template>
  <FormCard title="个人信息">
    <div class="form-fields">
      <div class="form-field">
        <label class="field-label">名字</label>
        <input 
          class="field-input" 
          type="text" 
          :value="firstName" 
          @input="updateFirstName"
          placeholder="请输入名字" 
        />
      </div>
      
      <div class="form-field">
        <label class="field-label">姓氏</label>
        <input 
          class="field-input" 
          type="text" 
          :value="lastName" 
          @input="updateLastName"
          placeholder="请输入姓氏" 
        />
      </div>
      
      <div class="form-field">
        <label class="field-label">密码</label>
        <input 
          class="field-input" 
          type="password" 
          :value="password" 
          @input="updatePassword"
          placeholder="请输入密码" 
        />
      </div>
    </div>
  </FormCard>
</template>

<script setup lang="ts">
import FormCard from '../Common/FormCard.vue'

// Props定义
interface Props {
  firstName: string
  lastName: string
  password: string
}

// Emits定义
interface Emits {
  (e: 'update:firstName', value: string): void
  (e: 'update:lastName', value: string): void
  (e: 'update:password', value: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 更新方法
const updateFirstName = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('update:firstName', target.value)
}

const updateLastName = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('update:lastName', target.value)
}

const updatePassword = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('update:password', target.value)
}
</script>

<style scoped>
.form-fields {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field-label {
  color: #006aaaff;
  font-weight: bold;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  font-size: 1.15em;
  text-shadow: 0em 0.1em 0em var(--color2);
}

.field-input {
  background-color: white;
  border: none;
  border-right: 5px solid #ff7c90ff;
  border-bottom: 5px solid #ff7c90ff;
  border-bottom-right-radius: 25em;
  height: 2em;
  width: 90%;
  transition: all 0.5s ease;
  color: #ff7c90ff;
  font-weight: bold;
  padding-left: 0.5em;
  font-size: 1.05em;
  box-shadow: 1em 0em 0em;
  outline: none;
}

.field-input:hover {
  width: 100%;
  background-color: white;
  box-shadow: 0em 0em 0em;
}

.field-input:focus {
  width: 100%;
  background-color: white;
  box-shadow: 0em 0em 0em;
  border-color: #006aaaff;
}
</style>
