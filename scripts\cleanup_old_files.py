#!/usr/bin/env python3
"""
清理旧文件脚本
删除重构前的旧Python文件，保留必要的文件
"""

import os
import shutil
from pathlib import Path

# 需要删除的旧Python文件列表
OLD_FILES_TO_DELETE = [
    "main.py",
    "vue_with_api_optimized.py", 
    "cursor_acc_info.py",
    "cursor_auth.py",
    "oauth_auth.py",
    "get_user_token.py",
    "new_signup.py",
    "cursor_register_manual.py",
    "backup_settings.py",
    "backup_session.py",
    "advanced_anti_detection.py",
    "anti_detection_integration.py",
    "cursor_pro_anti_detection.py",
    "bypass_token_limit.py",
    "bypass_version.py",
    "reset_machine_manual.py",
    "account_manager.py",
    "admin_panel.py",
    "config.py",
    "data_path_manager.py",
    "legacy_path_adapter.py",
    "utils.py",
    "app_version.py",
    "version_control.py",
    "parameter_tuning.py",
    "disable_auto_update.py",
    "check_user_authorized.py",
]

# 需要删除的旧目录
OLD_DIRS_TO_DELETE = [
    "database",  # 已移动到src/cursor_pro/database
    "__pycache__",
]

# 需要保留的文件（不删除）
KEEP_FILES = [
    "README.md",
    "requirements.txt",
    "setup.py",
    "pyproject.toml",
    ".gitignore",
    "MIGRATION_GUIDE.md",
    "start.bat",
    "start-dev.bat",
    "start-new.bat",
    "build-release.cmd",
    "package.json",
    "package-lock.json",
    "启动说明.md",
    "群发布使用说明.md",
]

def create_backup_list():
    """创建备份文件列表"""
    project_root = Path(__file__).parent.parent
    backup_file = project_root / "DELETED_FILES_BACKUP.txt"
    
    with open(backup_file, 'w', encoding='utf-8') as f:
        f.write("# Cursor Pro 重构 - 已删除文件列表\n")
        f.write("# 生成时间: " + str(Path().cwd()) + "\n\n")
        
        f.write("## 已删除的Python文件:\n")
        for file in OLD_FILES_TO_DELETE:
            if (project_root / file).exists():
                f.write(f"- {file}\n")
        
        f.write("\n## 已删除的目录:\n")
        for dir_name in OLD_DIRS_TO_DELETE:
            if (project_root / dir_name).exists():
                f.write(f"- {dir_name}/\n")
    
    print(f"✅ 备份列表已创建: {backup_file}")

def cleanup_old_files():
    """清理旧文件"""
    project_root = Path(__file__).parent.parent
    deleted_count = 0
    
    print("🧹 开始清理旧文件...")
    
    # 删除旧Python文件
    for file_name in OLD_FILES_TO_DELETE:
        file_path = project_root / file_name
        if file_path.exists():
            try:
                file_path.unlink()
                print(f"🗑️ 删除文件: {file_name}")
                deleted_count += 1
            except Exception as e:
                print(f"❌ 删除失败: {file_name} - {e}")
        else:
            print(f"⏭️ 文件不存在: {file_name}")
    
    # 删除旧目录
    for dir_name in OLD_DIRS_TO_DELETE:
        dir_path = project_root / dir_name
        if dir_path.exists():
            try:
                shutil.rmtree(dir_path)
                print(f"🗑️ 删除目录: {dir_name}/")
                deleted_count += 1
            except Exception as e:
                print(f"❌ 删除失败: {dir_name}/ - {e}")
        else:
            print(f"⏭️ 目录不存在: {dir_name}/")
    
    print(f"\n📊 清理完成:")
    print(f"   删除项目数: {deleted_count}")
    print(f"   保留重要文件: {len(KEEP_FILES)} 个")

def main():
    """主函数"""
    print("🚀 Cursor Pro 项目清理工具")
    print("=" * 50)
    
    # 确认操作
    response = input("⚠️ 确定要删除旧文件吗？这个操作不可逆！(y/N): ")
    if response.lower() != 'y':
        print("❌ 操作已取消")
        return
    
    # 创建备份列表
    create_backup_list()
    
    # 执行清理
    cleanup_old_files()
    
    print("\n✅ 清理完成！")
    print("📋 已删除文件的列表保存在: DELETED_FILES_BACKUP.txt")
    print("🔄 新的项目结构位于: src/cursor_pro/")

if __name__ == "__main__":
    main()
