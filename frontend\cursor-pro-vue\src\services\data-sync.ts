/**
 * 数据同步服务
 * 处理不同存储位置之间的数据同步
 */

import { dataManager, DataLocation } from '@/utils/data-manager'
import { ElMessage } from 'element-plus'

// 同步状态枚举
export enum SyncStatus {
  IDLE = 'idle',
  SYNCING = 'syncing',
  SUCCESS = 'success',
  ERROR = 'error'
}

// 同步配置
export interface SyncConfig {
  autoSync: boolean
  syncInterval: number // 毫秒
  conflictResolution: 'local' | 'remote' | 'manual'
  enabledSources: DataLocation[]
}

// 同步结果
export interface SyncResult {
  status: SyncStatus
  message: string
  conflicts: Array<{
    key: string
    localValue: any
    remoteValue: any
    resolution?: 'local' | 'remote'
  }>
  syncedKeys: string[]
  errors: string[]
}

/**
 * 数据同步服务类
 */
export class DataSyncService {
  private static instance: DataSyncService
  private syncStatus: SyncStatus = SyncStatus.IDLE
  private syncConfig: SyncConfig = {
    autoSync: false, // 禁用自动同步
    syncInterval: 5 * 60 * 1000, // 5分钟
    conflictResolution: 'remote',
    enabledSources: [DataLocation.LOCAL_STORAGE, DataLocation.API_SERVER]
  }
  private syncTimer?: number
  private syncQueue: string[] = []

  private constructor() {
    // 完全禁用自动同步 - 项目不需要这个功能
    console.log('📝 数据同步服务已禁用 - 使用直接API调用模式')
  }

  static getInstance(): DataSyncService {
    if (!DataSyncService.instance) {
      DataSyncService.instance = new DataSyncService()
    }
    return DataSyncService.instance
  }

  /**
   * 检查API可用性并启动同步
   */
  private async checkApiAvailabilityAndStart(): Promise<void> {
    try {
      // 尝试访问一个简单的API端点来检查后端是否可用
      const response = await fetch('http://localhost:8080/api/get_account_info', {
        method: 'GET',
        timeout: 3000
      } as RequestInit)

      if (response.ok || response.status === 500) {
        // 后端可用（即使返回错误也说明服务器在运行）
        console.log('✅ 后端API可用，启动数据同步服务')
        this.startAutoSync()
      } else {
        console.log('⚠️ 后端API不可用，跳过数据同步')
      }
    } catch (error) {
      console.log('⚠️ 后端API连接失败，跳过数据同步:', error)
      // 不启动自动同步，避免产生错误日志
    }
  }

  /**
   * 开始自动同步
   */
  startAutoSync(): void {
    if (this.syncConfig.autoSync && !this.syncTimer) {
      this.syncTimer = window.setInterval(() => {
        this.syncAll()
      }, this.syncConfig.syncInterval)
      console.log('🔄 自动同步已启动')
    }
  }

  /**
   * 停止自动同步
   */
  stopAutoSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer)
      this.syncTimer = undefined
      console.log('⏹️ 自动同步已停止')
    }
  }

  /**
   * 同步所有数据
   */
  async syncAll(): Promise<SyncResult> {
    if (this.syncStatus === SyncStatus.SYNCING) {
      return {
        status: SyncStatus.ERROR,
        message: '同步正在进行中',
        conflicts: [],
        syncedKeys: [],
        errors: ['同步正在进行中']
      }
    }

    this.syncStatus = SyncStatus.SYNCING
    console.log('🔄 开始全量数据同步')

    const result: SyncResult = {
      status: SyncStatus.SUCCESS,
      message: '同步完成',
      conflicts: [],
      syncedKeys: [],
      errors: []
    }

    try {
      // 定义需要同步的数据键
      const keysToSync = [
        'cursor-accounts',
        'app-config',
        'register-config',
        'cursor-account-info'
      ]

      for (const key of keysToSync) {
        try {
          const syncKeyResult = await this.syncKey(key)
          
          if (syncKeyResult.conflicts.length > 0) {
            result.conflicts.push(...syncKeyResult.conflicts)
          }
          
          if (syncKeyResult.status === SyncStatus.SUCCESS) {
            result.syncedKeys.push(key)
          } else {
            result.errors.push(`${key}: ${syncKeyResult.message}`)
          }
        } catch (error) {
          const errorMsg = error instanceof Error ? error.message : String(error)
          // 如果是网络连接错误，不记录为错误（避免控制台噪音）
          if (errorMsg.includes('Failed to fetch') || errorMsg.includes('fetch')) {
            console.log(`⚠️ 跳过同步 [${key}]: API服务器不可用`)
          } else {
            result.errors.push(`${key}: ${errorMsg}`)
          }
        }
      }

      // 处理冲突
      if (result.conflicts.length > 0) {
        await this.resolveConflicts(result.conflicts)
      }

      if (result.errors.length === 0) {
        result.status = SyncStatus.SUCCESS
        result.message = `成功同步 ${result.syncedKeys.length} 个数据项`
        console.log('✅ 数据同步完成:', result.message)
      } else {
        result.status = SyncStatus.ERROR
        result.message = `同步完成，但有 ${result.errors.length} 个错误`
        console.warn('⚠️ 数据同步有错误:', result.errors)
      }

    } catch (error) {
      result.status = SyncStatus.ERROR
      result.message = `同步失败: ${error instanceof Error ? error.message : String(error)}`
      result.errors.push(result.message)
      console.error('❌ 数据同步失败:', error)
    } finally {
      this.syncStatus = SyncStatus.IDLE
    }

    return result
  }

  /**
   * 同步单个数据键
   */
  async syncKey(key: string): Promise<SyncResult> {
    const result: SyncResult = {
      status: SyncStatus.SUCCESS,
      message: '同步成功',
      conflicts: [],
      syncedKeys: [],
      errors: []
    }

    try {
      // 从不同源获取数据
      const localData = await dataManager.getData(key, DataLocation.LOCAL_STORAGE)
      const remoteData = await dataManager.getData(key, DataLocation.API_SERVER)

      // 检查是否有冲突
      if (localData !== null && remoteData !== null) {
        const localStr = JSON.stringify(localData)
        const remoteStr = JSON.stringify(remoteData)
        
        if (localStr !== remoteStr) {
          // 发现冲突
          result.conflicts.push({
            key,
            localValue: localData,
            remoteValue: remoteData
          })
          
          console.log(`⚠️ 发现数据冲突 [${key}]:`, {
            local: localData,
            remote: remoteData
          })
        }
      } else if (localData !== null && remoteData === null) {
        // 本地有数据，远程没有 - 上传到远程
        await dataManager.setData(key, localData, DataLocation.API_SERVER)
        result.syncedKeys.push(key)
        console.log(`⬆️ 上传到远程 [${key}]`)
      } else if (localData === null && remoteData !== null) {
        // 远程有数据，本地没有 - 下载到本地
        await dataManager.setData(key, remoteData, DataLocation.LOCAL_STORAGE)
        result.syncedKeys.push(key)
        console.log(`⬇️ 下载到本地 [${key}]`)
      }

    } catch (error) {
      result.status = SyncStatus.ERROR
      result.message = `同步失败: ${error instanceof Error ? error.message : String(error)}`
      result.errors.push(result.message)
    }

    return result
  }

  /**
   * 解决冲突
   */
  private async resolveConflicts(conflicts: SyncResult['conflicts']): Promise<void> {
    for (const conflict of conflicts) {
      let resolvedValue: any
      
      switch (this.syncConfig.conflictResolution) {
        case 'local':
          resolvedValue = conflict.localValue
          conflict.resolution = 'local'
          break
        case 'remote':
          resolvedValue = conflict.remoteValue
          conflict.resolution = 'remote'
          break
        case 'manual':
          // 手动解决冲突 - 这里可以弹出对话框让用户选择
          resolvedValue = await this.manualConflictResolution(conflict)
          break
        default:
          resolvedValue = conflict.remoteValue
          conflict.resolution = 'remote'
      }

      // 应用解决方案
      await dataManager.setData(conflict.key, resolvedValue, DataLocation.LOCAL_STORAGE)
      await dataManager.setData(conflict.key, resolvedValue, DataLocation.API_SERVER)
      
      console.log(`🔧 冲突已解决 [${conflict.key}]:`, conflict.resolution)
    }
  }

  /**
   * 手动冲突解决
   */
  private async manualConflictResolution(conflict: SyncResult['conflicts'][0]): Promise<any> {
    // 这里应该弹出一个对话框让用户选择
    // 暂时使用远程数据
    return new Promise((resolve) => {
      ElMessage({
        message: `数据冲突: ${conflict.key}，使用远程数据`,
        type: 'warning',
        duration: 3000
      })
      resolve(conflict.remoteValue)
    })
  }

  /**
   * 强制同步（忽略冲突）
   */
  async forceSync(direction: 'upload' | 'download'): Promise<SyncResult> {
    console.log(`🔄 开始强制同步 (${direction})`)
    
    const result: SyncResult = {
      status: SyncStatus.SUCCESS,
      message: '强制同步完成',
      conflicts: [],
      syncedKeys: [],
      errors: []
    }

    try {
      const keysToSync = [
        'cursor-accounts',
        'app-config',
        'register-config',
        'cursor-account-info'
      ]

      for (const key of keysToSync) {
        try {
          if (direction === 'upload') {
            // 从本地上传到远程
            const localData = await dataManager.getData(key, DataLocation.LOCAL_STORAGE)
            if (localData !== null) {
              await dataManager.setData(key, localData, DataLocation.API_SERVER)
              result.syncedKeys.push(key)
            }
          } else {
            // 从远程下载到本地
            const remoteData = await dataManager.getData(key, DataLocation.API_SERVER)
            if (remoteData !== null) {
              await dataManager.setData(key, remoteData, DataLocation.LOCAL_STORAGE)
              result.syncedKeys.push(key)
            }
          }
        } catch (error) {
          result.errors.push(`${key}: ${error instanceof Error ? error.message : String(error)}`)
        }
      }

      if (result.errors.length === 0) {
        ElMessage.success(`强制同步完成，同步了 ${result.syncedKeys.length} 个数据项`)
      } else {
        ElMessage.warning(`强制同步完成，但有 ${result.errors.length} 个错误`)
      }

    } catch (error) {
      result.status = SyncStatus.ERROR
      result.message = `强制同步失败: ${error instanceof Error ? error.message : String(error)}`
      ElMessage.error(result.message)
    }

    return result
  }

  /**
   * 获取同步状态
   */
  getSyncStatus(): SyncStatus {
    return this.syncStatus
  }

  /**
   * 更新同步配置
   */
  updateSyncConfig(config: Partial<SyncConfig>): void {
    this.syncConfig = { ...this.syncConfig, ...config }
    
    // 重启自动同步
    this.stopAutoSync()
    if (this.syncConfig.autoSync) {
      this.startAutoSync()
    }
    
    console.log('🔧 同步配置已更新:', this.syncConfig)
  }

  /**
   * 获取同步配置
   */
  getSyncConfig(): SyncConfig {
    return { ...this.syncConfig }
  }

  /**
   * 添加到同步队列
   */
  queueSync(key: string): void {
    if (!this.syncQueue.includes(key)) {
      this.syncQueue.push(key)
      console.log(`📝 已添加到同步队列: ${key}`)
    }
  }

  /**
   * 处理同步队列
   */
  async processSyncQueue(): Promise<void> {
    if (this.syncQueue.length === 0) return

    console.log(`🔄 处理同步队列，共 ${this.syncQueue.length} 个项目`)

    const keysToProcess = [...this.syncQueue]
    this.syncQueue = []

    for (const key of keysToProcess) {
      try {
        await this.syncKey(key)
        console.log(`✅ 队列同步完成: ${key}`)
      } catch (error) {
        console.error(`❌ 队列同步失败 [${key}]:`, error)
      }
    }
  }
}

// 导出单例实例
export const dataSyncService = DataSyncService.getInstance()

// 便捷函数
export const syncAll = () => dataSyncService.syncAll()
export const syncKey = (key: string) => dataSyncService.syncKey(key)
export const forceSync = (direction: 'upload' | 'download') => dataSyncService.forceSync(direction)
