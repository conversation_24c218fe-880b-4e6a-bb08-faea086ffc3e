# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 收集所有 Python 文件
a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        # 包含配置文件和数据文件
        ('*.json', '.'),
        ('*.txt', '.'),
        ('*.md', '.'),
        ('locales', 'locales'),
        ('database', 'database'),
        ('api', 'api'),
        ('email_tabs', 'email_tabs'),
        ('images', 'images'),
        ('public', 'public'),
        ('docs', 'docs'),
    ],
    hiddenimports=[
        # 显式导入所有模块
        'version_control',
        'legacy_path_adapter',
        'data_path_manager',
        'account_manager',
        'advanced_anti_detection',
        'anti_detection_integration',
        'backup_session',
        'backup_settings',
        'bypass_token_limit',
        'bypass_version',
        'config',
        'cursor_acc_info',
        'cursor_auth',
        'cursor_pro_anti_detection',
        'cursor_register_manual',
        'enhanced_new_signup',
        'get_user_token',
        'main',
        'new_signup',
        'oauth_auth',
        'parameter_tuning',
        'reset_machine_manual',
        'utils',
        'flask',
        'flask_cors',
        'requests',
        'selenium',
        'playwright',
        'undetected_chromedriver',
        'colorama',
        'pandas',
        'openpyxl',
        'psutil',
        'configparser',
        'python_dotenv',
        'dateutil',
        'ujson',
        'cryptography',
        'urllib3',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='cursor-backend',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='images/logo.png' if os.path.exists('images/logo.png') else None,
)
