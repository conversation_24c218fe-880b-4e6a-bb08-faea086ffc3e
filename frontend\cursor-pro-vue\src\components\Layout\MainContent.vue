<template>
  <div class="main-content">
    <!-- 顶部栏 -->
    <div class="top-bar">
      <span class="version-info">{{ versionDisplay }}</span>
      <WindowControls />
    </div>
    <!-- 主滚动区域 -->
    <div class="main-scroll">
      <!-- 动态组件，根据当前视图显示不同页面 -->
      <!-- 使用keep-alive缓存组件，避免重复创建销毁 -->
      <keep-alive :include="cachedComponents">
        <component :is="currentComponent" :key="props.currentView" />
      </keep-alive>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'
import { realAPI } from '@/api/real-api'
import Dashboard from '@/views/Dashboard.vue'
import Features from '@/views/Features.vue'
import Settings from '@/views/SettingsNew.vue'
import About from '@/views/About.vue'
import Logs from '@/views/Logs.vue'
import WindowControls from '@/components/Common/WindowControls.vue'

interface MainContentProps {
  currentView: string
}

const props = defineProps<MainContentProps>()

// 版本显示
const versionDisplay = ref('Pro Version Activator v1.11.03')

// 获取Cursor版本
const loadCursorVersion = async () => {
  try {
    const response = await realAPI.getCursorVersion()
    if (response.success && response.data) {
      const cursorVersion = response.data.version
      versionDisplay.value = `Pro Version Activator for Cursor v${cursorVersion}`
    }
  } catch (error) {
    console.warn('获取Cursor版本失败，使用默认版本显示:', error)
    // 保持默认版本显示
  }
}

// 组件映射
const componentMap = {
  dashboard: Dashboard,
  features: Features,
  settings: Settings,
  about: About,
  logs: Logs
}

// 需要缓存的组件列表
const cachedComponents = ref(['Dashboard', 'Features', 'Settings', 'Logs'])

// 计算当前组件
const currentComponent = computed(() => {
  return componentMap[props.currentView as keyof typeof componentMap] || Dashboard
})

// 初始化
onMounted(() => {
  loadCursorVersion()
})
</script>

<style scoped>
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0 0 3px 0;
  height: 100vh;
  overflow: hidden;
  background: #181c20;
  box-shadow: none;

}

.top-bar {
  background: #23272e;
  padding: 22px 38px 12px 38px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1.5px solid #2d323a;
  box-shadow: 0 2px 12px rgba(96,165,250,0.04);
  -webkit-app-region: drag; /* 使顶部栏可拖拽 */

}

.version-info {
  color: #facc15;
  font-size: 1.12rem;
  font-weight: 700;
  letter-spacing: 1px;
  text-shadow: 0 2px 8px #23272e44;
}



.main-scroll {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;
  min-width: 0;
  box-sizing: border-box;
}
</style>
