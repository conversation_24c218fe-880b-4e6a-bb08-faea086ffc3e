/**
 * 设置相关API服务
 * 处理配置的保存、加载、重置等操作
 */

import { BaseAPI, ApiResponse } from '../base'
import type {
  SettingsData,
  SaveSettingsRequest,
  SaveSettingsResponse,
  LoadSettingsResponse,
  AppConfig,
  UpdateConfigRequest,
  UpdateConfigResponse
} from '../types'

/**
 * 设置服务类
 * 继承BaseAPI，提供设置相关的API调用
 */
export class SettingsService extends BaseAPI {
  /**
   * 保存注册配置
   */
  async saveRegisterConfig(settings: SettingsData): Promise<SaveSettingsResponse> {
    console.log('🔄 [SettingsService] 保存注册配置:', settings)

    const request: SaveSettingsRequest = { settings }
    const response = await this.post<SaveSettingsResponse>('save_register_config', request)

    console.log('📡 [SettingsService] 保存配置响应:', response)
    return response.data
  }

  /**
   * 加载设置配置
   */
  async loadSettings(): Promise<SettingsData | null> {
    console.log('🔄 [SettingsService] 加载设置配置')

    try {
      const response = await this.get<LoadSettingsResponse>('load_settings')
      console.log('📡 [SettingsService] 加载配置响应:', response)
      
      return response.data.settings
    } catch (error) {
      console.log('⚠️ [SettingsService] 没有找到保存的配置')
      return null
    }
  }

  /**
   * 重置配置为默认值
   */
  async resetSettings(): Promise<boolean> {
    console.log('🔄 [SettingsService] 重置配置为默认值')

    const response = await this.post<{ success: boolean }>('reset_settings')
    console.log('📡 [SettingsService] 重置配置响应:', response)
    
    return response.data.success
  }

  /**
   * 导出配置
   */
  async exportConfig(): Promise<Blob> {
    console.log('🔄 [SettingsService] 导出配置')

    const response = await this.get<any>('export_config')
    
    // 将响应数据转换为Blob
    const blob = new Blob([JSON.stringify(response.data, null, 2)], {
      type: 'application/json'
    })
    
    console.log('📡 [SettingsService] 配置导出完成')
    return blob
  }

  /**
   * 导入配置
   */
  async importConfig(configData: any): Promise<boolean> {
    console.log('🔄 [SettingsService] 导入配置:', configData)

    const response = await this.post<{ success: boolean }>('import_config', configData)
    console.log('📡 [SettingsService] 导入配置响应:', response)
    
    return response.data.success
  }

  /**
   * 验证配置
   */
  async validateConfig(settings: SettingsData): Promise<{
    valid: boolean
    errors: string[]
  }> {
    console.log('🔄 [SettingsService] 验证配置:', settings)

    const response = await this.post<{
      valid: boolean
      errors: string[]
    }>('validate_config', { settings })
    
    console.log('📡 [SettingsService] 配置验证响应:', response)
    return response.data
  }

  /**
   * 获取应用配置
   */
  async getAppConfig(): Promise<AppConfig> {
    console.log('🔄 [SettingsService] 获取应用配置')

    const response = await this.get<AppConfig>('app_config')
    console.log('📡 [SettingsService] 应用配置响应:', response)
    
    return response.data
  }

  /**
   * 更新应用配置
   */
  async updateAppConfig(config: Partial<AppConfig>): Promise<UpdateConfigResponse> {
    console.log('🔄 [SettingsService] 更新应用配置:', config)

    const request: UpdateConfigRequest = { config }
    const response = await this.put<UpdateConfigResponse>('app_config', request)
    
    console.log('📡 [SettingsService] 更新配置响应:', response)
    return response.data
  }

  /**
   * 测试配置连接
   */
  async testConnection(settings?: Partial<SettingsData>): Promise<{
    success: boolean
    message: string
    details?: any
  }> {
    console.log('🔄 [SettingsService] 测试配置连接:', settings)

    const response = await this.post<{
      success: boolean
      message: string
      details?: any
    }>('test_connection', { settings })
    
    console.log('📡 [SettingsService] 连接测试响应:', response)
    return response.data
  }

  /**
   * 获取默认设置
   */
  async getDefaultSettings(): Promise<SettingsData> {
    console.log('🔄 [SettingsService] 获取默认设置')

    const response = await this.get<SettingsData>('default_settings')
    console.log('📡 [SettingsService] 默认设置响应:', response)
    
    return response.data
  }

  /**
   * 备份当前配置
   */
  async backupConfig(): Promise<{
    success: boolean
    backupId: string
    message: string
  }> {
    console.log('🔄 [SettingsService] 备份当前配置')

    const response = await this.post<{
      success: boolean
      backupId: string
      message: string
    }>('backup_config')
    
    console.log('📡 [SettingsService] 配置备份响应:', response)
    return response.data
  }

  /**
   * 恢复配置备份
   */
  async restoreConfig(backupId: string): Promise<{
    success: boolean
    message: string
    settings: SettingsData
  }> {
    console.log('🔄 [SettingsService] 恢复配置备份:', backupId)

    const response = await this.post<{
      success: boolean
      message: string
      settings: SettingsData
    }>('restore_config', { backupId })
    
    console.log('📡 [SettingsService] 配置恢复响应:', response)
    return response.data
  }

  /**
   * 获取配置历史
   */
  async getConfigHistory(): Promise<{
    history: Array<{
      id: string
      timestamp: string
      description: string
      settings: SettingsData
    }>
  }> {
    console.log('🔄 [SettingsService] 获取配置历史')

    const response = await this.get<{
      history: Array<{
        id: string
        timestamp: string
        description: string
        settings: SettingsData
      }>
    }>('config_history')
    
    console.log('📡 [SettingsService] 配置历史响应:', response)
    return response.data
  }
}

// 创建设置服务实例
export const settingsService = new SettingsService()

// 添加默认拦截器
settingsService.addRequestInterceptor(async (config) => {
  // 添加时间戳
  config.headers = {
    ...config.headers,
    'X-Request-Time': new Date().toISOString()
  }
  return config
})

settingsService.addResponseInterceptor(async (response) => {
  // 记录响应时间
  console.log(`📊 [SettingsService] API响应时间: ${Date.now() - (response.timestamp || 0)}ms`)
  return response
})

settingsService.addErrorInterceptor(async (error) => {
  // 统一错误日志
  console.error('❌ [SettingsService] API错误:', {
    type: error.type,
    message: error.message,
    status: error.status
  })
  return error
})
