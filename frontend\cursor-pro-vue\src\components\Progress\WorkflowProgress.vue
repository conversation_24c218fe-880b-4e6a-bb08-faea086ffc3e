<template>
  <div class="workflow-progress-overlay" v-if="visible" @click.self="handleOverlayClick">
    <div class="workflow-progress-modal" @click.stop>
      <!-- 标题 -->
      <div class="progress-header">
        <h3>🚀 一键完整流程</h3>
      </div>

      <!-- 森林露营动画 -->
      <div class="scene">
        <div class="forest">
          <div class="tree tree1">
            <div class="branch branch-top"></div>
            <div class="branch branch-middle"></div>
          </div>

          <div class="tree tree2">
            <div class="branch branch-top"></div>
            <div class="branch branch-middle"></div>
            <div class="branch branch-bottom"></div>
          </div>

          <div class="tree tree3">
            <div class="branch branch-top"></div>
            <div class="branch branch-middle"></div>
            <div class="branch branch-bottom"></div>
          </div>

          <div class="tree tree4">
            <div class="branch branch-top"></div>
            <div class="branch branch-middle"></div>
            <div class="branch branch-bottom"></div>
          </div>

          <div class="tree tree5">
            <div class="branch branch-top"></div>
            <div class="branch branch-middle"></div>
            <div class="branch branch-bottom"></div>
          </div>

          <div class="tree tree6">
            <div class="branch branch-top"></div>
            <div class="branch branch-middle"></div>
            <div class="branch branch-bottom"></div>
          </div>

          <div class="tree tree7">
            <div class="branch branch-top"></div>
            <div class="branch branch-middle"></div>
            <div class="branch branch-bottom"></div>
          </div>
        </div>

        <div class="tent">
          <div class="roof"></div>
          <div class="roof-border-left">
            <div class="roof-border roof-border1"></div>
            <div class="roof-border roof-border2"></div>
            <div class="roof-border roof-border3"></div>
          </div>
          <div class="entrance">
            <div class="door left-door">
              <div class="left-door-inner"></div>
            </div>
            <div class="door right-door">
              <div class="right-door-inner"></div>
            </div>
          </div>
        </div>

        <div class="floor">
          <div class="ground ground1"></div>
          <div class="ground ground2"></div>
        </div>

        <div class="fireplace">
          <div class="support"></div>
          <div class="support"></div>
          <div class="bar"></div>
          <div class="hanger"></div>
          <div class="smoke"></div>
          <div class="pan"></div>
          <div class="fire">
            <div class="line line1">
              <div class="particle particle1"></div>
              <div class="particle particle2"></div>
              <div class="particle particle3"></div>
              <div class="particle particle4"></div>
            </div>
            <div class="line line2">
              <div class="particle particle1"></div>
              <div class="particle particle2"></div>
              <div class="particle particle3"></div>
              <div class="particle particle4"></div>
            </div>
            <div class="line line3">
              <div class="particle particle1"></div>
              <div class="particle particle2"></div>
              <div class="particle particle3"></div>
              <div class="particle particle4"></div>
            </div>
          </div>
        </div>

        <div class="time-wrapper">
          <div class="time">
            <div class="day"></div>
            <div class="night">
              <div class="moon"></div>
              <div class="star star1 star-big"></div>
              <div class="star star2 star-big"></div>
              <div class="star star3 star-big"></div>
              <div class="star star4"></div>
              <div class="star star5"></div>
              <div class="star star6"></div>
              <div class="star star7"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 总体进度 -->
      <div class="overall-progress">
        <div class="progress-label">
          <span>总体进度</span>
          <span>{{ Math.round(overallProgress) }}%</span>
        </div>
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: overallProgress + '%' }"
          ></div>
        </div>
      </div>

      <!-- 当前步骤进度条 -->
      <div class="steps-progress">
        <div class="step-progress-item">
          <div class="step-info">
            <span class="step-name">{{ currentStep.title }}</span>
            <div class="step-right">
              <span class="step-counter">{{ currentStepIndex + 1 }}/{{ steps.length }}</span>
              <span class="step-status" :class="currentStep.status">{{ getStatusText(currentStep.status) }}</span>
            </div>
          </div>

          <div class="loader-container" :class="{
            active: currentStep.status === 'running',
            pending: currentStep.status === 'pending',
            completed: currentStep.status === 'completed'
          }">
            <div class="simple-progress">
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  :style="{ width: `${currentStep.progress || 0}%` }"
                  :key="`progress-${currentStepIndex}-${currentStep.status}`"
                ></div>
              </div>
            </div>

          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="progress-actions">
        <button 
          v-if="canCancel" 
          @click="cancelWorkflow" 
          class="btn-cancel"
          :disabled="isCompleted"
        >
          取消
        </button>
        <button 
          v-if="isCompleted" 
          @click="closeModal" 
          class="btn-close"
        >
          关闭
        </button>
      </div>

      <!-- 详细日志 -->
      <div class="log-section" v-if="showLogs">
        <div class="log-header">
          <span>详细日志</span>
          <button @click="toggleLogs" class="btn-toggle">
            {{ showLogs ? '隐藏' : '显示' }}
          </button>
        </div>
        <div class="log-content">
          <div 
            v-for="(log, index) in logs" 
            :key="index"
            class="log-item"
            :class="log.level"
          >
            <span class="log-time">{{ formatTime(log.timestamp) }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface WorkflowStep {
  title: string
  description: string
  status: 'pending' | 'running' | 'completed' | 'error'
  progress?: number
  message?: string
  duration?: number
  startTime?: number
}

interface LogEntry {
  timestamp: number
  level: 'info' | 'success' | 'warning' | 'error'
  message: string
}

const props = defineProps<{
  visible: boolean
  canCancel?: boolean
  selectedSteps?: string[]
}>()

const emit = defineEmits<{
  cancel: []
  close: []
}>()

// 响应式数据
const overallProgress = ref(0)
const currentStepIndex = ref(0)
const showLogs = ref(false)
const logs = ref<LogEntry[]>([])

// 所有可能的步骤定义
const allStepsDefinition = {
  saveAccount: {
    title: '📦 保存原账户',
    description: '备份当前登录的账户信息',
    status: 'pending' as const
  },
  backupSettings: {
    title: '⚙️ 备份设置',
    description: '保存Cursor配置和扩展',
    status: 'pending' as const
  },
  backupSession: {
    title: '💬 备份会话',
    description: '保存聊天记录和对话历史',
    status: 'pending' as const
  },
  resetMachine: {
    title: '🔄 重置机器码',
    description: '清除设备标识，准备新注册',
    status: 'pending' as const
  },
  autoRegister: {
    title: '📝 自动注册',
    description: '生成临时邮箱并注册新账户',
    status: 'pending' as const
  },
  initCursor: {
    title: '🧹 初始化Cursor',
    description: '清理配置文件和缓存',
    status: 'pending' as const
  },
  restoreSettings: {
    title: '⚙️ 恢复设置',
    description: '将原设置应用到新账户',
    status: 'pending' as const
  },
  restoreSession: {
    title: '💬 恢复会话',
    description: '将原会话恢复到新账户',
    status: 'pending' as const
  },
  restartCursor: {
    title: '🔄 重启Cursor',
    description: '刷新界面显示新账户',
    status: 'pending' as const
  }
}

// 默认步骤顺序
const defaultStepOrder = [
  'saveAccount', 'backupSettings', 'backupSession', 'resetMachine',
  'autoRegister', 'initCursor', 'restoreSettings', 'restoreSession', 'restartCursor'
]

// 根据选择的步骤生成步骤列表
const generateSteps = (selectedSteps?: string[]): WorkflowStep[] => {
  console.log('🔍 WorkflowProgress generateSteps 接收到的selectedSteps:', selectedSteps)

  const stepKeys = selectedSteps && selectedSteps.length > 0
    ? selectedSteps
    : defaultStepOrder

  console.log('🔍 WorkflowProgress 使用的stepKeys:', stepKeys)
  console.log('🔍 WorkflowProgress 生成的步骤数量:', stepKeys.length)

  return stepKeys.map(key => ({
    ...allStepsDefinition[key as keyof typeof allStepsDefinition]
  }))
}

// 工作流程步骤定义（根据选择动态生成）
const steps = ref<WorkflowStep[]>(generateSteps(props.selectedSteps))

// 计算属性
const currentStep = computed(() => {
  // 使用 currentStepIndex 来确定显示哪个步骤
  if (currentStepIndex.value >= 0 && currentStepIndex.value < steps.value.length) {
    return steps.value[currentStepIndex.value]
  }

  // 如果索引无效，返回第一个步骤
  return steps.value[0] || { title: '', description: '', status: 'pending' }
})

const isCompleted = computed(() => {
  return steps.value.every(step => step.status === 'completed')
})

// 监听完成状态，显示成功通知并自动关闭
watch(isCompleted, (newValue, oldValue) => {
  if (newValue && !oldValue) {
    // 所有步骤刚刚完成
    addLog('success', '🎉 恭喜！所有步骤已成功完成！')

    // 可选：显示浏览器通知
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('Cursor Pro', {
        body: '一键流程已成功完成！',
        icon: '/favicon.ico'
      })
    }

    // 延迟2秒后自动关闭弹窗
    setTimeout(() => {
      emit('close')
    }, 2000)
  }
})

// 方法
const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '等待中'
    case 'running': return '执行中'
    case 'completed': return '已完成'
    case 'error': return '失败'
    default: return '等待中'
  }
}

const formatDuration = (duration: number) => {
  if (duration < 1000) return `${duration}ms`
  return `${(duration / 1000).toFixed(1)}s`
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString()
}

const toggleLogs = () => {
  showLogs.value = !showLogs.value
}

const cancelWorkflow = () => {
  emit('cancel')
}

const closeModal = () => {
  emit('close')
}

const handleOverlayClick = () => {
  // 点击遮罩层时不关闭弹窗，防止误操作
  console.log('点击了遮罩层，但不关闭弹窗以防止误操作')
}

// 公开方法供父组件调用
const startStep = (stepIndex: number, message?: string) => {
  if (stepIndex < steps.value.length) {
    // 不要自动完成之前的步骤！只有在completeStep被调用时才标记为completed

    // 开始当前步骤
    steps.value[stepIndex].status = 'running'
    steps.value[stepIndex].progress = 0  // 开始时进度为0%
    steps.value[stepIndex].startTime = Date.now()
    if (message) {
      steps.value[stepIndex].message = message
    }

    currentStepIndex.value = stepIndex
    overallProgress.value = (stepIndex / steps.value.length) * 100

    console.log(`🔍 startStep - stepIndex: ${stepIndex}, steps.length: ${steps.value.length}, progress: ${overallProgress.value}%`)
    addLog('info', `开始执行: ${steps.value[stepIndex].title}`)
  }
}

const updateStepProgress = (stepIndex: number, progress: number, message?: string) => {
  if (stepIndex < steps.value.length) {
    steps.value[stepIndex].progress = progress
    if (message) {
      steps.value[stepIndex].message = message
    }
    
    // 更新总体进度
    const baseProgress = (stepIndex / steps.value.length) * 100
    const stepProgress = (progress / 100) * (100 / steps.value.length)
    overallProgress.value = baseProgress + stepProgress
  }
}

const completeStep = (stepIndex: number, message?: string, duration?: number) => {
  if (stepIndex < steps.value.length) {
    steps.value[stepIndex].status = 'completed'
    steps.value[stepIndex].progress = 100
    if (message) {
      steps.value[stepIndex].message = message
    }

    // 计算耗时
    if (steps.value[stepIndex].startTime) {
      steps.value[stepIndex].duration = Date.now() - steps.value[stepIndex].startTime
    } else if (duration) {
      steps.value[stepIndex].duration = duration
    }

    // 保持在当前步骤显示完成状态
    // 不自动切换到下一步，等待下一个 startStep 调用
    currentStepIndex.value = stepIndex

    overallProgress.value = ((stepIndex + 1) / steps.value.length) * 100

    console.log(`🔍 completeStep - stepIndex: ${stepIndex}, steps.length: ${steps.value.length}, progress: ${overallProgress.value}%`)
    addLog('success', `完成: ${steps.value[stepIndex].title}`)
  }
}

const errorStep = (stepIndex: number, message: string) => {
  if (stepIndex < steps.value.length) {
    steps.value[stepIndex].status = 'error'
    steps.value[stepIndex].message = message
    
    addLog('error', `错误: ${steps.value[stepIndex].title} - ${message}`)
  }
}

const addLog = (level: LogEntry['level'], message: string) => {
  logs.value.push({
    timestamp: Date.now(),
    level,
    message
  })
  
  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value = logs.value.slice(-100)
  }
}

const reset = () => {
  console.log('🔍 WorkflowProgress reset 被调用，props.selectedSteps:', props.selectedSteps)

  currentStepIndex.value = 0
  overallProgress.value = 0
  logs.value = []

  // 重新生成步骤列表
  steps.value = generateSteps(props.selectedSteps)

  console.log('🔍 WorkflowProgress reset 完成，steps.value.length:', steps.value.length)
}

// 监听 selectedSteps 变化
watch(() => props.selectedSteps, (newSelectedSteps) => {
  steps.value = generateSteps(newSelectedSteps)
  reset()
}, { deep: true })

// 暴露方法给父组件
defineExpose({
  startStep,
  updateStepProgress,
  completeStep,
  errorStep,
  addLog,
  reset
})
</script>

<style scoped>
.workflow-progress-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.workflow-progress-modal {
  background: #1a202c;
  border-radius: 12px;
  padding: 32px;
  width: 95%;
  max-width: 900px;
  max-height: 85vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
  border: 1px solid #2d3748;
}

/* 隐藏滚动条 */
.workflow-progress-modal::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

.progress-header {
  text-align: center;
  margin-bottom: 32px;
}

.progress-header h3 {
  color: #e5e5e5;
  margin: 0;
  font-size: 24px;
  font-weight: 700;
}

.overall-progress {
  margin-bottom: 24px;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  color: #e5e5e5;
  font-size: 14px;
}

.progress-bar {
  height: 8px;
  background: #2d3748;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  transition: width 0.3s ease;
}

.steps-progress {
  margin-bottom: 24px;
  display: flex;
  justify-content: center;
}

.step-progress-item {
  padding: 20px;
  background: #2d3748;
  border-radius: 8px;
  border: 1px solid #4a5568;
  width: 100%;
  max-width: 600px;
}

.step-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.step-name {
  font-weight: 700;
  color: #e5e5e5;
  font-size: 18px;
}

.step-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.step-counter {
  font-size: 14px;
  color: #a0aec0;
  font-weight: 500;
  background: #4a5568;
  padding: 4px 8px;
  border-radius: 8px;
  min-width: 40px;
  text-align: center;
}

.step-status {
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 16px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.step-status.pending {
  background: linear-gradient(45deg, #bdc3c7, #95a5a6);
  color: white;
}

.step-status.running {
  background: linear-gradient(45deg, #3498db, #2980b9);
  color: white;
}

.step-status.completed {
  background: linear-gradient(45deg, #2ecc71, #27ae60);
  color: white;
}

.step-status.error {
  background: linear-gradient(45deg, #e74c3c, #c0392b);
  color: white;
}

/* 进度条动画样式 */
.loader-container {
  --color-primary: #2ecc71;
  --color-neutral: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 100%;
  height: 100px;
}

.simple-progress {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.simple-progress .progress-bar {
  width: 100%;
  height: 40px;
  background: #fff;
  border: 3px solid var(--color-primary);
  border-radius: 20px;
  padding: 4px;
  position: relative;
}

.simple-progress .progress-fill {
  height: 100%;
  background: var(--color-primary);
  border-radius: 16px;
  max-width: 100%;
  transition: width 0.3s ease;
}

@keyframes fillProgress {
  from {
    width: 0%;
  }
  to {
    width: 100%;
  }
}

.progress-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
}

.btn-cancel, .btn-close {
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn-cancel {
  background: linear-gradient(45deg, #ff6b6b, #ee5a52);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-cancel:hover:not(:disabled) {
  background: linear-gradient(45deg, #ff5252, #e53935);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.btn-cancel:disabled {
  background: #4a5568;
  color: #a0aec0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-close {
  background: linear-gradient(45deg, #4ecdc4, #44a08d);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-close:hover {
  background: linear-gradient(45deg, #26d0ce, #2bb3a0);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
}

.log-section {
  margin-top: 32px;
  border-top: 1px solid #4a5568;
  padding-top: 24px;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.log-header span {
  color: #e5e5e5;
  font-weight: 600;
  font-size: 16px;
}

.btn-toggle {
  background: #4a5568;
  color: #e5e5e5;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.btn-toggle:hover {
  background: #718096;
}

.log-content {
  max-height: 200px;
  overflow-y: auto;
  background: #1a1a2e;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #4a5568;
}

.log-item {
  display: flex;
  gap: 12px;
  margin-bottom: 6px;
  font-size: 13px;
  font-family: 'Consolas', 'Monaco', monospace;
}

.log-time {
  color: #a0aec0;
  min-width: 80px;
}

.log-message {
  flex: 1;
}

.log-item.info .log-message {
  color: #e5e5e5;
}

.log-item.success .log-message {
  color: #2ecc71;
}

.log-item.warning .log-message {
  color: #f39c12;
}

.log-item.error .log-message {
  color: #e74c3c;
}

/* 森林露营动画样式 */
@keyframes stageBackground {
  0%, 10%, 90%, 100% {
    background-color: #00B6BB;
  }

  25%, 75% {
    background-color: #0094bd;
  }
}

@keyframes earthRotation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes sunrise {
  0%, 10%, 90%, 100% {
    box-shadow: 0 0 0 25px #ffeb3b, 0 0 0 40px #ff9800, 0 0 0 60px rgba(255, 87, 34, 0.6), 0 0 0 90px rgba(244, 67, 54, 0.3);
  }

  25%, 75% {
    box-shadow: 0 0 0 0 #ffeb3b, 0 0 0 0 #ff9800, 0 0 0 0 rgba(255, 87, 34, 0.6), 0 0 0 0 rgba(244, 67, 54, 0.3);
  }
}

@keyframes moonOrbit {
  25% {
    transform: rotate(-60deg);
  }

  50% {
    transform: rotate(-60deg);
  }

  75% {
    transform: rotate(-120deg);
  }

  0%, 100% {
    transform: rotate(-180deg);
  }
}

@keyframes nightTime {
  0%, 90% {
    opacity: 0;
  }

  50%, 75% {
    opacity: 1;
  }
}

@keyframes hotPan {
  0%, 90% {
    background-color: #74667e;
  }

  50%, 75% {
    background-color: #b2241c;
  }
}

@keyframes heat {
  0%, 90% {
    box-shadow: inset 0 0 0 0 rgba(255, 255, 255, 0.3);
  }

  50%, 75% {
    box-shadow: inset 0 -2px 0 0 white;
  }
}

@keyframes smoke {
  0%, 50%, 90%, 100% {
    opacity: 0;
  }

  50%, 75% {
    opacity: 0.7;
  }
}

@keyframes fire {
  0%, 90%, 100% {
    opacity: 0;
  }

  50%, 75% {
    opacity: 1;
  }
}

@keyframes treeShake {
  0% {
    transform: rotate(0deg);
  }

  25% {
    transform: rotate(-2deg);
  }

  40% {
    transform: rotate(4deg);
  }

  50% {
    transform: rotate(-4deg);
  }

  60% {
    transform: rotate(6deg);
  }

  75% {
    transform: rotate(-6deg);
  }

  100% {
    transform: rotate(0deg);
  }
}

@keyframes fireParticles {
  0% {
    height: 30%;
    opacity: 1;
    top: 75%;
  }

  25% {
    height: 25%;
    opacity: 0.8;
    top: 40%;
  }

  50% {
    height: 15%;
    opacity: 0.6;
    top: 20%;
  }

  75% {
    height: 10%;
    opacity: 0.3;
    top: 0;
  }

  100% {
    opacity: 0;
  }
}

@keyframes fireLines {
  0%, 25%, 75%, 100% {
    bottom: 0;
  }

  50% {
    bottom: 5%;
  }
}

.scene {
  display: flex;
  margin: 0 auto 80px auto;
  justify-content: center;
  align-items: flex-end;
  width: 400px;
  height: 300px;
  position: relative;
}

.forest {
  display: flex;
  width: 75%;
  height: 90%;
  position: relative;
}

.tree {
  display: block;
  width: 50%;
  position: absolute;
  bottom: 0;
  opacity: 0.4;
}

.tree .branch {
  width: 80%;
  height: 0;
  margin: 0 auto;
  padding-left: 40%;
  padding-bottom: 50%;
  overflow: hidden;
}

.tree .branch:before {
  content: "";
  display: block;
  width: 0;
  height: 0;
  margin-left: -600px;
  border-left: 600px solid transparent;
  border-right: 600px solid transparent;
  border-bottom: 950px solid #000;
}

.tree .branch.branch-top {
  transform-origin: 50% 100%;
  animation: treeShake 0.5s linear infinite;
}

.tree .branch.branch-middle {
  width: 90%;
  padding-left: 45%;
  padding-bottom: 65%;
  margin: 0 auto;
  margin-top: -25%;
}

.tree .branch.branch-bottom {
  width: 100%;
  padding-left: 50%;
  padding-bottom: 80%;
  margin: 0 auto;
  margin-top: -40%;
}

.tree1 {
  width: 31%;
}

.tree1 .branch-top {
  transition-delay: 0.3s;
}

.tree2 {
  width: 39%;
  left: 9%;
}

.tree2 .branch-top {
  transition-delay: 0.4s;
}

.tree3 {
  width: 32%;
  left: 24%;
}

.tree3 .branch-top {
  transition-delay: 0.5s;
}

.tree4 {
  width: 37%;
  left: 34%;
}

.tree4 .branch-top {
  transition-delay: 0.6s;
}

.tree5 {
  width: 44%;
  left: 44%;
}

.tree5 .branch-top {
  transition-delay: 0.7s;
}

.tree6 {
  width: 34%;
  left: 61%;
}

.tree6 .branch-top {
  transition-delay: 0.2s;
}

.tree7 {
  width: 24%;
  left: 76%;
}

.tree7 .branch-top {
  transition-delay: 0.1s;
}

.tent {
  width: 60%;
  height: 25%;
  position: absolute;
  bottom: -0.5%;
  right: 15%;
  z-index: 1;
  text-align: right;
}

.roof {
  display: inline-block;
  width: 45%;
  height: 100%;
  margin-right: 10%;
  position: relative;
  z-index: 1;
  border-top: 4px solid #4D4454;
  border-right: 4px solid #4D4454;
  border-left: 4px solid #4D4454;
  border-top-right-radius: 6px;
  transform: skew(30deg);
  box-shadow: inset -3px 3px 0px 0px #F7B563;
  background: #f6d484;
}

.roof:before {
  content: "";
  width: 70%;
  height: 70%;
  position: absolute;
  top: 15%;
  left: 15%;
  z-index: 0;
  border-radius: 10%;
  background-color: #E78C20;
}

.roof:after {
  content: "";
  height: 75%;
  width: 100%;
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: 1;
  background: linear-gradient(to bottom, rgba(231, 140, 32, 0.4) 0%, rgba(231, 140, 32, 0.4) 64%, rgba(231, 140, 32, 0.8) 65%, rgba(231, 140, 32, 0.8) 100%);
}

.roof-border-left {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  width: 1%;
  height: 125%;
  position: absolute;
  top: 0;
  left: 35.7%;
  z-index: 1;
  transform-origin: 50% 0%;
  transform: rotate(35deg);
}

.roof-border-left .roof-border {
  display: block;
  width: 100%;
  border-radius: 2px;
  border: 2px solid #4D4454;
}

.roof-border-left .roof-border1 {
  height: 40%;
}

.roof-border-left .roof-border2 {
  height: 10%;
}

.roof-border-left .roof-border3 {
  height: 40%;
}

.door {
  width: 55px;
  height: 92px;
  position: absolute;
  bottom: 2%;
  overflow: hidden;
  z-index: 0;
  transform-origin: 0 105%;
}

.left-door {
  transform: rotate(35deg);
  position: absolute;
  left: 13.5%;
  bottom: -3%;
  z-index: 0;
}

.left-door .left-door-inner {
  width: 100%;
  height: 100%;
  transform-origin: 0 105%;
  transform: rotate(-35deg);
  position: absolute;
  top: 0;
  overflow: hidden;
  background-color: #EDDDC2;
}

.left-door .left-door-inner:before {
  content: "";
  width: 15%;
  height: 100%;
  position: absolute;
  top: 0;
  right: 0;
  background: repeating-linear-gradient(#D4BC8B, #D4BC8B 4%, #E0D2A8 5%, #E0D2A8 10%);
}

.left-door .left-door-inner:after {
  content: "";
  width: 50%;
  height: 100%;
  position: absolute;
  top: 15%;
  left: 10%;
  transform: rotate(25deg);
  background-color: #fff;
}

.right-door {
  height: 89px;
  right: 21%;
  transform-origin: 0 105%;
  transform: rotate(-30deg) scaleX(-1);
  position: absolute;
  bottom: -3%;
  z-index: 0;
}

.right-door .right-door-inner {
  width: 100%;
  height: 100%;
  transform-origin: 0 120%;
  transform: rotate(-30deg);
  position: absolute;
  bottom: 0px;
  overflow: hidden;
  background-color: #EFE7CF;
}

.right-door .right-door-inner:before {
  content: "";
  width: 50%;
  height: 100%;
  position: absolute;
  top: 15%;
  right: -28%;
  z-index: 1;
  transform: rotate(15deg);
  background-color: #524A5A;
}

.right-door .right-door-inner:after {
  content: "";
  width: 50%;
  height: 100%;
  position: absolute;
  top: 15%;
  right: -20%;
  transform: rotate(20deg);
  background-color: #fff;
}

.floor {
  width: 80%;
  position: absolute;
  right: 10%;
  bottom: 0;
  z-index: 1;
}

.floor .ground {
  position: absolute;
  border-radius: 2px;
  border: 2px solid #4D4454;
}

.floor .ground.ground1 {
  width: 65%;
  left: 0;
}

.floor .ground.ground2 {
  width: 30%;
  right: 0;
}

.fireplace {
  display: block;
  width: 24%;
  height: 20%;
  position: absolute;
  left: 5%;
}

.fireplace:before {
  content: "";
  display: block;
  width: 8%;
  position: absolute;
  bottom: -4px;
  left: 2%;
  border-radius: 2px;
  border: 2px solid #4D4454;
  background: #4D4454;
}

.fireplace .support {
  display: block;
  height: 105%;
  width: 2px;
  position: absolute;
  bottom: -5%;
  left: 10%;
  border: 2px solid #4D4454;
}

.fireplace .support:before {
  content: "";
  width: 100%;
  height: 15%;
  position: absolute;
  top: -18%;
  left: -4px;
  border-radius: 2px;
  border: 2px solid #4D4454;
  transform-origin: 100% 100%;
  transform: rotate(45deg);
}

.fireplace .support:after {
  content: "";
  width: 100%;
  height: 15%;
  position: absolute;
  top: -18%;
  left: 0px;
  border-radius: 2px;
  border: 2px solid #4D4454;
  transform-origin: 0 100%;
  transform: rotate(-45deg);
}

.fireplace .support:nth-child(1) {
  left: 85%;
}

.fireplace .bar {
  width: 100%;
  height: 2px;
  border-radius: 2px;
  border: 2px solid #4D4454;
}

.fireplace .hanger {
  display: block;
  width: 2px;
  height: 25%;
  margin-left: -4px;
  position: absolute;
  left: 50%;
  border: 2px solid #4D4454;
}

.fireplace .pan {
  display: block;
  width: 25%;
  height: 50%;
  border-radius: 50%;
  border: 4px solid #4D4454;
  position: absolute;
  top: 25%;
  left: 35%;
  overflow: hidden;
  animation: heat 5s linear infinite;
}

.fireplace .pan:before {
  content: "";
  display: block;
  height: 53%;
  width: 100%;
  position: absolute;
  bottom: 0;
  z-index: -1;
  border-top: 4px solid #4D4454;
  background-color: #74667e;
  animation: hotPan 5s linear infinite;
}

.fireplace .smoke {
  display: block;
  width: 20%;
  height: 25%;
  position: absolute;
  top: 25%;
  left: 37%;
  background-color: white;
  filter: blur(5px);
  animation: smoke 5s linear infinite;
}

.fireplace .fire {
  display: block;
  width: 25%;
  height: 120%;
  position: absolute;
  bottom: 0;
  left: 33%;
  z-index: 1;
  animation: fire 5s linear infinite;
}

.fireplace .fire:before {
  content: "";
  display: block;
  width: 100%;
  height: 2px;
  position: absolute;
  bottom: -4px;
  z-index: 1;
  border-radius: 2px;
  border: 1px solid #efb54a;
  background-color: #efb54a;
}

.fireplace .fire .line {
  display: block;
  width: 2px;
  height: 100%;
  position: absolute;
  bottom: 0;
  animation: fireLines 1s linear infinite;
}

.fireplace .fire .line2 {
  left: 50%;
  margin-left: -1px;
  animation-delay: 0.3s;
}

.fireplace .fire .line3 {
  right: 0;
  animation-delay: 0.5s;
}

.fireplace .fire .line .particle {
  height: 10%;
  position: absolute;
  top: 100%;
  z-index: 1;
  border-radius: 2px;
  border: 2px solid #efb54a;
  animation: fireParticles 0.5s linear infinite;
}

.fireplace .fire .line .particle1 {
  animation-delay: 0.1s;
}

.fireplace .fire .line .particle2 {
  animation-delay: 0.3s;
}

.fireplace .fire .line .particle3 {
  animation-delay: 0.6s;
}

.fireplace .fire .line .particle4 {
  animation-delay: 0.9s;
}

.time-wrapper {
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  overflow: hidden;
}

.time {
  display: block;
  width: 100%;
  height: 200%;
  position: absolute;
  transform-origin: 50% 50%;
  transform: rotate(270deg);
  animation: earthRotation 5s linear infinite;
}

.time .day {
  display: block;
  width: 20px;
  height: 20px;
  position: absolute;
  top: 20%;
  left: 40%;
  border-radius: 50%;
  box-shadow: 0 0 0 25px #ffeb3b, 0 0 0 40px #ff9800, 0 0 0 60px rgba(255, 87, 34, 0.6), 0 0 0 90px rgba(244, 67, 54, 0.3);
  animation: sunrise 5s ease-in-out infinite;
  background: radial-gradient(circle, #fff9c4 0%, #ffeb3b 30%, #ff9800 70%, #ff5722 100%);
}

.time .night {
  animation: nightTime 5s ease-in-out infinite;
}

.time .night .star {
  display: block;
  width: 4px;
  height: 4px;
  position: absolute;
  bottom: 10%;
  border-radius: 50%;
  background-color: #fff;
}

.time .night .star-big {
  width: 6px;
  height: 6px;
}

.time .night .star1 {
  right: 23%;
  bottom: 25%;
}

.time .night .star2 {
  right: 35%;
  bottom: 18%;
}

.time .night .star3 {
  right: 47%;
  bottom: 25%;
}

.time .night .star4 {
  right: 22%;
  bottom: 20%;
}

.time .night .star5 {
  right: 18%;
  bottom: 30%;
}

.time .night .star6 {
  right: 60%;
  bottom: 20%;
}

.time .night .star7 {
  right: 70%;
  bottom: 23%;
}

.time .night .moon {
  display: block;
  width: 25px;
  height: 25px;
  position: absolute;
  bottom: 22%;
  right: 33%;
  border-radius: 50%;
  transform: rotate(-60deg);
  box-shadow: 9px 9px 3px 0 white;
  filter: blur(1px);
  animation: moonOrbit 5s ease-in-out infinite;
}

.time .night .moon:before {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  bottom: -9px;
  left: 9px;
  border-radius: 50%;
  box-shadow: 0 0 0 5px rgba(255, 255, 255, 0.05), 0 0 0 15px rgba(255, 255, 255, 0.05), 0 0 0 25px rgba(255, 255, 255, 0.05), 0 0 0 35px rgba(255, 255, 255, 0.05);
  background-color: rgba(255, 255, 255, 0.2);
}
</style>
