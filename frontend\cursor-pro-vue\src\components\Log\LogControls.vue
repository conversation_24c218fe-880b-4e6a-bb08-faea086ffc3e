<template>
  <div class="log-controls">
    <!-- 左侧控制按钮 -->
    <div class="controls-left">
      <button 
        class="control-btn clear-btn"
        @click="handleClear"
        title="清空所有日志"
      >
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="3 6 5 6 21 6"></polyline>
          <path d="m19 6-2 14a2 2 0 0 1-2 2H9a2 2 0 0 1-2-2L5 6"></path>
          <path d="m10 11 0 6"></path>
          <path d="m14 11 0 6"></path>
        </svg>
        清空日志
      </button>
      
      <button 
        class="control-btn export-btn"
        @click="handleExport"
        title="导出日志到文件"
      >
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
          <polyline points="7 10 12 15 17 10"></polyline>
          <line x1="12" y1="15" x2="12" y2="3"></line>
        </svg>
        导出日志
      </button>

      <button 
        class="control-btn filter-btn"
        :class="{ active: showFilters }"
        @click="toggleFilters"
        title="显示/隐藏过滤器"
      >
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
        </svg>
        过滤器
      </button>
    </div>

    <!-- 中间统计信息 -->
    <div class="controls-center">
      <div class="log-stats">
        <span class="stat-item total">
          <span class="stat-icon">📊</span>
          总计: {{ totalCount }}
        </span>
        
        <span v-if="infoCount > 0" class="stat-item info">
          <span class="stat-icon">ℹ️</span>
          信息: {{ infoCount }}
        </span>
        
        <span v-if="successCount > 0" class="stat-item success">
          <span class="stat-icon">✅</span>
          成功: {{ successCount }}
        </span>
        
        <span v-if="warningCount > 0" class="stat-item warning">
          <span class="stat-icon">⚠️</span>
          警告: {{ warningCount }}
        </span>
        
        <span v-if="errorCount > 0" class="stat-item error">
          <span class="stat-icon">❌</span>
          错误: {{ errorCount }}
        </span>
      </div>
    </div>

    <!-- 右侧设置 -->
    <div class="controls-right">
      <label class="auto-scroll-toggle">
        <input 
          type="checkbox" 
          v-model="autoScrollEnabled"
          @change="handleAutoScrollChange"
        />
        <span class="toggle-text">自动滚动</span>
      </label>

      <div class="max-logs-setting">
        <label for="max-logs">最大日志数:</label>
        <select 
          id="max-logs"
          v-model="maxLogsValue"
          @change="handleMaxLogsChange"
          class="max-logs-select"
        >
          <option value="100">100</option>
          <option value="500">500</option>
          <option value="1000">1000</option>
          <option value="2000">2000</option>
          <option value="5000">5000</option>
        </select>
      </div>
    </div>

    <!-- 过滤器面板 -->
    <div v-if="showFilters" class="filters-panel">
      <div class="filter-group">
        <label>日志级别:</label>
        <div class="level-filters">
          <label class="level-filter">
            <input type="checkbox" v-model="levelFilters.info" @change="handleFilterChange" />
            <span class="level-label info">信息</span>
          </label>
          <label class="level-filter">
            <input type="checkbox" v-model="levelFilters.success" @change="handleFilterChange" />
            <span class="level-label success">成功</span>
          </label>
          <label class="level-filter">
            <input type="checkbox" v-model="levelFilters.warning" @change="handleFilterChange" />
            <span class="level-label warning">警告</span>
          </label>
          <label class="level-filter">
            <input type="checkbox" v-model="levelFilters.error" @change="handleFilterChange" />
            <span class="level-label error">错误</span>
          </label>
        </div>
      </div>

      <div class="filter-group">
        <label for="source-filter">来源过滤:</label>
        <input 
          id="source-filter"
          type="text" 
          v-model="sourceFilter"
          @input="handleFilterChange"
          placeholder="输入来源名称..."
          class="source-filter-input"
        />
      </div>

      <div class="filter-group">
        <label for="content-filter">内容搜索:</label>
        <input 
          id="content-filter"
          type="text" 
          v-model="contentFilter"
          @input="handleFilterChange"
          placeholder="搜索日志内容..."
          class="content-filter-input"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { LogEntry } from '@/stores/logs'

interface LogControlsProps {
  logs: LogEntry[]
  autoScroll?: boolean
  maxLogs?: number
}

interface LogControlsEvents {
  'clear': []
  'export': []
  'auto-scroll-change': [enabled: boolean]
  'max-logs-change': [maxLogs: number]
  'filter-change': [filters: LogFilters]
}

interface LogFilters {
  levels: {
    info: boolean
    success: boolean
    warning: boolean
    error: boolean
  }
  source: string
  content: string
}

const props = withDefaults(defineProps<LogControlsProps>(), {
  autoScroll: true,
  maxLogs: 1000
})

const emit = defineEmits<LogControlsEvents>()

// 响应式数据
const showFilters = ref(false)
const autoScrollEnabled = ref(props.autoScroll)
const maxLogsValue = ref(props.maxLogs)

// 过滤器状态
const levelFilters = ref({
  info: true,
  success: true,
  warning: true,
  error: true
})
const sourceFilter = ref('')
const contentFilter = ref('')

// 计算属性
const totalCount = computed(() => props.logs.length)

const infoCount = computed(() => 
  props.logs.filter(log => log.level === 'info').length
)

const successCount = computed(() => 
  props.logs.filter(log => log.level === 'success').length
)

const warningCount = computed(() => 
  props.logs.filter(log => log.level === 'warning').length
)

const errorCount = computed(() => 
  props.logs.filter(log => log.level === 'error').length
)

// 方法
const handleClear = () => {
  emit('clear')
}

const handleExport = () => {
  emit('export')
}

const toggleFilters = () => {
  showFilters.value = !showFilters.value
}

const handleAutoScrollChange = () => {
  emit('auto-scroll-change', autoScrollEnabled.value)
}

const handleMaxLogsChange = () => {
  emit('max-logs-change', maxLogsValue.value)
}

const handleFilterChange = () => {
  const filters: LogFilters = {
    levels: { ...levelFilters.value },
    source: sourceFilter.value,
    content: contentFilter.value
  }
  emit('filter-change', filters)
}

// 监听props变化
watch(() => props.autoScroll, (newValue) => {
  autoScrollEnabled.value = newValue
})

watch(() => props.maxLogs, (newValue) => {
  maxLogsValue.value = newValue
})
</script>

<style scoped>
.log-controls {
  background: #23272e;
  border: 1px solid #2d323a;
  border-radius: 8px;
  padding: 12px;
  font-family: 'Fira Mono', 'Consolas', monospace;
  font-size: 0.9rem;
}

.log-controls > div:first-child {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  margin-bottom: 0;
}

.controls-left,
.controls-center,
.controls-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.controls-center {
  flex: 1;
  justify-content: center;
}

/* 控制按钮 */
.control-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: #374151;
  color: #e5e5e5;
  border: 1px solid #4b5563;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.8rem;
  font-family: inherit;
  white-space: nowrap;
}

.control-btn:hover {
  background: #4b5563;
  border-color: #6b7280;
  transform: translateY(-1px);
}

.control-btn.active {
  background: #60a5fa;
  border-color: #3b82f6;
  color: #ffffff;
}

.clear-btn:hover {
  background: #dc2626;
  border-color: #ef4444;
}

.export-btn:hover {
  background: #059669;
  border-color: #10b981;
}

/* 统计信息 */
.log-stats {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.8rem;
  font-weight: 600;
}

.stat-icon {
  font-size: 0.9rem;
}

.stat-item.total { color: #94a3b8; }
.stat-item.info { color: #60a5fa; }
.stat-item.success { color: #10b981; }
.stat-item.warning { color: #f59e0b; }
.stat-item.error { color: #f87171; }

/* 右侧设置 */
.auto-scroll-toggle {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #94a3b8;
  font-size: 0.8rem;
  cursor: pointer;
  white-space: nowrap;
}

.auto-scroll-toggle input[type="checkbox"] {
  margin: 0;
  cursor: pointer;
}

.max-logs-setting {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #94a3b8;
  font-size: 0.8rem;
  white-space: nowrap;
}

.max-logs-select {
  background: #374151;
  color: #e5e5e5;
  border: 1px solid #4b5563;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 0.8rem;
  font-family: inherit;
  cursor: pointer;
}

/* 过滤器面板 */
.filters-panel {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #2d323a;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.filter-group label {
  color: #94a3b8;
  font-size: 0.8rem;
  font-weight: 600;
}

.level-filters {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.level-filter {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  font-size: 0.8rem;
}

.level-label {
  font-weight: 500;
}

.level-label.info { color: #60a5fa; }
.level-label.success { color: #10b981; }
.level-label.warning { color: #f59e0b; }
.level-label.error { color: #f87171; }

.source-filter-input,
.content-filter-input {
  background: #374151;
  color: #e5e5e5;
  border: 1px solid #4b5563;
  border-radius: 4px;
  padding: 6px 8px;
  font-size: 0.8rem;
  font-family: inherit;
  width: 100%;
}

.source-filter-input:focus,
.content-filter-input:focus {
  outline: none;
  border-color: #60a5fa;
  box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .log-controls > div:first-child {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .controls-left,
  .controls-center,
  .controls-right {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .filters-panel {
    grid-template-columns: 1fr;
  }
  
  .level-filters {
    justify-content: center;
  }
}
</style>
