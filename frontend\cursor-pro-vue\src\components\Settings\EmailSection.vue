<template>
  <FormCard title="邮箱配置">
    <div class="form-fields">
      <div class="form-field">
        <label class="field-label">邮箱地址</label>
        <input 
          class="field-input" 
          type="email" 
          :value="email" 
          @input="updateEmail"
          placeholder="请输入邮箱地址" 
        />
      </div>
      
      <div class="form-field">
        <label class="field-label">临时邮箱域名</label>
        <input 
          class="field-input" 
          type="text" 
          :value="tempmailDomain" 
          @input="updateTempmailDomain"
          placeholder="请输入临时邮箱域名" 
        />
      </div>
      
      <div class="form-field">
        <label class="field-label">TempMail PIN码</label>
        <input 
          class="field-input" 
          type="text" 
          :value="tempmailEpin" 
          @input="updateTempmailEpin"
          placeholder="请输入PIN码" 
        />
      </div>
    </div>
  </FormCard>
</template>

<script setup lang="ts">
import FormCard from '../Common/FormCard.vue'

// Props定义
interface Props {
  email: string
  tempmailDomain: string
  tempmailEpin: string
}

// Emits定义
interface Emits {
  (e: 'update:email', value: string): void
  (e: 'update:tempmailDomain', value: string): void
  (e: 'update:tempmailEpin', value: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 更新方法
const updateEmail = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('update:email', target.value)
}

const updateTempmailDomain = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('update:tempmailDomain', target.value)
}

const updateTempmailEpin = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('update:tempmailEpin', target.value)
}
</script>

<style scoped>
.form-fields {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field-label {
  color: #006aaaff;
  font-weight: bold;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  font-size: 1.15em;
  text-shadow: 0em 0.1em 0em var(--color2);
}

.field-input {
  background-color: white;
  border: none;
  border-right: 5px solid #ff7c90ff;
  border-bottom: 5px solid #ff7c90ff;
  border-bottom-right-radius: 25em;
  height: 2em;
  width: 90%;
  transition: all 0.5s ease;
  color: #ff7c90ff;
  font-weight: bold;
  padding-left: 0.5em;
  font-size: 1.05em;
  box-shadow: 1em 0em 0em;
  outline: none;
}

.field-input:hover {
  width: 100%;
  background-color: white;
  box-shadow: 0em 0em 0em;
}

.field-input:focus {
  width: 100%;
  background-color: white;
  box-shadow: 0em 0em 0em;
  border-color: #006aaaff;
}
</style>
