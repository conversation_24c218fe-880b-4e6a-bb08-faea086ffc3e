<template>
  <BaseModal
    :visible="visible"
    title="验证码信息"
    width="600px"
    max-width="90vw"
    @close="handleClose"
  >
    <!-- 验证码内容显示区域 -->
    <div class="verification-modal-content">
      <div class="verification-content">
        <pre class="verification-text" v-html="formattedVerificationCode"></pre>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <button
        class="card__button card__button--confirm"
        @click="handleCopyCode"
        :disabled="!data.code"
      >
        复制验证码
      </button>
      <button
        class="card__button card__button--cancel"
        @click="handleClose"
      >
        关闭
      </button>
    </template>
  </BaseModal>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import BaseModal from './BaseModal.vue'

interface VerificationData {
  code?: string
  email?: string
  subject?: string
  time?: string
}

interface VerificationCodeModalProps {
  visible: boolean
  data: VerificationData
}

interface VerificationCodeModalEvents {
  'close': []
  'update:visible': [visible: boolean]
}

const props = defineProps<VerificationCodeModalProps>()
const emit = defineEmits<VerificationCodeModalEvents>()

// 格式化验证码信息
const formattedVerificationCode = computed(() => {
  if (!props.data) return ''
  
  let formatted = ''
  formatted += '【验证码信息】\n'
  formatted += '-'.repeat(40) + '\n'
  
  if (props.data.code) {
    formatted += `[验证码]\n`
    formatted += `  code = ${props.data.code}\n`
  }
  
  if (props.data.email) {
    formatted += `[邮箱地址]\n`
    formatted += `  email = ${props.data.email}\n`
  }
  
  if (props.data.subject) {
    formatted += `[邮件主题]\n`
    formatted += `  subject = ${props.data.subject}\n`
  }
  
  if (props.data.time) {
    formatted += `[接收时间]\n`
    formatted += `  time = ${props.data.time}\n`
  }
  
  formatted += '-'.repeat(40) + '\n'
  formatted += `获取时间: ${new Date().toLocaleString('zh-CN')}\n`
  
  // 配置内容高亮美化（与ConfigModal一致）
  formatted = formatted.replace(/^【验证码信息】/m, '<span style="color:#10b981;font-weight:bold;">【验证码信息】</span>')
  formatted = formatted.replace(/^[-]{10,}/gm, '<span style="color:#60a5fa;">$&</span>')
  formatted = formatted.replace(/^\[.*?\]/gm, match => `<span style='color:#60a5fa;'>${match}</span>`)
  formatted = formatted.replace(/^\s*([\w_]+)\s*=\s*([^\n]+)$/gm, (m, k, v) => {
    return `<span style='color:#b5f4a5;'>${k}</span> = <span style='color:#fff;'>${v}</span>`
  })
  
  return formatted.replace(/\n/g, '<br>')
})

// 复制验证码
const handleCopyCode = async () => {
  if (!props.data.code) return

  try {
    await navigator.clipboard.writeText(props.data.code)
    console.log('验证码复制成功')
  } catch (error) {
    console.error('复制失败:', error)
  }
}

// 关闭弹窗
const handleClose = () => {
  emit('close')
  emit('update:visible', false)
}
</script>

<style scoped>
.verification-modal-content {
  min-height: 200px;
  max-height: 500px;
  overflow-y: auto;
}

.verification-content {
  padding: 16px 0;
}

.verification-text {
  background: #181c20;
  border: 1px solid #2d323a;
  border-radius: 8px;
  padding: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  font-size: 0.9rem;
  line-height: 1.5;
  color: #e5e5e5;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  max-height: 400px;
  overflow-y: auto;
}

.verification-text::-webkit-scrollbar {
  width: 6px;
}

.verification-text::-webkit-scrollbar-track {
  background: transparent;
}

.verification-text::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 3px;
}

.verification-text::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* 按钮样式 */
.card__button {
  border: 3px solid #000;
  background: #000;
  color: #fff;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: bold;
  text-transform: uppercase;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s;
  min-width: 100px;
}

.card__button--cancel {
  background: #dc2626;
  border-color: #dc2626;
}

.card__button--cancel::before {
  content: "Sure?";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 105%;
  background-color: #f87171;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.card__button--cancel:hover::before {
  transform: translateY(0);
}

.card__button--confirm {
  background: #059669;
  border-color: #059669;
}

.card__button--confirm::before {
  content: "Copy!";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 105%;
  background-color: #34d399;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.card__button--confirm:hover::before {
  transform: translateY(0);
}

.card__button:active {
  transform: scale(0.95);
}
</style>
