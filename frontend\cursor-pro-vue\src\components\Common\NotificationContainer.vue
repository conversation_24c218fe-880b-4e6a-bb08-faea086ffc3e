<template>
  <Teleport to="body">
    <div class="notification-container">
      <TransitionGroup
        name="notification"
        tag="div"
        class="notification-list"
      >
        <StatusAlert
          v-for="notification in notifications"
          :key="notification.id"
          :type="notification.type"
          :title="notification.title"
          :message="notification.message"
          :closable="notification.closable"
          @close="removeNotification(notification.id)"
        />
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { onMounted, watch } from 'vue'
import { notification } from '@/composables/useNotification'
import StatusAlert from './StatusAlert.vue'

const { notifications, removeNotification } = notification

onMounted(() => {
  console.log('🔔 NotificationContainer 已挂载')
  console.log('notifications:', notifications.value)
})

watch(notifications, (newNotifications) => {
  console.log('🔔 通知列表更新:', newNotifications)
}, { deep: true })
</script>

<style scoped>
.notification-container {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  max-width: 400px;
  width: 100%;
  pointer-events: none;
}

.notification-container > div {
  pointer-events: auto;
}

.notification-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 动画效果 */
.notification-enter-active {
  animation: slideDown 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.notification-leave-active {
  animation: slideUp 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.notification-move {
  transition: transform 0.3s ease;
}

@keyframes slideDown {
  0% {
    opacity: 0;
    transform: translateY(-120px) scale(0.8);
  }
  50% {
    opacity: 0.8;
    transform: translateY(8px) scale(1.05);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideUp {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-100px) scale(0.9);
  }
}

/* 响应式设计 */
@media (max-width: 640px) {
  .notification-container {
    top: 10px;
    left: 10px;
    right: 10px;
    transform: none;
    max-width: none;
  }
}
</style>
