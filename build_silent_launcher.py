#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
构建Cursor Pro静默启动器exe
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def create_silent_launcher_spec():
    """创建静默启动器的spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['cursor_pro_launcher_silent.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='Cursor Pro',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 无控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='cursor-pro.ico' if os.path.exists('cursor-pro.ico') else None,
)
'''
    
    with open('cursor-pro-silent-launcher.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 创建了静默启动器spec文件")

def build_silent_launcher():
    """构建静默启动器exe"""
    print("🚀 开始构建Cursor Pro静默启动器...")
    
    # 创建spec文件
    create_silent_launcher_spec()
    
    # 清理之前的构建
    if os.path.exists('dist'):
        shutil.rmtree('dist')
        print("🧹 清理旧的构建文件")
    
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # 执行构建
    try:
        print("📦 正在打包静默启动器...")
        subprocess.check_call([
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            "cursor-pro-silent-launcher.spec"
        ])
        
        launcher_exe = "dist/Cursor Pro.exe"
        if os.path.exists(launcher_exe):
            print("✅ 静默启动器打包成功!")
            print(f"📁 可执行文件位置: {os.path.abspath(launcher_exe)}")
            return True
        else:
            print("❌ 打包失败，未找到可执行文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包过程中出错: {e}")
        return False

def copy_to_release():
    """复制静默启动器到发布目录"""
    launcher_exe = "dist/Cursor Pro.exe"
    if not os.path.exists(launcher_exe):
        print("❌ 静默启动器exe不存在")
        return False
    
    # 创建新的发布目录
    release_dir = "release_cursor_pro_final_v4"
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    
    os.makedirs(release_dir)
    
    # 复制静默启动器（重命名为主程序）
    main_exe_path = os.path.join(release_dir, "Cursor Pro.exe")
    shutil.copy2(launcher_exe, main_exe_path)
    print(f"✅ 复制静默启动器到: {main_exe_path}")
    
    # 复制原来的前端程序（重命名）
    old_frontend = "release_cursor_pro_v3/Cursor Pro.exe"
    if os.path.exists(old_frontend):
        frontend_path = os.path.join(release_dir, "Cursor Pro Frontend.exe")
        shutil.copy2(old_frontend, frontend_path)
        print(f"✅ 复制前端程序到: {frontend_path}")
    
    # 复制后端目录
    old_backend_dir = "release_cursor_pro_v3/resources"
    if os.path.exists(old_backend_dir):
        new_backend_dir = os.path.join(release_dir, "resources")
        shutil.copytree(old_backend_dir, new_backend_dir)
        print(f"✅ 复制后端目录到: {new_backend_dir}")
    
    # 复制其他必要文件
    other_files = [
        "release_cursor_pro_v3/LICENSE.electron.txt",
        "release_cursor_pro_v3/LICENSES.chromium.html",
        "release_cursor_pro_v3/chrome_100_percent.pak",
        "release_cursor_pro_v3/chrome_200_percent.pak",
        "release_cursor_pro_v3/d3dcompiler_47.dll",
        "release_cursor_pro_v3/ffmpeg.dll",
        "release_cursor_pro_v3/icudtl.dat",
        "release_cursor_pro_v3/libEGL.dll",
        "release_cursor_pro_v3/libGLESv2.dll",
        "release_cursor_pro_v3/resources.pak",
        "release_cursor_pro_v3/snapshot_blob.bin",
        "release_cursor_pro_v3/v8_context_snapshot.bin",
        "release_cursor_pro_v3/vk_swiftshader.dll",
        "release_cursor_pro_v3/vk_swiftshader_icd.json",
        "release_cursor_pro_v3/vulkan-1.dll"
    ]
    
    for file_path in other_files:
        if os.path.exists(file_path):
            filename = os.path.basename(file_path)
            target_path = os.path.join(release_dir, filename)
            shutil.copy2(file_path, target_path)
    
    # 复制locales目录
    old_locales = "release_cursor_pro_v3/locales"
    if os.path.exists(old_locales):
        new_locales = os.path.join(release_dir, "locales")
        shutil.copytree(old_locales, new_locales)
        print(f"✅ 复制locales目录到: {new_locales}")
    
    # 创建说明文件
    readme_content = """# Cursor Pro v4.0 - 静默启动版

## 版本信息
- 构建时间: 2025-07-31 21:00:00
- 版本: 4.0.0 静默启动版
- 架构: Windows x64
- 特点: 后台启动，无多余窗口

## 🎯 本版本特点
✅ 静默启动后端服务（无控制台窗口）
✅ 自动启动前端应用
✅ 一键启动，无多余界面
✅ 完美的用户体验

## 🚀 使用说明
1. 双击 "Cursor Pro.exe" 启动应用
2. 后端会在后台静默启动（无窗口）
3. 前端界面会自动显示
4. 享受完整的Cursor Pro功能

## 文件说明
- Cursor Pro.exe - 主启动器（静默启动前后端）
- Cursor Pro Frontend.exe - 原前端程序（备用）
- resources/ - 后端服务和资源文件

## 系统要求
- Windows 10/11 (x64)
- 无需安装Python或Node.js
- 无需额外配置

---
🎉 v4.0 静默启动版 - 完美的用户体验！
构建时间: 2025-07-31 21:00:00
"""
    
    readme_path = os.path.join(release_dir, "README.txt")
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✅ 创建了新的发布目录: {release_dir}")
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("🏗️  Cursor Pro - 静默启动器打包工具")
    print("=" * 50)
    
    if build_silent_launcher():
        print("\n🎉 静默启动器打包完成!")
        
        # 复制到发布目录
        if copy_to_release():
            print("📦 静默启动器已创建新的发布目录")
        
        print("\n🚀 使用方法:")
        print("1. 双击 'release_cursor_pro_final_v4/Cursor Pro.exe'")
        print("2. 后端静默启动（无窗口）")
        print("3. 前端自动显示")
        print("4. 完美体验!")
    else:
        print("\n❌ 静默启动器打包失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
