<template>
  <!-- 模态框遮罩 -->
  <div
    v-if="visible"
    class="modal-mask"
    @click="handleMaskClick"
  >
    <!-- 使用新的卡片样式 -->
    <div
      class="card base-modal-card"
      :style="{ width: width, maxWidth: maxWidth }"
      @click.stop
    >
      <span v-if="title" class="card__title">{{ title }}</span>

      <!-- 关闭按钮 -->
      <button
        v-if="closable"
        class="close-button"
        @click="handleClose"
        aria-label="关闭"
      >
        关闭
      </button>

      <!-- 模态框内容区域 -->
      <div class="card__content">
        <slot></slot>
      </div>

      <!-- 模态框底部 -->
      <div v-if="$slots.footer" class="card__form">
        <slot name="footer"></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface BaseModalProps {
  visible: boolean
  title?: string
  width?: string
  maxWidth?: string
  closable?: boolean
  maskClosable?: boolean
}

interface BaseModalEvents {
  'close': []
  'update:visible': [visible: boolean]
}

const props = withDefaults(defineProps<BaseModalProps>(), {
  width: '480px',
  maxWidth: '90vw',
  closable: true,
  maskClosable: true
})

const emit = defineEmits<BaseModalEvents>()

const handleClose = () => {
  emit('close')
  emit('update:visible', false)
}

const handleMaskClick = () => {
  if (props.maskClosable) {
    handleClose()
  }
}
</script>

<style scoped>
/* From Uiverse.io by 0xnihilism */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.card {
  width: 480px;
  padding: 20px;
  background: #fff;
  border: 6px solid #000;
  box-shadow: 12px 12px 0 #000;
  transition: transform 0.3s, box-shadow 0.3s;
  animation: slideIn 0.3s ease;
  position: relative;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.card:hover {
  transform: translate(-5px, -5px);
  box-shadow: 17px 17px 0 #000;
}

.card__title {
  font-size: 32px;
  font-weight: 900;
  color: #000;
  text-transform: uppercase;
  margin-bottom: 15px;
  display: block;
  position: relative;
  overflow: hidden;
}

.card__title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 90%;
  height: 3px;
  background-color: #000;
  transform: translateX(-100%);
  transition: transform 0.3s;
}

.card:hover .card__title::after {
  transform: translateX(0);
}

.close-button {
  position: absolute;
  top: 15px;
  right: 15px;
  background: #000;
  color: #fff;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.close-button:hover {
  background: #dc2626;
  transform: rotate(90deg);
}

.card__content {
  font-size: 16px;
  line-height: 1.4;
  color: #000;
  margin-bottom: 20px;
}

.card__form {
  display: flex;
  flex-direction: row;
  gap: 15px;
  justify-content: center;
}

@keyframes glitch {
  0% {
    transform: translate(2px, 2px);
  }
  25% {
    transform: translate(-2px, -2px);
  }
  50% {
    transform: translate(-2px, 2px);
  }
  75% {
    transform: translate(2px, -2px);
  }
  100% {
    transform: translate(2px, 2px);
  }
}

.glitch {
  animation: glitch 0.3s infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card {
    width: 90vw;
    max-width: 400px;
    margin: 20px;
  }

  .card__title {
    font-size: 24px;
  }

  .card__form {
    flex-direction: column;
  }
}
</style>
